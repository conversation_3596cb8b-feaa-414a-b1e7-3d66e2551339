/*!
 * @file
 * @brief Manages all the fault code.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include <string.h>
#include "FaultCode.h"
#include "LedController.h"
#include "DisplayUsart.h"
#include "InverterUsart.h"
#include "Driver_AdSample.h"
#include "Driver_Fan.h"
#include "Defrosting.h"
#include "Driver_DoorSwitch.h"
#include "DisplayInterface.h"
#include "FactoryMode.h"

#define U8_500MS_IN_100MS_COUNT (uint8_t)5

static uint8_t ary_FaultCodeByte[(uint8_t)eFCode_Max];
static uint8_t ary_FaultCodeByteBak[(uint8_t)eFCode_Max];
static uint8_t u8_FaultCodeNumber;
static DispFaultData_st st_DispFaultData;
static uint8_t u8_TimeLength = 0;
static uint8_t u8_TimeCount = 0;
static uint8_t u8_TimeCount100ms = 0;

// clang-format off
static const  DispFault_st ary_DispFault[] =
{ //  Fault Type            High Disp     Low Disp    Fault Byte Index              Bit Value 
    { FAULT_REF_SNR,            CON_DispR,    CON_Disp5,  (uint8_t)eFCode_IotByte0,     0x02 }, // 冷藏传感器故障 rS
    { FAULT_REF_VAR_SNR,        CON_DispU,    CON_Disp5,  (uint8_t)eFCode_IotByte1,     0x04 }, // 母婴室传感器故障 US
    { FAULT_FRZ_SNR,            CON_DispF,    CON_Disp5,  (uint8_t)eFCode_IotByte0,     0x04 }, // 冷冻传感器故障 FS
    { FAULT_DEF_SNR,            CON_DispF,    CON_DispD,  (uint8_t)eFCode_IotByte0,     0x10 }, // 冷冻化霜传感器故障 Fd
    { FAULT_ROOM_SNR,           CON_DispE,    CON_Disp5,  (uint8_t)eFCode_IotByte0,     0x08 }, // 环境温度传感器故障 ES
    { FAULT_FRZ_FAN,            CON_DispF,    CON_DispF,  (uint8_t)eFCode_IotByte0,     0x20 }, // 冷冻风机故障 FF
    { FAULT_COOL_FAN,           CON_DispL,    CON_DispF,  (uint8_t)eFCode_IotByte1,     0x02 }, // 冷凝风机故障 LF
    { FAULT_DEF_FUNC,           CON_DispE,    CON_DispD,  (uint8_t)eFCode_IotByte0,     0x40 }, // 化霜故障 Ed
    { FAULT_INVERTER_COMM,      CON_DispC,    CON_Disp0,  (uint8_t)eFCode_IotByte2,     0x01 }, // 变频板通信故障 C0
    { FAULT_DOOR_REF_LEFT,      CON_DispD,    CON_DispL,  (uint8_t)eFCode_DoorSwitch,   0x01 }, // 冷藏左门超时 dL
    { FAULT_DOOR_REF_RIGHT,     CON_DispD,    CON_DispR,  (uint8_t)eFCode_DoorSwitch,   0x02 }, // 冷藏右门超时 dr
    { FAULT_DOOR_FRZ_LEFT,      CON_DispD,    CON_Disp5,  (uint8_t)eFCode_DoorSwitch,   0x04 }, // 冷冻左门超时 dS
    { FAULT_DOOR_FRZ_RIGHT,     CON_DispD,    CON_DispF,  (uint8_t)eFCode_DoorSwitch,   0x08 }, // 冷冻右门超时 dF
    { FAULT_WIFI_MODULE,        CON_DispU,    CON_DispU,  (uint8_t)eFCode_Other,        0x01 }, // WiFi模组不匹配
};
// clang-format on

static void Collect_ActiveFaultCode(void)
{
    uint8_t index = 0;
    uint8_t bit_value = 0;
    uint8_t active_number = 0;

    if(memcmp(ary_FaultCodeByte, ary_FaultCodeByteBak, (uint8_t)eFCode_Max) != 0)
    {
        memcpy(ary_FaultCodeByteBak, ary_FaultCodeByte, (uint8_t)eFCode_Max);
        u8_FaultCodeNumber = 0;

        for(index = 0; index < U8_FAULT_CODE_NUMNER; index++)
        {
            bit_value = ary_FaultCodeByteBak[ary_DispFault[index].u8_ByteNum] & ary_DispFault[index].u8_BitValue;
            if(bit_value == ary_DispFault[index].u8_BitValue)
            {
                st_DispFaultData.ary_FaultCodebuffer[u8_FaultCodeNumber] = index;
                u8_FaultCodeNumber++;
                st_DispFaultData.ary_ActiveFaultbuffer[active_number] = index;
                active_number++;
            }
        }
        st_DispFaultData.u8_ActiveFaultNumber = active_number;
    }
    st_DispFaultData.u8_FaultNumber = u8_FaultCodeNumber;

    if(0 != u8_FaultCodeNumber)
    {
        Wakeup_UserInterface();
    }
}

void Collect_FaultCode(void)
{
    bool err_state = false;
    uint8_t byte = 0;

    byte = 0;
    err_state = Get_DisplayCommErr();
    BIT_WRITE(byte, BIT_INDEX_0, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_REF);
    BIT_WRITE(byte, BIT_INDEX_1, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_FRZ);
    BIT_WRITE(byte, BIT_INDEX_2, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_ROOM);
    BIT_WRITE(byte, BIT_INDEX_3, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_DEFROST);
    BIT_WRITE(byte, BIT_INDEX_4, err_state);
    err_state = Is_FanContinueError(FRZ_FAN);
    BIT_WRITE(byte, BIT_INDEX_5, err_state);
    err_state = Get_DefrostFunctionErrorReport();
    BIT_WRITE(byte, BIT_INDEX_6, err_state);
    ary_FaultCodeByte[(uint8_t)eFCode_IotByte0] = byte;

    byte = 0;
    err_state = Is_FanContinueError(COOL_FAN);
    BIT_WRITE(byte, BIT_INDEX_1, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_VV);
    BIT_WRITE(byte, BIT_INDEX_2, err_state);
    ary_FaultCodeByte[(uint8_t)eFCode_IotByte1] = byte;

    byte = 0;
    err_state = Get_InverterCommErr();
    BIT_WRITE(byte, BIT_INDEX_0, err_state);
    ary_FaultCodeByte[(uint8_t)eFCode_IotByte2] = byte;

    byte = 0;
    err_state = Get_DoorSwitchAlarmState((uint8_t)DOOR_REF_LEFT);
    BIT_WRITE(byte, BIT_INDEX_0, err_state);
    err_state = Get_DoorSwitchAlarmState((uint8_t)DOOR_REF_RIGHT);
    BIT_WRITE(byte, BIT_INDEX_1, err_state);
    err_state = Get_DoorSwitchAlarmState((uint8_t)DOOR_FRZ_LEFT);
    BIT_WRITE(byte, BIT_INDEX_2, err_state);
    err_state = Get_DoorSwitchAlarmState((uint8_t)DOOR_FRZ_RIGHT);
    BIT_WRITE(byte, BIT_INDEX_3, err_state);
    ary_FaultCodeByte[(uint8_t)eFCode_DoorSwitch] = byte;

    byte = Get_CompErrorState();
    ary_FaultCodeByte[(uint8_t)eFCode_Inverter] = byte;

    byte = 0;
    err_state = Get_FctWifiMatchError();
    BIT_WRITE(byte, BIT_INDEX_0, err_state);
    ary_FaultCodeByte[(uint8_t)eFCode_Other] = byte;

    ary_FaultCodeByte[(uint8_t)eFCode_FactoryDevice] = Get_FactoryDeviceFaultByte();

    Collect_ActiveFaultCode();
}

uint16_t Get_CurrentDispFaultCode(void)
{
    uint8_t fault_number = 0;

    u8_TimeLength = st_DispFaultData.u8_ActiveFaultNumber << 2;

    if(u8_TimeCount < u8_TimeLength)
    {
        if(u8_TimeCount % 4 == 3)
        {
            st_DispFaultData.st_DispData.sByte.u8_MSB = CON_DispNC;
            st_DispFaultData.st_DispData.sByte.u8_LSB = CON_DispNC;
        }
        else
        {
            fault_number = st_DispFaultData.ary_FaultCodebuffer[u8_TimeCount >> 2];
            st_DispFaultData.st_DispData.sByte.u8_MSB = ary_DispFault[fault_number].u8_HighDisp;
            st_DispFaultData.st_DispData.sByte.u8_LSB = ary_DispFault[fault_number].u8_LowDisp;
        }

        if(u8_TimeCount100ms < U8_500MS_IN_100MS_COUNT)
        {
            u8_TimeCount100ms++;
        }
        else
        {
            u8_TimeCount100ms = 0;
            u8_TimeCount++;
        }
    }
    else
    {
        u8_TimeCount = 0;
    }

    return (st_DispFaultData.st_DispData.u16_Word);
}

uint8_t Get_FaultCodeByte(uint8_t index)
{
    return (ary_FaultCodeByte[(FaultCodeByteIndex_t)index]);
}

uint8_t Get_FaultCodeNumber(void)
{
    return (u8_FaultCodeNumber);
}
