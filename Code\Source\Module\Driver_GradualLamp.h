/*!
 * @file
 * @brief This file defines public constants, types and functions for the pwm adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __DRIVER_GRADUAL_LAMP_H__
#define __DRIVER_GRADUAL_LAMP_H__

#include <stdint.h>
#include <stdbool.h>

#define U8_SEGMENT_NUMBER ((uint8_t)9)
#define U8_LAMP_DUTY_MAX_VALUE ((uint8_t)100)
#define U16_GRADUAL_LAMP_CYCLE_MAX_VALUE ((uint16_t)6400)
#define U16_GRADUAL_LAMP_UPDATE_INTERVAL_TIME_MSELS ((uint16_t)8)
#define U16_BREATH_LAMP_UPDATE_INTERVAL_TIME_MSELS ((uint16_t)8)
#define U16_BREATH_LAMP_KEEP_HIGH_BRIGHT_TIME_MSELS ((uint16_t)1000)
#define U16_BREATH_LAMP_KEEP_LOW_BRIGHT_TIME_MSELS ((uint16_t)1500)

typedef enum
{
    GRADUAL_LAMP_ID0 = 0,
    GRADUAL_LAMP_MAX,
    REF_SURFACE_LAMP = GRADUAL_LAMP_ID0
} GradualLampID_em;

typedef enum
{
    GRADUAL_LAMP_TYPE_BRIGHTENING = 0,
    GRADUAL_LAMP_TYPE_BRIGHTENING_FADING,
    GRADUAL_LAMP_TYPE_FADING,
    GRADUAL_LAMP_TYPE_BREATHING,
    GRADUAL_LAMP_TYPE_MAX
} GradualLampType_em;

typedef enum
{
    GRADUAL_LAMP_STATE_STOP = 0,
    GRADUAL_LAMP_STATE_BRIGHTENING,
    GRADUAL_LAMP_STATE_KEEP_HIGH_BRIGHT,
    GRADUAL_LAMP_STATE_FADING,
    GRADUAL_LAMP_STATE_KEEP_LOW_BRIGHT,
    GRADUAL_LAMP_STATE_MAX
} GradualLampState_em;

typedef struct
{
    uint16_t u16_PWMSetMaxDutyValve;
    uint16_t u16_PWMSetMinDutyValve;
    uint16_t u16_PWMDutyValue;
    uint16_t u16_PWMDutyRealValue;
    uint16_t u16_TimeMselCount;

    GradualLampState_em em_GradualLampState;
    uint8_t f_Init;
    uint8_t f_FactoryTest;
    uint8_t f_LampState;
} GradualLampParm_st;

typedef struct
{
    void (*Set_LampPWMDutyValue)(uint16_t u16_duty_value);

    uint16_t u16_PWMCycleValue;
    uint8_t u8_PWMMaxDuty;
    uint8_t u8_PWMMinDuty;
    GradualLampType_em em_GradualLampType;
} ConGradualLampParm_st;

typedef struct
{
    uint16_t u16_SegmentValue;
    uint16_t u16_SetpValue;
} GradualLampUpdateParm_st;

void Init_GradualLamp(void);
void Test_GradualLamp(uint8_t u8_gradual_lamp_ID, bool b_test_state);
void Set_GradualLampMaxDuty(const GradualLampID_em em_gradual_lamp_ID, uint8_t u8_max_duty);
void Set_GradualLampState(const GradualLampID_em em_gradual_lamp_ID, const bool b_lamp_state);
bool Get_GradualLampState(const GradualLampID_em em_gradual_lamp_ID);
void Driver_GradualLamp(void);
void Toggle_LampTest(void);

#endif /* __DRIVER_GRADUAL_LAMP_H__ */
