/*!
 * @file
 * @brief Manages all the state variables of the system.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "DisplayInterface.h"
#include "Core_CallBackTimer.h"
#include "Parameter_TemperatureZone.h"
#include "LedController.h"
#include "KeyManager.h"
#include "CoolingCycle.h"
#include "SystemTimerModule.h"
#include "FridgeRunner.h"
#include "Driver_Flash.h"

#define U16_UI_POLL_MILLISECONDS (uint16_t)100
#define U16_UI_STARTUP_TIME_100MSEC_COUNT (uint16_t)30 // 3s
#define U16_UI_ACTIVE_TIME_100MSEC_COUNT (uint16_t)250 // 25s
#define U16_TURBO_COOL_MINUTES (uint16_t)480 // 8h
#define U16_TURBO_FREEZE_MINUTES (uint16_t)3000 // 50h

enum
{
    Signal_Entry = SimpleFsmSignal_Entry,
    Signal_Exit = SimpleFsmSignal_Exit,
    Signal_PollTimerExpired = SimpleFsmSignal_UserStart,
    Signal_DoorOpened,
    Signal_DoorClosed,
    Signal_UiStartActive,
    Signal_UiStartSleep,
    Signal_Wakeup,
    Signal_Restart,
    Signal_Sleep,
    Signal_EnterDiagnostics
};

static SimpleFsm_t st_UserInterfaceFsm;
static st_CoreCallbackTimer st_UserInterfaceTimer;
static uint16_t u16_UiTimeCount;
static UiState_t uiState;
static UserMode_t userMode;
static uint16_t u16_TurboCoolTimerStart;
static uint16_t u16_TurboFreezeTimerStart;
static uint16_t u16_TurboCoolTimer = 0;
static uint16_t u16_TurboFreezeTimer = 0;
static uint8_t u8_ManualRefSetTemp;
static uint8_t u8_ManualFrzSetTemp;

void Process_UserMode(void)
{
    if((UserMode_t)eManual_Mode == userMode)
    {
        u8_ManualRefSetTemp = Get_RefSetTemp();
        u8_ManualFrzSetTemp = Get_FrzSetTemp();
    }

    if((UserMode_t)eTurboCool_Mode == userMode)
    {
        u16_TurboCoolTimer = Get_MinuteElapsedTime(u16_TurboCoolTimerStart);
        if(u16_TurboCoolTimer >= U16_TURBO_COOL_MINUTES)
        {
            Update_RefSetTemp(u8_ManualRefSetTemp);
            userMode = (UserMode_t)eManual_Mode;
            Set_ModeDisplay(false, userMode);
            Set_TurboCoolState(false);
        }
        else
        {
            Set_TurboCoolState(true);
        }
    }
    else
    {
        Set_TurboCoolState(false);
        u16_TurboCoolTimerStart = Get_MinuteCount();
        u16_TurboCoolTimer = 0;
    }

    if((UserMode_t)eTurboFreeze_Mode == userMode)
    {
        u16_TurboFreezeTimer = Get_MinuteElapsedTime(u16_TurboFreezeTimerStart);
        if(u16_TurboFreezeTimer >= U16_TURBO_FREEZE_MINUTES)
        {
            Update_FrzSetTemp(u8_ManualFrzSetTemp);
            userMode = (UserMode_t)eManual_Mode;
            Set_ModeDisplay(false, userMode);
            Set_TurboFreezeState(false);
            Set_TurboFreezeDefrostingAutoExitState(true);
        }
        else
        {
            Set_TurboFreezeState(true);
        }
    }
    else
    {
        Set_TurboFreezeState(false);
        Set_TurboFreezeDefrostingAutoExitState(false);
        u16_TurboFreezeTimerStart = Get_MinuteCount();
        u16_TurboFreezeTimer = 0;
    }
}

static void PollTimerExpired(void)
{
    SimpleFsm_SendSignal(&st_UserInterfaceFsm, Signal_PollTimerExpired, NULL);
}

static void UserInterface_PollTimer(uint16_t tickMsec)
{
    Core_CallbackTimer_TimerStart(
        &st_UserInterfaceTimer,
        PollTimerExpired,
        0,
        tickMsec,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

static void UserInterface_StopTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_UserInterfaceTimer);
}

static void UserInterface_Startup(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void UserInterface_Active(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void UserInterface_Sleep(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);

static void UserInterface_Startup(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
        case Signal_Restart:
            uiState = (UiState_t)eUiStartup_State;
            if(IsQuiteOtaBoot())
            {
                Set_AllLedsState(false);
            }
            else
            {
                Set_AllLedsState(true);
            }
            Init_DisplayStatus();
            u16_UiTimeCount = 0;
            UserInterface_PollTimer(U16_UI_POLL_MILLISECONDS);
            break;
        case Signal_PollTimerExpired:
            u16_UiTimeCount++;
            if(u16_UiTimeCount >= U16_UI_STARTUP_TIME_100MSEC_COUNT)
            {
                if(IsQuiteOtaBoot())
                {
                    SimpleFsm_Transition(fsm, UserInterface_Sleep);
                }
                else
                {
                    SimpleFsm_Transition(fsm, UserInterface_Active);
                }
            }
            break;
        case Signal_Exit:
            u16_UiTimeCount = 0;
            break;
        default:
            break;
    }
}

static void UserInterface_Active(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            uiState = (UiState_t)eUiActive_State;
            u16_UiTimeCount = 0;
            UserInterface_PollTimer(U16_UI_POLL_MILLISECONDS);
            break;
        case Signal_Wakeup:
            u16_UiTimeCount = 0;
            break;
        case Signal_PollTimerExpired:
            Update_DisplayStatus();
            u16_UiTimeCount++;
            if(u16_UiTimeCount >= U16_UI_ACTIVE_TIME_100MSEC_COUNT)
            {
                SimpleFsm_Transition(fsm, UserInterface_Sleep);
            }
            break;
        case Signal_Restart:
            SimpleFsm_Transition(fsm, UserInterface_Startup);
            break;
    case Signal_Sleep:
        SimpleFsm_Transition(fsm, UserInterface_Sleep);
        break;
        case Signal_Exit:
            break;
        default:
            break;
    }
}

static void UserInterface_Sleep(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            uiState = (UiState_t)eUiSleep_State;
            Set_AllLedsState(false);
            Set_LockStatus(true);
            UserInterface_StopTimer();
            break;
        case Signal_Wakeup:
            SimpleFsm_Transition(fsm, UserInterface_Active);
            break;
        case Signal_Restart:
            SimpleFsm_Transition(fsm, UserInterface_Startup);
            break;
        case Signal_Exit:
            break;
        default:
            break;
    }
}

void Restart_UserInterface(void)
{
    SimpleFsm_SendSignal(&st_UserInterfaceFsm, Signal_Restart, NULL);
}

void Wakeup_UserInterface(void)
{
    SimpleFsm_SendSignal(&st_UserInterfaceFsm, Signal_Wakeup, NULL);
}

void Sleep_UserInterface(void)
{
    SimpleFsm_SendSignal(&st_UserInterfaceFsm, Signal_Sleep, NULL);
}

UiState_t Get_UiState(void)
{
    return (uiState);
}

void UserInterface_Init(void)
{
    SimpleFsm_Init(&st_UserInterfaceFsm, UserInterface_Startup, NULL);
}

void Set_UserMode(UserMode_t mode)
{
    if(mode < (UserMode_t)eMax_Mode)
    {
        userMode = mode;
        switch(userMode)
        {
            case(UserMode_t)eFuzzy_Mode:
                Update_RefSetTemp(REF_LEVEL_5);
                Update_FrzSetTemp(FRZ_LEVEL_F18);
                break;
            case(UserMode_t)eTurboCool_Mode:
                Update_RefSetTemp(REF_LEVEL_2);
                Update_FrzSetTemp(u8_ManualFrzSetTemp);
                break;
            case(UserMode_t)eTurboFreeze_Mode:
                Update_RefSetTemp(u8_ManualRefSetTemp);
                Update_FrzSetTemp(FRZ_LEVEL_F30);
                break;
            case(UserMode_t)eManual_Mode:
                Update_RefSetTemp(u8_ManualRefSetTemp);
                Update_FrzSetTemp(u8_ManualFrzSetTemp);
                break;
            default:
                break;
        }
    }
    SetSysParam(SYSPARAM_USER_MODE, userMode);
}

void Update_ManualRefSetTemp(uint8_t u8_SetTemp)
{
    uint8_t set_temp;
    if(u8_SetTemp <= U8_REF_LEVEL_MIN)
    {
        set_temp = U8_REF_LEVEL_MIN;
    }
    else if(u8_SetTemp > U8_REF_LEVEL_MAX)
    {
        set_temp = U8_REF_LEVEL_MAX;
    }
    else
    {
        set_temp = u8_SetTemp;
    }
    u8_ManualRefSetTemp = set_temp;
    SetSysParam(SYSPARAM_REFTEMP, u8_ManualRefSetTemp);
}

void Update_ManualFrzSetTemp(uint8_t u8_SetTemp)
{
    uint8_t set_temp;
    if(u8_SetTemp <= U8_FRZ_LEVEL_MIN)
    {
        set_temp = U8_FRZ_LEVEL_MIN;
    }
    else if((u8_SetTemp > U8_FRZ_LEVEL_MIN) && (u8_SetTemp < U8_FRZ_ON_OFFLEVEL_MIN))
    {
        set_temp = U8_FRZ_ON_OFFLEVEL_MIN;
    }
    else if(u8_SetTemp > U8_FRZ_LEVEL_MAX)
    {
        set_temp = U8_FRZ_LEVEL_MAX;
    }
    else
    {
        set_temp = u8_SetTemp;
    }
    u8_ManualFrzSetTemp = set_temp;
    SetSysParam(SYSPARAM_FRZTEMP, u8_ManualFrzSetTemp);
}

uint8_t Get_ManualRefSetTemp(void)
{
    return u8_ManualRefSetTemp;
}

uint8_t Get_ManualFrzSetTemp(void)
{
    return u8_ManualFrzSetTemp;
}

uint16_t Get_TurboCoolTimeMinute(void)
{
    return (u16_TurboCoolTimer);
}

uint16_t Get_TurboFreezeTimeMinute(void)
{
    return (u16_TurboFreezeTimer);
}

UserMode_t Get_UserMode(void)
{
    return (userMode);
}
