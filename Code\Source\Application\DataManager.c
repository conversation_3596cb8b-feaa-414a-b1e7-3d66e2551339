/*!
 * @file
 * @brief Manages all the data.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include <string.h>
#include "Adpt_Reset.h"
#include "DataManager.h"
#include "Core_Types.h"
#include "FridgeRunner.h"
#include "Defrosting.h"
#include "CoolingCycle.h"
#include "Parameter_TemperatureZone.h"
#include "Driver_AdSample.h"
#include "Parameter_Device.h"
#include "Driver_DoorSwitch.h"
#include "Driver_CompFrequency.h"
#include "Driver_Fan.h"
#include "Driver_DoubleDamper.h"
#include "IO_Device.h"
#include "Driver_GradualLamp.h"
#include "SystemTimerModule.h"
#include "Core_Types.h"
#include "VerticalBeamHeater.h"
#include "DisplayInterface.h"
#include "InverterUsart.h"
#include "ResolverDevice.h"
#include "FaultCode.h"
#include "SystemManager.h"
#include "Driver_Flash.h"
#include "FunctionalCircuitTest.h"
#include "ParameterManager.h"
#include "TestUsart.h"
#include "FactoryMode.h"

static void Edit_UartTestNormalFrame(UartTestParm_st *const p_uart_test_parm,
    const uint8_t u8_index);
// 新:负载控制
static uint8_t Inquire_CloseAllLoads(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 关闭所有负载 0
static uint8_t Inquire_CheckComp(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 1
static uint8_t Inquire_CheckFrzFan(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 2
static uint8_t Inquire_CheckCoolFan(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 3
static uint8_t Inquire_CheckDoubleDamper(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 4
static uint8_t Inquire_CheckRefSingleDamper(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 5
static uint8_t Inquire_CheckVarSingleDamper(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 6
static uint8_t Inquire_CheckVerticalBeamHeater(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 7
static uint8_t Inquire_CheckFrzDefHeater(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 8
static uint8_t Inquire_CheckRefLamp(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 9
static uint8_t Inquire_CheckFrzLamp(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // A
static uint8_t Inquire_CheckRefVarLamp(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // B
static uint8_t Inquire_ExitCheckLoad(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // B

static uint8_t Inquire_ProjectInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 新:项目信息
static uint8_t Inquire_BoardVersionInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_InverterVersionInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 新：变频版本信息
static uint8_t Inquire_ProtocolVersionInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_SlaveVersionInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_E2Info1(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_E2Info2(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_E2Info3(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_SysStateInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count); // 添加系统状态信息
static uint8_t Inquire_SensorInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_DoorInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_SetInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_CoolingInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_DefInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_HeaterInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_LampInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_ErrInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_CompressorInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_IceMakerInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Inquire_SavedDefrostInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Shorten_TimeMinutes(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint16_t Get_MicroAdjustParm(MicroAdjust_em em_type);
static void Set_MicroAdjustParm(MicroAdjust_em em_Type, uint16_t u16_parm);
static uint8_t Read_Parm(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Write_Parm(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t Clear_Parm(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t QueryBoradSn(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t BurnWriteSn(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t ClearBoradSn(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static uint8_t EnterUpdateMode(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count);
static const uint8_t ary_ProtcolVersionInfo[2] = {
    0x01, 0x00
};
/******************************************************************************
 * Notes          :  00 负载控制
 *                   01 查询版本
 *                   02 数据查询
 *                   03 参数读写
 *                   04 查询日志
 ******************************************************************************/

static const MasterComputerCmd_st ary_MasterComputerCmd[] = {
    // Cmd1, Cmd2,    p_HandleFunc
    { 0x00, 0x00, Inquire_CloseAllLoads }, // 负载全关
    { 0x00, 0x01, Inquire_CheckComp }, // 负载控制：压缩机
    { 0x00, 0x02, Inquire_CheckFrzFan }, // 负载控制：冷冻风机
    { 0x00, 0x03, Inquire_CheckCoolFan }, // 负载控制：冷却风机
    { 0x00, 0x04, Inquire_CheckDoubleDamper }, // 负载控制：双风门
    { 0x00, 0x05, Inquire_CheckRefSingleDamper }, // 负载控制：冷藏风门
    { 0x00, 0x06, Inquire_CheckVarSingleDamper }, // 负载控制：变温风门
    { 0x00, 0x07, Inquire_CheckVerticalBeamHeater }, // 负载控制：纵梁加热丝
    { 0x00, 0x08, Inquire_CheckFrzDefHeater }, // 负载控制：冷冻化霜加热丝
    { 0x00, 0x09, Inquire_CheckRefLamp }, // 负载控制：冷藏灯
    { 0x00, 0x0A, Inquire_CheckFrzLamp }, // 负载控制：冷冻灯
    { 0x00, 0x0B, Inquire_CheckRefVarLamp }, // 负载控制：变温灯
    { 0x00, 0xFF, Inquire_ExitCheckLoad }, // 退出负载控制

    { 0x01, 0x00, Inquire_ProjectInfo }, // 新：项目信息
    { 0x01, 0x01, Inquire_BoardVersionInfo }, // 主板信息
    { 0x01, 0x02, Inquire_InverterVersionInfo }, // 新：变频板信息
    { 0x01, 0x03, Inquire_ProtocolVersionInfo },
    { 0x01, 0x04, Inquire_SlaveVersionInfo },
    { 0x01, 0x11, Inquire_E2Info1 },
    { 0x01, 0x12, Inquire_E2Info2 },
    { 0x01, 0x13, Inquire_E2Info3 },

    { 0x02, 0x00, Inquire_SysStateInfo }, // 新：系统状态信息
    { 0x02, 0x01, Inquire_SensorInfo },
    { 0x02, 0x02, Inquire_DoorInfo },
    { 0x02, 0x03, Inquire_SetInfo },
    { 0x02, 0x04, Inquire_CoolingInfo },
    { 0x02, 0x05, Inquire_DefInfo },
    { 0x02, 0x06, Inquire_HeaterInfo },
    { 0x02, 0x07, Inquire_LampInfo },
    { 0x02, 0x08, Inquire_ErrInfo },
    { 0x02, 0x09, Inquire_CompressorInfo },
    { 0x02, 0x0A, Inquire_IceMakerInfo },
    { 0x02, 0x0C, Inquire_SavedDefrostInfo },

    { 0x03, 0x01, Read_Parm },
    { 0x03, 0x02, Write_Parm },
    { 0x03, 0x03, Clear_Parm },

    { 0x03, 0x04, Shorten_TimeMinutes }, // 上位机缩时 11.6改->03 04
    { 0x03, 0x05, EnterUpdateMode },
    { 0x03, 0x06, BurnWriteSn },
    { 0x03, 0x07, QueryBoradSn },
    { 0x03, 0x08, ClearBoradSn },
};

#define U8_MASTER_COMPUTER_CMD_NUMBER \
    (uint8_t)(GET_ARRAY_SIZE(ary_MasterComputerCmd))

void Handle_MasterComputerFrame(UartTestParm_st *const p_uart_test_parm);

/******************************************************************************
 * Function name  : Inquire_CloseAllLoads
 * Description    : This function inquires .
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 关闭所有负载 返回1完成
 ******************************************************************************/
static uint8_t Inquire_CloseAllLoads(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_index = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_index = (p_uart_test_parm->ary_ReceBuff[7]);
        if(u8_index == 1)
        {
            p_uart_test_parm->ary_SendBuff[u8_length_count++] = 1; // 全关
        }
        else
        {
            p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0; // 失败
        }
    }

    return u8_length_count;
}
// 压缩机
static uint8_t Inquire_CheckComp(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_sensor_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_sensor_value = Get_CompFeedbackFreq();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_sensor_value;
    }

    return u8_length_count;
}
// 冷冻风机
static uint8_t Inquire_CheckFrzFan(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_sensor_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_sensor_value = (uint8_t)Get_FanDuty(FRZ_FAN);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_sensor_value;
    }

    return u8_length_count;
}
// 冷却风机
static uint8_t Inquire_CheckCoolFan(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_sensor_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_sensor_value = (uint8_t)Get_FanParameter(COOL_FAN);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_sensor_value;
    }
    return u8_length_count;
}
// 双风门
static uint8_t Inquire_CheckDoubleDamper(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_value = 0;
    uint8_t u8_refstate = 0;
    uint8_t u8_refvarstate = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_refstate = (uint8_t)Get_ActiveDamperState();
        u8_refvarstate = (uint8_t)Get_SlaveDamperState();
        u8_value = (u8_refstate | u8_refvarstate);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_value;
    }
    return u8_length_count;
}
// 冷藏风门
static uint8_t Inquire_CheckRefSingleDamper(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_value = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_value;
    }
    return u8_length_count;
}
// 变温风门
static uint8_t Inquire_CheckVarSingleDamper(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_value = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_value;
    }
    return u8_length_count;
}
// 纵梁加热丝
static uint8_t Inquire_CheckVerticalBeamHeater(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_value = Get_ResolvedDeviceStatus(DEVICE_VerticalBeamHeater);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_value;
    }
    return u8_length_count;
}
// 冷冻化霜加热丝
static uint8_t Inquire_CheckFrzDefHeater(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_value = Get_ResolvedDeviceStatus(DEVICE_FrzDefHeater);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_value;
    }
    return u8_length_count;
}
// 冷藏灯
static uint8_t Inquire_CheckRefLamp(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_value = (uint8_t)Get_GradualLampState(REF_SURFACE_LAMP);
        // u8_value = p_uart_test_parm->ary_ReceBuff[7];
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_value;
    }
    return u8_length_count;
}
// 冷冻灯
static uint8_t Inquire_CheckFrzLamp(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_value = p_uart_test_parm->ary_ReceBuff[7];
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_value;
    }
    return u8_length_count;
}
// 变温灯
static uint8_t Inquire_CheckRefVarLamp(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_value = p_uart_test_parm->ary_ReceBuff[7];
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_value;
    }
    return u8_length_count;
}

static uint8_t Inquire_ExitCheckLoad(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_value = p_uart_test_parm->ary_ReceBuff[7];
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_value;
    }
    return u8_length_count;
}
/******************************************************************************
 * Function name  : Inquire_ProjectInfo
 * Description    : This function inquires main board version information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 项目编号(4Byte)+boot版本号(2Bytes)+
 *                  boot：CRC校验码(2Bytes)+ app：版本号(2Bytes)+
 *                  app：CRC校验码(2Bytes)+ 显示板掰边信息(1Bytes)+
 *                  显示板软件版本(2Bytes)
 ******************************************************************************/
static uint8_t Inquire_ProjectInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t model[PRODUCT_MODEL_BYTES + 1] = { 0 };
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // 先留接口之后加：编号（bf07s）6byte
        ReadProductModel(model, PRODUCT_MODEL_BYTES);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = model[0];
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = model[1];
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = model[2];
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = model[3];
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = model[4];
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0;
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_BoardVersionInfo
 * Description    : This function inquires main board version information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 主控 boot：版本号 4byte；boot：CRC校验码 4byte；
 *                  app：版本号4byte；app：CRC校验码4byte
 ******************************************************************************/

static uint8_t Inquire_BoardVersionInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint32_t u32_sensor_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // boot：版本号 4byte
        u32_sensor_value = GetBootVersion(); // 32位 最高 次高 次低 最低
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SLSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MLSB(u32_sensor_value);

        // boot：CRC校验码 4byte
        u32_sensor_value = GetBootCrc(); // 32位 最高 次高 次低 最低
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SLSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MLSB(u32_sensor_value);

        // app：版本号4byte
        u32_sensor_value = GetAppVersion(); // 32位 最高 次高 次低 最低
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SLSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MLSB(u32_sensor_value);

        // app：CRC校验码4byte
        u32_sensor_value = GetAppCrc(); // 32位 最高 次高 次低 最低
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SLSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MLSB(u32_sensor_value);
    }

    return u8_length_count;
}

static uint8_t Inquire_InverterVersionInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint32_t u32_sensor_value = 0;
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // boot：版本号 4byte
        u32_sensor_value = Get_CompBootVersion(); // 32位 最高 次高 次低 最低
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SLSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MLSB(u32_sensor_value);

        // boot：CRC校验码 4byte
        u32_sensor_value = Get_CompBootCrc(); // 32位 最高 次高 次低 最低
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SLSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MLSB(u32_sensor_value);

        // app：版本号4byte
        u32_sensor_value = Get_CompAppVersion(); // 32位 最高 次高 次低 最低
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SLSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MLSB(u32_sensor_value);

        // app：CRC校验码4byte
        u32_sensor_value = Get_CompAppCrc(); // 32位 最高 次高 次低 最低
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SHSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_SLSB(u32_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint32_MLSB(u32_sensor_value);
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_ProtocolVersionInfo
 * Description    : This function inquires protocol version info.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 查询使用的当前协议版本，使用正式发布版本
 ******************************************************************************/

static uint8_t Inquire_ProtocolVersionInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // 协议版本信息(2Bytes)
        // 查询使用的当前协议版本，使用正式发布版本。
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = ary_ProtcolVersionInfo[0];
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = ary_ProtcolVersionInfo[1];
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_SlaveVersionInfo
 * Description    : This function inquires slave machine version information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 设备类别(1Byte)+从机专用号信息(8Bytes)+ 从机掰边信息(2Bytes)
 *                  + 从机版本代码(2Bytes)
 ******************************************************************************/

static uint8_t Inquire_SlaveVersionInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // 设备类别(1Byte)
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x01;

        // 从机专用号信息(8Bytes)
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;

        // 从机掰边信息(2Bytes)
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;

        // 从机版本代码(2Bytes)
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_E2Info1
 * Description    : This function inquires main board Eeprom save information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : None
 ******************************************************************************/

static uint8_t Inquire_E2Info1(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_E2Info2
 * Description    : This function inquires main board Eeprom save information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : None
 ******************************************************************************/

static uint8_t Inquire_E2Info2(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_E2Info3
 * Description    : This function inquires main board Eeprom save information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : None
 ******************************************************************************/

static uint8_t Inquire_E2Info3(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_SysStateInfo
 * Description    : This function inquires sensor parameter information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 系统状态
 ******************************************************************************/
static uint8_t Inquire_SysStateInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint16_t u16_sensor_value = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u16_sensor_value = Get_FridgeState(); // 系统状态
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u16_sensor_value;

        u16_sensor_value = Get_EnterDefrostingState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u16_sensor_value;
        u16_sensor_value = Get_DefrostMode();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u16_sensor_value;
        u16_sensor_value = Get_CoolingCompState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u16_sensor_value;
        u16_sensor_value = Get_ZoneCoolingState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u16_sensor_value;
        u16_sensor_value = Get_CoolingCapacityState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u16_sensor_value;
    }
    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_SensorInfo
 * Description    : This function inquires sensor parameter information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 环温、湿度、冷藏、冷藏化霜、冷冻、冷冻化霜、变温1、
 *                  变温化霜、冷藏2、变温2、干区、湿区
 ******************************************************************************/

static uint8_t Inquire_SensorInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint16_t u16_sensor_value = 0;
    uint32_t u32_value = 0;
    uint16_t u16_total_power = Get_TotalPower();
    FridgeState_t fridge_state = Get_FridgeState();

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // 环温
        u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_ROOM);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // 湿度
        u16_sensor_value = Get_HumidityRange();
        u16_sensor_value = u16_sensor_value * 5 + 10;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // 冷藏
        u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_REF);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // 冷藏化霜
        u16_sensor_value = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // 冷冻
        u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_FRZ);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // 冷冻化霜
        u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_DEFROST);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // 变温1
        u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_VV);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // 电能
        u32_value = Get_ElectricEnergy();
        u16_sensor_value = (uint16_t)(u32_value / 3600);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // 功率
        if(((FridgeState_t)eFridge_Factory) == fridge_state)
        {
            u16_total_power = Get_FactoryTotalPower();
        }
        u16_sensor_value = u16_total_power;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // // 液晶传感器1
        // u16_sensor_value = 0;
        // p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        // p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // // 液晶传感器2
        // u16_sensor_value = 0;
        // p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        // p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);

        // // 液晶传感器3
        // u16_sensor_value = 0;
        // p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_sensor_value);
        // p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_sensor_value);
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_DoorInfo
 * Description    : This function inquires door state and open time information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 人感、冷藏左门、冷藏左门开时间、冷藏右门、冷藏右门开时间、
 *                  冷藏门累计时间、冷冻1门、冷冻1门开时间、冷冻2门、
 *                  冷冻2门开时间、冷冻门累计时间、变温1门、变温1门开时间、
 *                  变温2门、变温2门开时间、变温门累计时间
 *                  门状态定义：
 *                  BIT0:        门开关端口状态
 *                  BIT1:        门开、关
 *                  BIT2:        门开关一次
 *                  BIT3:        门开报警
 *                  BIT4:        门视为已关
 *                  BIT5:        门开关故障
 ******************************************************************************/

static uint8_t Inquire_DoorInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_flag_state = 0;
    uint16_t u16_door_open_time_seconds = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // 冷藏左门
        u8_flag_state = Get_DoorSwitchflagState(DOOR_REF_LEFT);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_flag_state;

        // 冷藏左门开时间
        u16_door_open_time_seconds = Get_DoorOpenTimeSecond(DOOR_REF_LEFT);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_door_open_time_seconds);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_door_open_time_seconds);

        // 冷藏右门
        u8_flag_state = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_flag_state;
        // 冷藏右门开时间
        u16_door_open_time_seconds = Get_DoorOpenTimeSecond(DOOR_REF_RIGHT);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_door_open_time_seconds);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_door_open_time_seconds);

        // 冷藏门累计时间
        u16_door_open_time_seconds = Get_DoorOpenTimeSecond(DOOR_REF);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_door_open_time_seconds);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_door_open_time_seconds);

        // 冷冻1门
        u8_flag_state = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_flag_state;

        // 冷冻1门开时间
        u16_door_open_time_seconds = Get_DoorOpenTimeSecond(DOOR_FRZ_LEFT);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_door_open_time_seconds);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_door_open_time_seconds);

        // 冷冻2门
        u8_flag_state = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_flag_state;

        // 冷冻2门开时间
        u16_door_open_time_seconds = Get_DoorOpenTimeSecond(DOOR_FRZ_RIGHT);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_door_open_time_seconds);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_door_open_time_seconds);

        // 冷冻门累计时间
        u16_door_open_time_seconds = Get_DoorOpenTimeSecond(DOOR_FRZ);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_door_open_time_seconds);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_door_open_time_seconds);

        //        // 变温1门
        //        u8_flag_state = 0;
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_flag_state;

        //        // 变温1门开时间
        //        u16_door_open_time_seconds = 0;
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        //            Get_uint16_MSB(u16_door_open_time_seconds);
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        //            Get_uint16_LSB(u16_door_open_time_seconds);

        //        // 变温2门
        //        u8_flag_state = 0;
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_flag_state;

        //        // 变温2门开时间
        //        u16_door_open_time_seconds = 0;
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        //            Get_uint16_MSB(u16_door_open_time_seconds);
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        //            Get_uint16_LSB(u16_door_open_time_seconds);

        //        // 变温门累计时间
        //        u16_door_open_time_seconds = 0;
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        //            Get_uint16_MSB(u16_door_open_time_seconds);
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        //            Get_uint16_LSB(u16_door_open_time_seconds);
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_SetInfo
 * Description    : This function inquires set and zone on off temperature
 *                  information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 特殊功能、档位、开关机点(档位1个字节、开关机点2个字节)
 *                  特殊功能8字节、
 *                  冷藏档位、冷藏开机点、冷藏关机点、
 *                  冷冻档位、冷冻开机点、冷冻关机点、
 *                  左变温档位、左变温开机点、左变温关机点、
 *                  右变温档位、右变温开机点、右变温关机点。
 ******************************************************************************/

static uint8_t Inquire_SetInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint16_t u16_zone_on_off_temp = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // // 特殊功能8字节
        // memcpy(&(p_uart_test_parm->ary_SendBuff[u8_length_count]),
        //     (uint8_t *)(&flagState),
        //     8);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0;
        // u8_length_count += 8;

        // 冷藏档位
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_RefSetTemp();

        // 冷藏开机点
        u16_zone_on_off_temp = Get_RefZoneOnTemp();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_zone_on_off_temp);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_zone_on_off_temp);

        // 冷藏关机点
        u16_zone_on_off_temp = Get_RefZoneOffTemp();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_zone_on_off_temp);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_zone_on_off_temp);

        // 冷冻档位
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_FrzSetTemp();

        // 冷冻开机点
        u16_zone_on_off_temp = Get_FrzZoneOnTemp();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_zone_on_off_temp);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_zone_on_off_temp);

        // 冷冻关机点
        u16_zone_on_off_temp = Get_FrzZoneOffTemp();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_zone_on_off_temp);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_zone_on_off_temp);

        // 左变温档位
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_RefVarSetTemp();

        // 左变温开机点
        u16_zone_on_off_temp = Get_RefVarZoneOnTemp();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_zone_on_off_temp);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_zone_on_off_temp);

        // 左变温关机点
        u16_zone_on_off_temp = Get_RefVarZoneOffTemp();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_zone_on_off_temp);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_zone_on_off_temp);

        //        // 右变温档位
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0x00;

        //        // 右变温开机点
        //        u16_zone_on_off_temp = 0;
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        //            Get_uint16_MSB(u16_zone_on_off_temp);
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        //            Get_uint16_LSB(u16_zone_on_off_temp);

        //        // 右变温关机点
        //        u16_zone_on_off_temp = 0;
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        //            Get_uint16_MSB(u16_zone_on_off_temp);
        //        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        //            Get_uint16_LSB(u16_zone_on_off_temp);
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_CoolingInfo
 * Description    : This function inquires cooling information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 制冷相关(每个状态一个字节)
 *                  间室制冷状态(由低到高：冷藏、冷冻、变温1、变温2、干区、湿区)、
 *                  压机频率、电磁阀位置、风门状态1、风门状态2、冷冻风机、
 *                  冷却风机、冷藏风机、变温风机
 ******************************************************************************/

static uint8_t Inquire_CoolingInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_device_state = 0;
    uint16_t u16_device_state = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // 间室制冷状态(0：idle|1:Ref|2:Frz|3:RefFrz)
        u8_device_state = Get_ZoneCoolingState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;

        // 压机频率
        u16_device_state = Get_CompFreq();
        u8_device_state = (uint8_t)u16_device_state;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;

        // 风门状态(变温风门)
        u8_device_state = (uint8_t)Get_SlaveDamperState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;

        // 风门状态(冷藏风门)
        u8_device_state = (uint8_t)Get_ActiveDamperState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;

        // comp状态
        u8_device_state = Get_CoolingCompState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;

        // 冷冻风机
        u16_device_state = Get_FanDuty(FRZ_FAN);
        if(u16_device_state > 0xFF)
        {
            u16_device_state /= 10;
        }
        u8_device_state = (uint8_t)u16_device_state;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;

        // 冷却风机
        u16_device_state = Get_FanParameter(COOL_FAN);
        if(u16_device_state > 0xFF)
        {
            u16_device_state /= 10;
        }
        u8_device_state = (uint8_t)u16_device_state;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;

        // 冷藏风机
        // u16_device_state = Get_FanParameter(FRZ_FAN);
        // if(u16_device_state > 0xFF)
        // {
        //     u16_device_state /= 10;
        // }
        u8_device_state = Get_CompFeedbackFreq();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;

        // Capacity
        u8_device_state = Get_CoolingCapacityState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;

        u8_device_state = Get_EnterDefrostingState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_DefInfo
 * Description    : This function inquires defrost information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 化霜相关（时间两个字节）
 *                  冰箱运行时间(分)、压机累计运行时间(分)、
 *                  压机连续运行时间(分)、冷冻变温上次化霜后时间(分)、
 *                  冷藏上次化霜后时间(分）
 ******************************************************************************/

static uint8_t Inquire_DefInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint16_t u16_run_time_minutes = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // 冰箱运行时间(分)
        u16_run_time_minutes = Get_FridgeTotalOnTimeMinute();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_run_time_minutes);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_run_time_minutes);

        // 压机累计运行时间(分)
        u16_run_time_minutes = Get_CompTotalOnTimeMinute();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_run_time_minutes);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_run_time_minutes);

        // 压机连续运行时间(分)
        u16_run_time_minutes = Get_CompStillOnTimeMinute();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_run_time_minutes);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_run_time_minutes);

        // 化霜预冷时间(分)
        u16_run_time_minutes = Get_PreCoolingtimeMinute();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_run_time_minutes);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_run_time_minutes);

        // 化霜时间(秒)
        u16_run_time_minutes = Get_DefrostHeaterOnSecond();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_run_time_minutes);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_run_time_minutes);

        // 速冻开启时间(分)
        if((UserMode_t)eTurboCool_Mode == Get_UserMode())
        {
            u16_run_time_minutes = Get_TurboCoolTimeMinute();
        }
        else
        {
            u16_run_time_minutes = Get_TurboFreezeTimeMinute();
        }

        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_run_time_minutes);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_run_time_minutes);

        // 化霜状态
        u16_run_time_minutes = (uint16_t)Get_DefrostMode();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_run_time_minutes);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_run_time_minutes);

        // 当前化霜状态运行时间(秒)
        u16_run_time_minutes = Get_DefrostingtimeSecond();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_run_time_minutes);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_run_time_minutes);
    }

    return u8_length_count;
}

/******************************************************************************
* Function name  : Inquire_HeaterInfo
* Description    : This function inquires heater runs time and state.
* Parameters     : UartTestParm_st *const p_uart_test_parm
*                  uint8_t u8_length_count
* Return         : None
* Notes          : 加热丝(时间一个字节、加热丝状态一个字节)
*                  竖梁加热丝开通时间(1分钟内开通时间s)、
*                  变温室补偿加热丝开通时间(宽变温1分钟内开通时间s)、
*                  冷藏化霜加热丝开通时间(min)、冷冻化霜加热丝开通时间(min)、
*                  变温(制冰)化霜加热丝开通时间(min)、
*                  加热丝状态(竖梁、冷冻化霜、冷藏化霜、变温化霜、宽变温室补偿、
                   变温补偿1、变温补偿2、风门加热丝1、风门加热丝2)
******************************************************************************/

static uint8_t Inquire_HeaterInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_device_state = 0;
    uint8_t u8_run_times = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // 竖梁加热丝开通时间(1分钟内开通时间s)
        u8_run_times = Get_VerticalBeamHeaterOnSecond();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_run_times;

        // 变温室补偿加热丝开通时间(宽变温1分钟内开通时间s)
        u8_run_times = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_run_times;

        // 冷藏化霜加热丝开通时间(min)
        u8_run_times = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_run_times;

        // 冷冻化霜加热丝开通时间(min)
        u8_run_times = (uint8_t)(Get_DefrostHeaterOnSecond() / 60);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_run_times;

        // 变温(制冰)化霜加热丝开通时间(min)
        u8_run_times = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_run_times;

        // 加热丝状态(冷冻化霜、冷藏化霜、变温化霜、宽变温室补偿、竖梁、
        // 变温补偿1、变温补偿2、风门加热丝1、风门加热丝2)

        u8_device_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;

        u8_device_state = 0;
        // 竖梁加热丝
        if(Get_ResolvedDeviceStatus(DEVICE_VerticalBeamHeater) == (uint8_t)DS_On)
        {
            BIT_SET(u8_device_state, BIT_INDEX_4);
        }
        // 冷冻化霜加热丝
        if(Get_ResolvedDeviceStatus(DEVICE_FrzDefHeater) == (uint8_t)DS_On)
        {
            BIT_SET(u8_device_state, BIT_INDEX_1);
        }
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_LampInfo
 * Description    : This function inquires lamp state.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 照明灯状态(每个灯状态占1Bit, 由低到高)
 *                 BIT0： 冷藏面光源,            1表示开启, 0表示关闭;
 *                 BIT1： 冷藏辅助灯,            1表示开启, 0表示关闭;
 *                 BIT2： 冷藏MSA灯,             1表示开启, 0表示关闭;
 *                 BIT3： 冷藏上风口灯,          1表示开启, 0表示关闭;
 *                 BIT4： 冷藏中风口灯,          1表示开启, 0表示关闭;
 *                 BIT5： 冷藏下风口灯,          1表示开启, 0表示关闭;
 *                 BIT6： 摄像头补光灯,          1表示开启, 0表示关闭;
 *                 BIT7： 冷藏杀菌(净化)灯,      1表示开启, 0表示关闭;
 *                 BIT8： 冷藏左下视灯,          1表示开启, 0表示关闭;
 *                 BIT9： 冷藏右下视灯,          1表示开启, 0表示关闭;
 *                 BIT10：冷冻上灯,              1表示开启, 0表示关闭;
 *                 BIT11：冷冻下灯,              1表示开启, 0表示关闭;
 *                 BIT12：LOGO灯,                1表示开启, 0表示关闭;
 *                 BIT13：把手灯,                1表示开启, 0表示关闭;
 *                 BIT14：分配器照明灯,          1表示开启, 0表示关闭;
 *                 BIT15：预留
 ******************************************************************************/

static uint8_t Inquire_LampInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_device_state = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_device_state = 0;

        // 冷藏面光源
        if(Get_GradualLampState(REF_SURFACE_LAMP) == true)
        {
            BIT_SET(u8_device_state, 0);
        }

        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_device_state;
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_ErrInfo
 * Description    : This function inquires error information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 故障状态, 共计8字节，参考故障表
 ******************************************************************************/

static uint8_t Inquire_ErrInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_err_state = 0;
    uint16_t u16_mcu_reset_flag = Get_ResetFlag();

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_err_state = Get_uint16_MSB(u16_mcu_reset_flag);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_err_state;
        u8_err_state = Get_uint16_LSB(u16_mcu_reset_flag);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_err_state;
        u8_err_state = Get_FaultCodeByte(eFCode_IotByte0);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_err_state;
        u8_err_state = Get_FaultCodeByte(eFCode_IotByte1);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_err_state;
        u8_err_state = Get_FaultCodeByte(eFCode_IotByte2);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_err_state;
        u8_err_state = Get_FaultCodeByte(eFCode_IotByte3);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_err_state;
        u8_err_state = Get_FaultCodeByte(eFCode_DoorSwitch);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_err_state;
        u8_err_state = Get_FaultCodeByte(eFCode_Inverter);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_err_state;
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_CompressorInfo
 * Description    : This function inquires MSA information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 压机状态(母线电压、功率、转速 2个字节、
 *                  故障状态 1个字节)
 *                  母线电压、功率、转速、
 *                  故障状态1~15(过压、欠压、软件过流、硬件过流、缺相、偏置异常、堵转、
 *                  IGBT过温、过载、启动失败、飞车、强拖失败、电机过温、其它)
 ******************************************************************************/

static uint8_t Inquire_CompressorInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_state = 0;
    uint16_t u16_parm = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // 母线电压
        u16_parm = Get_CompBusVoltage();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_parm);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_parm);
        // 功率
        u16_parm = Get_CompPower();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_parm);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_parm);
        // 转速
        u16_parm = (uint16_t)Get_CompFeedbackFreq();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_parm);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_parm);
        // 故障状态
        u8_state = Get_CompErrorState();
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_IceMakerInfo
 * Description    : This function inquires ice maker information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 制冰机(温度两个字节、制冰风机、进水量一个字节、
 *                  其余每个占一个字节)
 *                  制冰机温度、制冰室温度、制冰风机、制冰机进水量、
 *                  制冰功能开启或关闭、满冰状态、分配器状态、分配器开关状态、
 *                  翻冰电机状态、盖板电机状态、碎冰电机状态、进水泵状态、
 *                  箱体总水阀状态、制冰机进水阀状态、分配器水阀状态、
 *                  进水管加热丝、分配器口加热丝、冰桶加热丝
 ******************************************************************************/

static uint8_t Inquire_IceMakerInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_state = 0;
    uint16_t u16_parm = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        // 制冰机温度
        u16_parm = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_parm);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_parm);

        // 制冰室温度
        u16_parm = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_parm);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_parm);

        // 制冰风机
        u16_parm = 0;
        if(u16_parm > 0xFF)
        {
            u16_parm /= 10;
        }
        u8_state = (uint8_t)u16_parm;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 制冰机进水量
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 制冰功能开启或关闭
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 满冰状态
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 分配器状态
        // if(true == f_Water)
        // {
        //     u8_state = 0x01;
        // }
        // else if(true == f_CrushedIce)
        // {
        //     u8_state = 0x02;
        // }
        // else if(true == f_CubedIce)
        // {
        //     u8_state = 0x04;
        // }
        // else
        // {
        //     u8_state = 0x00;
        // }
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 分配器开关状态
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 翻冰电机状态
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 盖板电机状态
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 碎冰电机状态
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 进水泵状态
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 箱体总水阀状态
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 制冰机进水阀状态
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 分配器水阀状态
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 进水管加热丝
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 分配器口加热丝
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;

        // 冰桶加热丝
        u8_state = 0;
        p_uart_test_parm->ary_SendBuff[u8_length_count++] = u8_state;
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Inquire_SavedDefrostInfo
 * Description    : This function inquires saved defrost type information.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 化霜标识(有过化霜0xAA,无0x00)、化霜次数(0-14)、化霜类型(14个)
 ******************************************************************************/

static uint8_t Inquire_SavedDefrostInfo(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_number = 0;
    uint8_t *p_defrost_type_buff = (uint8_t *)NULL;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u8_number = Get_SavedDefrostType(&p_defrost_type_buff);

        memcpy(&(p_uart_test_parm->ary_SendBuff[u8_length_count]),
            p_defrost_type_buff,
            14);
        u8_length_count += 14;
    }

    return u8_length_count;
}

/******************************************************************************
 * Function name  : Shorten_TimeMinutes
 * Description    : This function sets the number of shorten time minutes.
 * Parameters     : UartTestParm_st *const p_uart_test_parm
 *                  uint8_t u8_length_count
 * Return         : None
 * Notes          : 缩时时间为1 - 18 * 60 分钟
 ******************************************************************************/

static uint8_t Shorten_TimeMinutes(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint16_t u16_shorten_times = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u16_shorten_times = (uint16_t)(p_uart_test_parm->ary_ReceBuff[7]) * 255 +
            (uint16_t)(p_uart_test_parm->ary_ReceBuff[8]);

        if(u16_shorten_times > U16_MAX_SHORTEN_TIME_MINUTES)
        {
            u16_shorten_times = U16_MAX_SHORTEN_TIME_MINUTES;
        }

        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_MSB(u16_shorten_times);
        p_uart_test_parm->ary_SendBuff[u8_length_count++] =
            Get_uint16_LSB(u16_shorten_times);

        if(u16_shorten_times > 0)
        {
            Shorten_Timer(u16_shorten_times);
        }
    }

    return u8_length_count;
}

static uint8_t EnterUpdateMode(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    TestUart_EnterUpdateMode();
    return u8_length_count;
}

static uint8_t BurnWriteSn(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        if(WriteProductSn(&p_uart_test_parm->ary_ReceBuff[7], PRODUCT_SN_SIZE, true) < 0)
        {
            p_uart_test_parm->ary_SendBuff[u8_length_count++] = 1;
        }
        else
        {
            ParameterSnUpdate();
            p_uart_test_parm->ary_SendBuff[u8_length_count++] = 0;
        }
    }

    return u8_length_count;
}

static uint8_t QueryBoradSn(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t sn[PRODUCT_SN_SIZE + 1] = { 0 };

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        if(IsParameterManagerFault())
        {
            GetParameterManagerFault(sn, PRODUCT_SN_SIZE);
            memcpy(&p_uart_test_parm->ary_SendBuff[u8_length_count], sn, PRODUCT_SN_SIZE);
        }
        else if(IsParameterManagerReady())
        {
            if(ReadProductSn(&p_uart_test_parm->ary_SendBuff[u8_length_count], PRODUCT_SN_SIZE) < 0)
            {
                strcpy((char *)sn, "SN:NOT EXIST");
                memcpy(&p_uart_test_parm->ary_SendBuff[u8_length_count], sn, PRODUCT_SN_SIZE);
            }
        }
        else
        {
            strcpy((char *)sn, "SN:NOT READY");
            memcpy(&p_uart_test_parm->ary_SendBuff[u8_length_count], sn, PRODUCT_SN_SIZE);
        }
        u8_length_count += PRODUCT_SN_SIZE;
    }
    return u8_length_count;
}

static uint8_t ClearBoradSn(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        ClearProductSn();
        ParameterSnUpdate();
    }
    return u8_length_count;
}
static uint16_t Get_MicroAdjustParm(MicroAdjust_em em_type)
{
    uint16_t u16_parm = 0;

    switch(em_type)
    {
        case(uint8_t)E_INQUIRE_REF_ON_TEMP: // 01 冷藏室开机点
            u16_parm = Get_RefOnTempMicroAdjustParm();
            break;
        case(uint8_t)E_INQUIRE_REF_OFF_TEMP: // 02 冷藏室关机点
            u16_parm = Get_RefOffTempMicroAdjustParm();
            break;
        case(uint8_t)E_INQUIRE_FRZ_ON_TEMP: // 03 冷冻室开机点
            u16_parm = Get_FrzOnTempMicroAdjustParm();
            break;
        case(uint8_t)E_INQUIRE_FRZ_OFF_TEMP: // 04 冷冻室关机点
            u16_parm = Get_FrzOffTempMicroAdjustParm();
            break;
        case(uint8_t)E_INQUIRE_VAR_ON_TEMP: // 05 变温室1开机点
            u16_parm = Get_RefVarOnTempMicroAdjustParm();
            break;
        case(uint8_t)E_INQUIRE_VAR_OFF_TEMP: // 06 变温室1关机点
            u16_parm = Get_RefVarOffTempMicroAdjustParm();
            break;
        case(uint8_t)E_INQUIRE_COMP_PWM: // 09 压机频率目标值
            u16_parm = Get_CompFreqMicroAdjustParm();
            break;
        case(uint8_t)E_INQUIRE_FRZ_FAN: // 10 冷冻风机目标值
            u16_parm = Get_FrzFanAdjustParm();
            break;
        case(uint8_t)E_INQUIRE_COOL_FAN: // 13 冷却风机目标值
            u16_parm = Get_CoolFanAdjustParm();
            break;
        default:
            break;
    }
    return u16_parm;
}

static void Set_MicroAdjustParm(MicroAdjust_em em_type, uint16_t u16_parm)
{
    switch(em_type)
    {
        case(uint8_t)E_INQUIRE_REF_ON_TEMP: // 01 冷藏室开机点
            Set_RefOnTempMicroAdjustParm(u16_parm);
            break;
        case(uint8_t)E_INQUIRE_REF_OFF_TEMP: // 02 冷藏室关机点
            Set_RefOffTempMicroAdjustParm(u16_parm);
            break;
        case(uint8_t)E_INQUIRE_FRZ_ON_TEMP: // 03 冷冻室开机点
            Set_FrzOnTempMicroAdjustParm(u16_parm);
            break;
        case(uint8_t)E_INQUIRE_FRZ_OFF_TEMP: // 04 冷冻室关机点
            Set_FrzOffTempMicroAdjustParm(u16_parm);
            break;
        case(uint8_t)E_INQUIRE_VAR_ON_TEMP: // 05 变温室开机点
            Set_RefVarOnTempMicroAdjustParm(u16_parm);
            break;
        case(uint8_t)E_INQUIRE_VAR_OFF_TEMP: // 06 变温室关机点
            Set_RefVarOffTempMicroAdjustParm(u16_parm);
            break;
        case(uint8_t)E_INQUIRE_COMP_PWM: // 09 压机频率目标值
            Set_CompFreqMicroAdjustParm((uint8_t)u16_parm);
            break;
        case(uint8_t)E_INQUIRE_FRZ_FAN: // 10 冷冻风机目标值
            Set_FrzFanAdjustParm(u16_parm);
            break;
        case(uint8_t)E_INQUIRE_COOL_FAN: // 13 冷却风机目标值
            Set_CoolFanAdjustParm(u16_parm);
            break;
        default:
            break;
    }
}

static int ConfigSysParam(sysparam_type_e type, uint8_t val)
{
    switch(type)
    {
        case SYSPARAM_INSPECTION:
            SetSysParam(SYSPARAM_INSPECTION, val);
            break;
        default:
            return -1;
    }

    return 0;
}

static uint8_t Read_Parm(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t val;
    uint16_t u16_index = 0;
    uint16_t u16_parm = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u16_index = (uint16_t)(p_uart_test_parm->ary_ReceBuff[7]) * 256 +
            (uint16_t)(p_uart_test_parm->ary_ReceBuff[8]);
        if((p_uart_test_parm->ary_ReceBuff[7]) == 0xEE)
        {
            if(GetSysParamFromFlash((sysparam_type_e)(p_uart_test_parm->ary_ReceBuff[8]), &val) == 0)
            {
                u16_parm = val;
            }
            else
            {
                u16_parm = 0xFFFF;
            }
        }
        else
        {
            u16_parm = Get_MicroAdjustParm((MicroAdjust_em)u16_index);
        }
    }

    p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        Get_uint16_MSB(u16_index);
    p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        Get_uint16_LSB(u16_index);

    p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        Get_uint16_MSB(u16_parm);
    p_uart_test_parm->ary_SendBuff[u8_length_count++] =
        Get_uint16_LSB(u16_parm);

    return u8_length_count;
}

static uint8_t Write_Parm(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint16_t u16_index = 0;
    uint16_t u16_parm = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        u16_index = (uint16_t)(p_uart_test_parm->ary_ReceBuff[7]) * 256 +
            (uint16_t)(p_uart_test_parm->ary_ReceBuff[8]);

        u16_parm = (uint16_t)(p_uart_test_parm->ary_ReceBuff[9]) * 256 +
            (uint16_t)(p_uart_test_parm->ary_ReceBuff[10]);

        if((p_uart_test_parm->ary_ReceBuff[7]) == 0xEE)
        {
            ConfigSysParam((sysparam_type_e)(p_uart_test_parm->ary_ReceBuff[8]), u16_parm);
        }
        else
        {
            Set_MicroAdjustParm((MicroAdjust_em)u16_index, u16_parm);
        }
    }

    p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_index);
    p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_index);

    p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_MSB(u16_parm);
    p_uart_test_parm->ary_SendBuff[u8_length_count++] = Get_uint16_LSB(u16_parm);

    return u8_length_count;
}

static uint8_t Clear_Parm(UartTestParm_st *const p_uart_test_parm,
    uint8_t u8_length_count)
{
    uint8_t u8_index = 0;

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        (void)p_uart_test_parm;

        for(u8_index = 0; u8_index < (uint8_t)E_INQUIRE_MAX; u8_index++)
        {
            Set_MicroAdjustParm((MicroAdjust_em)u8_index, 0);
        }
    }

    return u8_length_count;
}

static void Edit_UartTestNormalFrame(UartTestParm_st *const p_uart_test_parm,
    const uint8_t u8_index)
{
    uint8_t u8_length_count = 0;

    if(((UartTestParm_st *)NULL != p_uart_test_parm) &&
        (NULL != ary_MasterComputerCmd[u8_index].p_HandleFunc))
    {
        p_uart_test_parm->ary_SendBuff[0] = U8_UART_BUS_FRAME_HEAD;
        p_uart_test_parm->ary_SendBuff[1] = U8_UART_BUS_FRAME_PC_ADDR;

        // 帧长度发送时，再赋值
        p_uart_test_parm->ary_SendBuff[2] = U8_UART_BUS_FRAME_BOARD_ADDR;
        p_uart_test_parm->ary_SendBuff[3] = U8_UART_BUS_FRAME_CMD;
        p_uart_test_parm->ary_SendBuff[4] = p_uart_test_parm->ary_ReceBuff[4];
        p_uart_test_parm->ary_SendBuff[5] = p_uart_test_parm->ary_ReceBuff[5];

        // 初始化帧长计数器
        u8_length_count = 7;

        u8_length_count = ary_MasterComputerCmd[u8_index].p_HandleFunc(p_uart_test_parm, u8_length_count);

        // 计算发送长度,CRC校验数据和帧尾发送时追加
        p_uart_test_parm->ary_SendBuff[6] = ((u8_length_count + 3) - 10);

        p_uart_test_parm->f_HaveFrameToSend = true;
    }
}

void Handle_MasterComputerFrame(UartTestParm_st *const p_uart_test_parm)
{
    uint8_t u8_index = 0;
    FridgeState_t fridge_state = Get_FridgeState();

    if((UartTestParm_st *)NULL != p_uart_test_parm)
    {
        if((U8_UART_BUS_FRAME_BOARD_ADDR == p_uart_test_parm->ary_ReceBuff[1]) &&
            (U8_UART_BUS_FRAME_PC_ADDR == p_uart_test_parm->ary_ReceBuff[2]) &&
            (U8_UART_BUS_FRAME_CMD == p_uart_test_parm->ary_ReceBuff[3]))
        {
            for(u8_index = 0; u8_index < U8_MASTER_COMPUTER_CMD_NUMBER;
                u8_index++)
            {
                if((ary_MasterComputerCmd[0].u8_MasterComputerCmd1 == p_uart_test_parm->ary_ReceBuff[4]) &&
                    (ary_MasterComputerCmd[u8_index].u8_MasterComputerCmd2 == p_uart_test_parm->ary_ReceBuff[5])) // 以0开头功能的都是自检的
                {
                    Set_SelfCheckDeviceIDAndValue((p_uart_test_parm->ary_ReceBuff[5]), (p_uart_test_parm->ary_ReceBuff[7])); // 5号 值是7
                    if(eFridge_FunctionalCircuitTest != fridge_state)
                    {
                        FridgeState_Update((FridgeState_t)eFridge_FunctionalCircuitTest);
                    }
                    Edit_UartTestNormalFrame(p_uart_test_parm, u8_index);
                    break;
                }
                else if((ary_MasterComputerCmd[u8_index].u8_MasterComputerCmd1 == p_uart_test_parm->ary_ReceBuff[4]) &&
                    (ary_MasterComputerCmd[u8_index].u8_MasterComputerCmd2 == p_uart_test_parm->ary_ReceBuff[5]))
                {
                    Edit_UartTestNormalFrame(p_uart_test_parm, u8_index);
                    break;
                }
            }
        }
    }
}
