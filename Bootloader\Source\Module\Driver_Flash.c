/*!
 * @file
 * @brief This module covers the complete fan functionality.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Adpt_Flash.h"
#include "Driver_Flash.h"
#include "Init_Mcu.h"
#include "Crc16_CCITT_FALSE.h"

static uint32_t JumpAddress;
static func_ptr_t JumpToApplication;
static ota_param_st *ota_zone = (ota_param_st *)BOOT_PARA_ADDRESS;
static ota_param_st ota_zone_cache;
static ota_imagedld_st ota_images[OTA_IMAGE_MAX] = {
    { APP_ADDRESS - APP_HEADER_SIZE, 0, APP_SIZE },
};
static app_promote_st *app_promote = (app_promote_st *)APP_PROMOTE_ADDR;

static bool CheckAppPromoteCrc(void)
{
    uint16_t crc = Cal_CRC_MultipleData((uint8_t *)app_promote, offsetof(app_promote_st, crc16));
    if(app_promote->crc16 == crc)
    {
        return true;
    }
    return false;
}

static void UpdateAppPromoteCrc(void)
{
    app_promote->crc16 = Cal_CRC_MultipleData((uint8_t *)app_promote, offsetof(app_promote_st, crc16));
}

static void InitAppPromote(void)
{
    if(app_promote->magic != APP_MAGIC_NUM || CheckAppPromoteCrc() == false)
    {
        app_promote->magic = APP_MAGIC_NUM;
        app_promote->count = 0;
        UpdateAppPromoteCrc();
    }
    app_promote->boot_version = atoi(BOOT_VERSION);
    app_promote->app_version = GetAppVersion();
    app_promote->app_crc = 0;
}

uint32_t convert_from_bigendian32(uint32_t val)
{
    uint32_t temp = 0;
    uint8_t *p = (uint8_t *)&val;

    temp = (p[0] << 24) |
        (p[1] << 16) |
        (p[2] << 8) |
        p[3];
    return temp;
}

uint16_t convert_from_bigendian16(uint16_t val)
{
    uint16_t temp = 0;
    uint8_t *p = (uint8_t *)&val;

    temp = (p[0] << 8) | p[1];
    return temp;
}

void Init_Flash(void)
{
    memcpy(&ota_zone_cache, ota_zone, sizeof(ota_param_st));
    InitAppPromote();
}

void SetOtaDone(uint32_t version)
{
    en_result_t res;
    debug("set ota flag start!");
    ota_zone_cache.ota_magic = OTA_MAGIC;
    ota_zone_cache.ota_flag = OTA_DONE_FLAG;
    ota_zone_cache.ota_version = version;
    res = FlashSectorErase(BOOT_PARA_ADDRESS);
    info("FlashSectorErase %lx, %d\n", BOOT_PARA_ADDRESS, res);
    res = FlashWriteBytes(BOOT_PARA_ADDRESS, (uint8_t *)&ota_zone_cache, sizeof(ota_zone_cache));
    info("FlashWriteBytes %lx, %d\n", BOOT_PARA_ADDRESS, res);
    debug("set ota flag success!\n");
    return;
}

void ClearOtaParam(void)
{
    FlashSectorErase(BOOT_PARA_ADDRESS);
}

bool IsOtaFlag(void)
{
    return (OTA_MAGIC == ota_zone_cache.ota_magic &&
           (OTA_START_FLAG == ota_zone_cache.ota_flag ||
            OTA_TESTUART_FLAG == ota_zone_cache.ota_flag));
}

bool IsTestUartOtaFlag(void)
{
    return (OTA_MAGIC == ota_zone_cache.ota_magic &&
            OTA_TESTUART_FLAG == ota_zone_cache.ota_flag);
}


int32_t ImageDownload(ota_imageid_e imageid, uint8_t *buf, uint32_t len)
{
    ota_imagedld_st *dld = &ota_images[imageid];
    uint16_t sremain = 0;
    uint32_t addr;

    if(dld->bytes == 0)
    {
        if(memcmp(buf, (app_imageheader_st *)dld->offset, sizeof(app_imageheader_st)) == 0)
        {
            dld->skip = 1;
            if(CheckImageCrc(imageid) < 0)
            {
                dld->skip = 0;
            }
        }
        else
        {
            info("flash and ota header is different\n");
        }
    }

    if(dld->bytes + len > dld->size)
    {
        err("out of the app image range \n");
        return -1;
    }

    while(len > 0)
    {
        if(dld->skip)
        {
            dld->bytes += len;
            buf += len;
            len = 0;
            continue;
        }
        addr = dld->offset + dld->bytes;
        sremain = FLASH_SECTOR_SIZE - addr % FLASH_SECTOR_SIZE;
        if(addr % FLASH_SECTOR_SIZE == 0 || dld->bytes == 0)
        {
            FlashSectorErase(addr / FLASH_SECTOR_SIZE * FLASH_SECTOR_SIZE);
        }

        if(len <= sremain)
        {
            FlashWriteBytes(addr, buf, len);
            dld->bytes += len;
            buf += len;
            len = 0;
        }
        else
        {
            FlashWriteBytes(addr, buf, sremain);
            dld->bytes += sremain;
            buf += sremain;
            len -= sremain;
        }
    }

    return 0;
}

void ImageDownloadRest(void)
{
    ota_imageid_e id;
    for(id = OTA_IMAGE_APP; id < OTA_IMAGE_MAX; id++)
    {
        ota_images[id].bytes = 0;
        ota_images[id].skip = 0;
    }
    return;
}

uint32_t GetMcuVersion(void)
{
    if(ota_zone_cache.ota_magic != OTA_MAGIC ||
        ota_zone_cache.ota_flag != OTA_DONE_FLAG ||
        ota_zone_cache.ota_version > MCU_MAX_VERSION)
    {
        return MCU_FACTORY_VERSION;
    }
    else
    {
        return ota_zone_cache.ota_version;
    }
}

int32_t CheckImageCrc(ota_imageid_e imageid)
{
    app_imageheader_st header;
    unsigned char *buf;
    uint32_t len;

    memcpy(&header, (void *)ota_images[imageid].offset, sizeof(app_imageheader_st));
    header.magic = convert_from_bigendian32(header.magic);
    header.length = convert_from_bigendian32(header.length);
    header.crc16 = convert_from_bigendian16(header.crc16);
    if(header.magic == APP_MAGIC_NUM)
    {
        len = header.length - sizeof(app_imageheader_st);
        buf = (unsigned char *)(ota_images[imageid].offset + sizeof(app_imageheader_st));
        if(header.crc16 == Cal_CRC_MultipleData(buf, len))
        {
            app_promote->app_crc = header.crc16;
            return 0;
        }
    }
    return -1;
}

int32_t CheckImageVersionCrc(ota_imageid_e imageid, uint32_t ota_version, uint32_t ota_crc)
{
    app_imageheader_st header;
    unsigned char *buf;
    uint32_t len;

    memcpy(&header, (void *)ota_images[imageid].offset, sizeof(app_imageheader_st));
    header.magic = convert_from_bigendian32(header.magic);
    header.version = convert_from_bigendian32(header.version);
    header.length = convert_from_bigendian32(header.length);
    header.crc16 = convert_from_bigendian16(header.crc16);
    info("hversion:%d  oversion:%d\n", header.version, ota_version);
    info("hcrc16:%x    ota_crc:%x\n", header.crc16, ota_crc);
    if(header.magic == APP_MAGIC_NUM && header.version == ota_version && header.crc16 == ota_crc)
    {
        len = header.length - sizeof(app_imageheader_st);
        buf = (unsigned char *)(ota_images[imageid].offset + sizeof(app_imageheader_st));
        if(header.crc16 == Cal_CRC_MultipleData(buf, len))
        {
            return 0;
        }
    }
    return -1;
}

void JumpToApp(void)
{
    DeInit_Mcu();
    JumpAddress = *(__IO uint32_t *)(APP_ADDRESS + 4);
    JumpToApplication = (func_ptr_t)JumpAddress;
    __set_MSP(*(__IO uint32_t *)(APP_ADDRESS));
    JumpToApplication();
}

void BoardReset(void)
{
    NVIC_SystemReset();
    while(1)
        ;
}

uint32_t GetAppPromoteCount(void)
{
    info("app promote count:%d\n", app_promote->count);
    return app_promote->count;
}

void ClearAppPromoteCount(void)
{
    app_promote->count = 0;
    UpdateAppPromoteCrc();
}

void IncreaseAppPromoteCount(void)
{
    app_promote->count++;
    UpdateAppPromoteCrc();
}

void SaveOfflineLog(void *buf, uint32_t len)
{
    uint32_t addr = OTA_LOG_ADDRESS;
    uint32_t offset = 0;

    for(;offset < OTA_LOG_SIZE; offset += FLASH_SECTOR_SIZE)
    {
        FlashSectorErase(addr + offset);
    }

    len = len > OTA_LOG_SIZE ? OTA_LOG_SIZE : len;
    offset = 0;
    while(len > 0)
    {
        if(len >= FLASH_SECTOR_SIZE)
        {
            FlashWriteBytes(addr + offset, buf + offset, FLASH_SECTOR_SIZE);
            len -= FLASH_SECTOR_SIZE;
            offset += FLASH_SECTOR_SIZE;
            continue;
        }
        FlashWriteBytes(addr + offset, buf + offset, len);
        len = 0;
    }
}

uint32_t GetAppVersion(void)
{
    app_imageheader_st header;
    unsigned char *buf;
    uint32_t len;

    memcpy(&header, (void *)(APP_ADDRESS - APP_HEADER_SIZE), sizeof(app_imageheader_st));
    header.magic = convert_from_bigendian32(header.magic);
    header.version = convert_from_bigendian32(header.version);
    if(header.magic == APP_MAGIC_NUM)
    {
        return header.version;
    }
    return 0;
}

uint32_t CalcBootCrc(void)
{
    unsigned char *buf = (unsigned char *)FLASH_BASE;
    uint32_t len = BOOT_SIZE - 2 * FLASH_SECTOR_SIZE;

    app_promote->boot_crc = Cal_CRC_MultipleData(buf, len);
    return app_promote->boot_crc;
}

