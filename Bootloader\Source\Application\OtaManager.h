/*!
 * @file
 * @brief This file defines public constants, types and functions for the cooling controller.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef __OTA_MANAGER_H__
#define __OTA_MANAGER_H__

#include <stddef.h>
#include <ctype.h>
#include <stdint.h>
#include "miio_uart.h"
#include "FirewareComm.h"

/* Xmodem flag definition */
#define SOH (0x01)
#define STX (0x02)
#define EOT (0x04)
#define ACK (0x06)
#define NAK (0x15)
#define CAN (0x18)
#define CTRLZ (0x1A)

/* Xmodem definition */
#define XMODEM_MTU (128)
#define XMODEM_HEADER_POS (0)
#define XMODEM_PACKNO_POS (1)
#define XMODEM_DATA_POS (3)
#define XMODEM_CRC_POS (131)
#define XMODEM_PACK_LEN (133)

/* Xmodem-1k definition */
#define XMODEM_1K_MTU (1024)
#define XMODEM_1K_HEADER_POS (0)
#define XMODEM_1K_PACKNO_POS (1)
#define XMODEM_1K_DATA_POS (3)
#define XMODEM_1K_CRC_POS (1027)
#define XMODEM_1K_PACK_LEN (1029)

#define XMODEM_RECV_BUFF_LEN (1048)
#define XMODEM_RECV_TIMEOUT (200)
#define XMODEM_INTERVAL_MS (150)
#define XMODEM_FAIL_INTERVAL_MS (1000)
#define XMODEM_INIT_TIMEOUT (150)
#define XMODEM_INIT_RETRY_MAX (10)
#define XMODEM_FAIL_RETRY_MAX (5)
#define XMODEM_WAIT_TIMEOUT_MS      (500)
#define TRANS_RETRY_MAX (20)
#define PACK_RETRY_MAX (10)
#define CRC_RETRY_MAX (10)

#define OTA_FIREWARE_MAX (7)

#define OTA_POLL_INTERVAL_MS (1)
#define REMOTE_XMODEM_RECV_TIMEOUT 18000
#define REMOTE_XMODEM_INIT_TIMEOUT 6000
#define REMOTE_XMODEM_FAIL_INTERVAL_MS 6000
#define REMOTE_XMODEM_WAIT_TIMEOUT_MS 6000
#define REMOTE_XMODEM_INTERVAL_MS 6000

typedef void (*ota_state_func)(void);

typedef enum xmodem_type
{
    XMODEM = 0,
    XMODEM_1K,
} xmodem_type;

typedef enum
{
    OTA_STATE_INIT = 0x0,
    OTA_STATE_READY,
    OTA_STATE_DOWNLOAD,
    OTA_STATE_FAIL,
    OTA_STATE_FAIL_ACK,
    OTA_STATE_RETRY,
    OTA_STATE_CHECKCRC,
    OTA_STATE_DONE,
    OTA_STATE_MAX
} ota_state_e;

#pragma pack(1)
typedef struct
{
    uint16_t id;
    uint32_t version;
    uint32_t offset;
    uint32_t length;
    uint16_t crc;
} ota_fireware_header_st;

typedef struct
{
    uint8_t hw_id_str[8];
    uint32_t total_version;
    ota_fireware_header_st fhs[OTA_FIREWARE_MAX];
    uint32_t filldata[1];
} ota_imageheader_st;
#pragma pack()

typedef struct
{
    uint32_t start;
    uint32_t end;
    uint32_t id;
    bool skip;
    fireware_comm_st *fw;
} ota_fireware_st;

typedef struct
{
    ota_state_e state;
    xmodem_type type;
    uint32_t xlen;
    uint8_t trans_retry;
    uint8_t pack_retry;
    uint8_t crc_retry;
    uint8_t pack_no;
    size_t sum_data_bytes;
    uint16_t timeout;
    uint8_t initcount;
    uint8_t header_checked;
    uint8_t failsend;
    uint8_t ota_fw;
    uint8_t ota_fwsum;
    bool schedule;
    bool panic;
    uint8_t xmodem_ch;
    uint16_t interval_ticks;
    uint32_t recv_timeout;
    uint32_t init_timeout;
    uint32_t fail_timeout;
    uint32_t wait_timeout;
    uint32_t interval_timeout;
    ota_imageheader_st header;
    uint8_t xbuff[XMODEM_RECV_BUFF_LEN];
    ota_fireware_st ota_fws[OTA_FIREWARE_MAX];
    ota_state_func otafuncs[OTA_STATE_MAX];
} ota_mannager_st;

void OtaManagerInit(void);
void OtaManagerRun(void);
void HandlerOtaScheduleInterval(void);
#endif
