/*!
 * @file
 * @brief Configures the Usart.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include <string.h>
#include "Adpt_Usart.h"
#include "Adpt_GPIO.h"
#include "TestUsart.h"
#include "DisplayUsart.h"
#include "lpuart.h"
#include "uart.h"
#include "InverterUsart.h"

static uint8_t enable_log_output = 0;

void LpUart0_SendOneData(uint8_t u8_data)
{
    LPUart_SendDataIt(M0P_LPUART0, u8_data);
}

// Uart Enable Interrupts
void LpUart0_EnalbeTxInterrupts(void)
{
    // Enable Tx interrupt
    LPUart_EnableIrq(M0P_LPUART0, LPUartTxEIrq);
}

void LpUart0_DisalbeTxInterrupts(void)
{
    // Disable Tx interrupt
    LPUart_DisableIrq(M0P_LPUART0, LPUartTxEIrq);
}

void LpUart0_EnalbeRxInterrupts(void)
{
    LPUart_ClrStatus(M0P_LPUART0, LPUartRC);
    // Enable rx interrupt
    LPUart_EnableIrq(M0P_LPUART0, LPUartRxIrq);
}

void LpUart0_DisalbeRxInterrupts(void)
{
    // Disable rx interrupt
    LPUart_DisableIrq(M0P_LPUART0, LPUartRxIrq);
}

void LpUart0_IRQHandler(void)
{
    uint8_t rec_data;
    if((LPUart_GetStatus(M0P_LPUART0, LPUartRC)) && (M0P_LPUART0->SCON_f.RCIE)) /// 接收数据
    {
        LPUart_ClrStatus(M0P_LPUART0, LPUartRC); ///< 清接收中断请求
                                                 // 用户接收数据处理
        rec_data = LPUart_ReceiveData(M0P_LPUART0); // 接收数据字节
        Handle_UartDisplayReceData(rec_data);
    }

    if(M0P_LPUART0->SCON_f.TXEIE && (LPUart_GetStatus(M0P_LPUART0, LPUartTxe)))
    {
        // 用户发送数据处理
        Handle_UartDisplaySendData();
    }
}

static void Board_InitLpUart0(void)
{
    stc_lpuart_cfg_t stcCfg;

    DDL_ZERO_STRUCT(stcCfg);

    ///< 外设模块时钟使能
    Sysctrl_SetPeripheralGate(SysctrlPeripheralLpUart0, TRUE);

    ///< LPUART 初始化
    stcCfg.enStopBit = LPUart1bit; ///< 1停止位
    stcCfg.enMmdorCk = LPUartDataOrAddr; /// 无校验
    stcCfg.stcBaud.enSclkSel = LPUartMskPclk; ///< 传输时钟源
    stcCfg.stcBaud.u32Sclk = Sysctrl_GetPClkFreq(); ///< PCLK获取
    stcCfg.stcBaud.enSclkDiv = LPUartMsk4Or8Div; ///< 采样分频
    stcCfg.stcBaud.u32Baud = 9600; ///< 波特率
    stcCfg.enRunMode = LPUartMskMode1; ///< 工作模式
    LPUart_Init(M0P_LPUART0, &stcCfg);

    ///< LPUART 中断使能
    LPUart_ClrStatus(M0P_LPUART0, LPUartRC); ///< 清接收中断请求
    LPUart_ClrStatus(M0P_LPUART0, LPUartTC); ///< 清发送中断请求
    LPUart_DisableIrq(M0P_LPUART0, LPUartRxIrq); ///< 禁止接收中断
    LPUart_DisableIrq(M0P_LPUART0, LPUartTxEIrq); ///< 禁止发送中断

    EnableNvic(LPUART0_IRQn, IrqLevel1, TRUE); ///< 系统中断使能
}

void InverterUart_SendOneData(uint8_t u8_data)
{
    Uart_SendDataIt(M0P_UART1, u8_data);
}

void InverterUart_EnalbeTxInterrupts(void)
{
    Uart_EnableIrq(M0P_UART1, UartTxEIrq);
}

void InverterUart_DisalbeTxInterrupts(void)
{
    Uart_DisableIrq(M0P_UART1, UartTxEIrq);
}

void InverterUart_EnalbeRxInterrupts(void)
{
    Uart_ClrStatus(M0P_UART1, UartRC);
    Uart_EnableIrq(M0P_UART1, UartRxIrq);
}

void InverterUart_DisalbeRxInterrupts(void)
{
    Uart_DisableIrq(M0P_UART1, UartRxIrq);
}

void Board_InitInverterUart(void)
{
    stc_uart_cfg_t stcCfg;

    DDL_ZERO_STRUCT(stcCfg);
    ///< 开启外设时钟
    Sysctrl_SetPeripheralGate(SysctrlPeripheralUart1, TRUE); ///< 使能uart1模块时钟

    ///< UART Init
    stcCfg.enRunMode = UartMskMode1; ///< 模式1
    stcCfg.enStopBit = UartMsk1bit; ///< 1bit停止位
    stcCfg.enMmdorCk = UartMskDataOrAddr; ///< 无检验
    stcCfg.stcBaud.u32Baud = 9600; ///< 波特率9600
    stcCfg.stcBaud.enClkDiv = UartMsk8Or16Div; ///< 通道采样分频配置
    stcCfg.stcBaud.u32Pclk = Sysctrl_GetPClkFreq(); ///< 获得外设时钟（PCLK）频率值
    Uart_Init(M0P_UART1, &stcCfg); ///< 串口初始化

    ///< UART中断使能
    Uart_ClrStatus(M0P_UART1, UartRC); ///< 清接收请求
    Uart_ClrStatus(M0P_UART1, UartTC); ///< 清接收请求
    Uart_EnableIrq(M0P_UART1, UartRxIrq); ///< 使能串口接收中断
    Uart_DisableIrq(M0P_UART1, UartTxEIrq); ///< 禁止串口发送完成中断

    EnableNvic(UART1_3_IRQn, IrqLevel1, TRUE); ///< 系统中断使能
}

void Uart1_IRQHandler(void)
{
    uint8_t rx_data = 0;

    if(M0P_UART1->SCON_f.RCIE && (Uart_GetStatus(M0P_UART1, UartRC))) // UART数据接收
    {
        Uart_ClrStatus(M0P_UART1, UartRC);
        rx_data = Uart_ReceiveData(M0P_UART1); // 接收数据字节
        Handle_UartInverterReceData(rx_data);
    }

    if(M0P_UART1->SCON_f.TXEIE && (Uart_GetStatus(M0P_UART1, UartTxe))) // UART数据发送
    {
        // 用户发送数据处理
        Handle_UartInverterSendData();
    }
}

void TestLog_SendOneData(uint8_t u8_data)
{
    if(enable_log_output)
    {
        Uart_SendDataPoll(M0P_UART0, u8_data);
    }
}

void TestUart_SendOneData(uint8_t u8_data)
{
    if(enable_log_output)
    {
        enable_log_output = 0;
        while(FALSE == Uart_GetStatus(M0P_UART0, UartTxe))
            ;
    }
    Uart_SendDataIt(M0P_UART0, u8_data);
}

void TestUart_EnalbeTxInterrupts(void)
{
    enable_log_output = 0;
    Uart_EnableIrq(M0P_UART0, UartTxEIrq);
}

void TestUart_DisalbeTxInterrupts(void)
{
    enable_log_output = 1;
    Uart_DisableIrq(M0P_UART0, UartTxEIrq);
}

void TestUart_EnalbeRxInterrupts(void)
{
    Uart_ClrStatus(M0P_UART0, UartRC);
    Uart_EnableIrq(M0P_UART0, UartRxIrq);
}

void TestUart_DisalbeRxInterrupts(void)
{
    Uart_DisableIrq(M0P_UART0, UartRxIrq);
}

// Test UART中断函数
void Uart0_IRQHandler(void)
{
    uint8_t rx_data = 0;

    if(M0P_UART0->SCON_f.RCIE && (Uart_GetStatus(M0P_UART0, UartRC))) // UART数据接收
    {
        Uart_ClrStatus(M0P_UART0, UartRC);
        // 用户接收数据处理
        rx_data = Uart_ReceiveData(M0P_UART0); // 接收数据字节
        Handle_UartTestReceData(rx_data);
    }

    if(M0P_UART0->SCON_f.TXEIE && (Uart_GetStatus(M0P_UART0, UartTxe))) // UART数据发送
    {
        // 用户发送数据处理
        Handle_UartTestSendData();
    }
}

void Board_InitUart3(void)
{
    stc_uart_cfg_t stcCfg;

    DDL_ZERO_STRUCT(stcCfg);
    ///< 开启外设时钟
    Sysctrl_SetPeripheralGate(SysctrlPeripheralUart3, TRUE); ///< 使能uart1模块时钟

    ///< UART Init
    stcCfg.enRunMode = UartMskMode1; ///< 模式1
    stcCfg.enStopBit = UartMsk1bit; ///< 1bit停止位
    stcCfg.enMmdorCk = UartMskDataOrAddr; ///< 无检验
    stcCfg.stcBaud.u32Baud = 115200; ///< 波特率115200
    stcCfg.stcBaud.enClkDiv = UartMsk8Or16Div; ///< 通道采样分频配置
    stcCfg.stcBaud.u32Pclk = Sysctrl_GetPClkFreq(); ///< 获得外设时钟（PCLK）频率值
    Uart_Init(M0P_UART3, &stcCfg); ///< 串口初始化

    ///< UART中断使能
    Uart_ClrStatus(M0P_UART3, UartRC); ///< 清接收请求
    Uart_ClrStatus(M0P_UART3, UartTC); ///< 清接收请求
    Uart_EnableIrq(M0P_UART3, UartRxIrq); ///< 使能串口接收中断
    Uart_EnableIrq(M0P_UART3, UartTxIrq); ///< 使能串口发送完成中断

    EnableNvic(UART1_3_IRQn, IrqLevel1, TRUE); ///< 系统中断使能
}

void Board_InitUsart(void)
{
    Board_InitLpUart0();
    Board_InitInverterUart();
	Board_InitUart3();
}

void Board_InitTestUsart(void)
{
    stc_uart_cfg_t stcCfg;

    Board_InitSwdGpio(); ///< 配置SWD为UART0

    DDL_ZERO_STRUCT(stcCfg);
    ///< 开启外设时钟
    Sysctrl_SetPeripheralGate(SysctrlPeripheralUart0, TRUE); ///< 使能uart0模块时钟

    ///< UART Init
    stcCfg.enRunMode = UartMskMode1; ///< 模式1
    stcCfg.enStopBit = UartMsk1bit; ///< 1bit停止位
    stcCfg.enMmdorCk = UartMskDataOrAddr; ///< 无检验
    stcCfg.stcBaud.u32Baud = 9600; ///< 波特率9600
    stcCfg.stcBaud.enClkDiv = UartMsk8Or16Div; ///< 通道采样分频配置
    stcCfg.stcBaud.u32Pclk = Sysctrl_GetPClkFreq(); ///< 获得外设时钟（PCLK）频率值
    Uart_Init(M0P_UART0, &stcCfg); ///< 串口初始化

    ///< UART中断使能
    Uart_ClrStatus(M0P_UART0, UartRC); ///< 清接收请求
    Uart_ClrStatus(M0P_UART0, UartTC); ///< 清接收请求
    Uart_EnableIrq(M0P_UART0, UartRxIrq); ///< 使能串口接收中断
    Uart_DisableIrq(M0P_UART0, UartTxEIrq); ///< 禁止串口发送完成中断

    EnableNvic(UART0_2_IRQn, IrqLevel1, TRUE); ///< 系统中断使能
    enable_log_output = 1;
}
