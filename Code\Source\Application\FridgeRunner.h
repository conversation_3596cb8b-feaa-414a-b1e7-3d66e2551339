/*!
 * @file
 * @brief This file defines public constants, types and functions for the fridge runner.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef FRIDGE_RUNNER_H
#define FRIDGE_RUNNER_H

#include <stdint.h>
#include <stdbool.h>
#include "SimpleFsm.h"
#include "Defrosting.h"
#include "CoolingCycle.h"

#define FLOAT_DEFROST_RATING (float)2100
#define FLOAT_AC_VOLTAGE (float)220
// 正常变化条件各环温段中最大个数
#define U8_NORMAL_DEFROST_CONDITION_MAX_NUMBER ((uint8_t)7)
// 首次化霜压机累计运行时间7小时
#define U16_FIRST_DEFROST_COMP_TOTAL_ON_TIME_MINUTES ((uint16_t)(7 * 60))
// 首次化霜冰箱运行8小时
#define U16_FIRST_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(8 * 60))
// 速冻化霜压机累计运行时间7小时
#define U16_SUPER_FRZ_DEFROST_COMP_TOTAL_ON_TIME_MINUTES ((uint16_t)(7 * 60))
// 速冻化霜冰箱累计运行12小时
#define U16_SUPER_FRZ_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(12 * 60))
// 冷冻传感器故障化霜压机累计运行8小时
#define U16_FRZ_SENSOR_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(8 * 60))
// 化霜传感器故障化霜压机累计运行8小时
#define U16_DEF_SENSOR_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(8 * 60))
// 门开关故障化霜压机累计运行8小时
#define U16_DOOR_SW_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(8 * 60))
// 化霜功能故障化霜压机累计运行5小时
#define U16_DEF_FUNCTION_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(5 * 60))
#define U16_ENERGY_FIRST_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(20 * 60))
#define U16_ENERGY_MORMAL_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(72 * 60))
// 过负载化霜压机累计运行5小时
#define U16_OVERLOAD_DEFROST_COMP_TOTAL_ON_TIME_MINUTES ((uint16_t)(5 * 60))
// 速冻第一次化霜速冻运行16小时
#define U16_TURBO_FRZ_DEFROST_COMP_TOTAL_ON_TIME_MINUTES ((uint16_t)(16 * 60))
// 过负载化霜压机累计开4小时
#define U16_OVERLOAD_DEFROST_REF_DAMPER_TOTAL_ON_TIME_MINUTES ((uint16_t)(4 * 60))

enum
{
    eRunning_CoolingCycle = 0,
    eRunning_Defrosting
};
typedef uint8_t RunningState_t;

typedef struct
{
    uint16_t u16_FridgeTotalOnTimeMinutes; // 冰箱运行时间
    uint16_t u16_CompTotalOnTimeMinutes; // 温区制冷时间
    uint16_t u16_RefDoorTotalOpenTimeSecond; // 冷藏门开门总时间
    uint16_t u16_FrzVarDoorTotalOpenTimeSecond; // 冷冻、变温开门总时间
    uint16_t u16_AllDoorTotalOpenTimeSecond; // 开门总时间
    uint16_t u16_DefrostExitTemp;
} DefrostCondition_st;

typedef struct
{
    uint8_t u8_DefrostConditionNumber; // 各个环温段条件个数
    DefrostCondition_st ary_DefrostCondition[U8_NORMAL_DEFROST_CONDITION_MAX_NUMBER];
} DefrostNormalCondition_st;

// 化霜类型存储数据
typedef struct
{
    uint8_t u8_SavedFlag;
    uint8_t u8_SavedCount;
    EnterDefrostingState_t ary_DefrostTypeBuff[U8_DEFROST_TYPE_MAX_SAVE_NUMBER];
} SaveDefrostType_st;

void FridgeRunner_Init(void);
void FridgeRunner_Exit(void);
RunningState_t Get_RunningState(void);
EnterDefrostingState_t Get_EnterDefrostingState(void);
uint8_t Get_SavedDefrostType(uint8_t **p_defrost_type_buff);
uint16_t Get_FridgeTotalOnTimeMinute(void);
void Clear_FridgeTotalOnTimeMinute(void);
bool Get_PowerOnDelayRefVarCoolingState(void);
uint16_t Get_DeforstExitTemp(void);
void Set_CoolingEntryMode(CoolingEntryMode_t mode);
void Set_TurboFreezeDefrostingAutoExitState(bool state);
void Force_EnterEnergyConsumptionModeState(void);
bool Get_EnergyConsumptionModeState(void);
bool Get_CondensationModeState(void);
void Update_ElectricEnergy(void);
// real power * 10
uint16_t Get_TotalPower(void);
// Wh * 10
uint32_t Get_ElectricEnergy(void);

#endif
