/*
 * Iot_SpecHandler.c
 *
 *  Created on: 2023骞?7鏈?3鏃?
 *      Author: Mi
 */

#include <string.h>
#include "Iot_SpecHandler.h"
#include "iot_operation_encoder.h"
#include "iot_operation_decoder.h"
#include "arch_os.h"
#include "util.h"
#include "miio_api.h"

#define REPORT_PROP_CHANGED_MAX_NUM 15

static TLong elapse_ms_after_connect = 0;
static TBool iot_operated = false;

TLong current_date = 0;

void receive_plugin_connect(void)
{
    set_plugin_connected();
    elapse_ms_after_connect = 0;
}

void receive_plugin_disconnect(void)
{
    set_plugin_disconnected();
    reset_all_prop_updated_by_plugin();
}

void countdown_for_plugin_link(void)
{
    elapse_ms_after_connect++;
    if((elapse_ms_after_connect > PLUGIN_LINK_TIMEOUT) && (is_plugin_connected() == TRUE))
    {
        receive_plugin_disconnect();
    }
}

void set_network_state(const char *pState)
{
    net_state_e net_state_cur;
    net_state_e net_state_prev;

    if(pState == NULL)
        return;

    net_state_prev = get_dev_net_state();

    if(strncmp(pState, NET_STATE_OFFLINE, strlen(NET_STATE_OFFLINE)) == 0)
    {
        net_state_cur = ZM_APP_NET_STATE_OFFLINE;
    }
    else if(strncmp(pState, NET_STATE_LOCAL, strlen(NET_STATE_LOCAL)) == 0)
    {
        net_state_cur = ZM_APP_NET_STATE_LOCAL;
    }
    else if(strncmp(pState, NET_STATE_CLOUD, strlen(NET_STATE_CLOUD)) == 0)
    {
        net_state_cur = ZM_APP_NET_STATE_CLOUD;
    }
    else if(strncmp(pState, NET_STATE_UPDATING, strlen(NET_STATE_UPDATING)) == 0)
    {
        net_state_cur = ZM_APP_NET_STATE_UPDATING;
    }
    else if(strncmp(pState, NET_STATE_UAP, strlen(NET_STATE_UAP)) == 0)
    {
        net_state_cur = ZM_APP_NET_STATE_UAP;
    }
    else if(strncmp(pState, NET_STATE_UNPROV, strlen(NET_STATE_UNPROV)) == 0)
    {
        net_state_cur = ZM_APP_NET_STATE_UNPROV;
    }
    else if(strncmp(pState, NET_STATE_UPDATING_AUTO, strlen(NET_STATE_UPDATING_AUTO)) == 0)
    {
        net_state_cur = ZM_APP_NET_STATE_UPDATING_AUTO;
    }
    else if(strncmp(pState, NET_STATE_UPDATING_FORCE, strlen(NET_STATE_UPDATING_FORCE)) == 0)
    {
        net_state_cur = ZM_APP_NET_STATE_UPDATING_FORCE;
    }
    else
    {
        return;
    }

    if(net_state_prev < ZM_APP_NET_STATE_CLOUD && net_state_cur == ZM_APP_NET_STATE_CLOUD)
    {
        if(!is_dev_has_inited())
        {
            set_dev_inited();
            trig_fridge_event(EVENT_FIRST_CONNECT_NETWORK);
        }
        else
        {
            trig_fridge_event(EVENT_CONNECT_NETWORK);
        }
    }
    else if(net_state_prev == ZM_APP_NET_STATE_CLOUD && net_state_cur != ZM_APP_NET_STATE_CLOUD)
    {
        trig_fridge_event(EVENT_DISCONNECT_NETWORK);
    }

    switch(net_state_prev)
    {
        case ZM_APP_NET_STATE_UAP:
        case ZM_APP_NET_STATE_UNPROV:
        case ZM_APP_NET_STATE_NONE:
            break;
        default:
        {
        }
    }

    set_dev_net_state(net_state_cur);
}

void update_current_date(const char *pDate)
{
    int len = 0;
    char factory_report_date[DATE_INFO_LENGTH + 1];
    char *ptemp = factory_report_date;
    TLong date = 0;

    date = 0;

    memset(factory_report_date, 0, DATE_INFO_LENGTH + 1);

    strncpy(factory_report_date, pDate, DATE_INFO_LENGTH);

    while((*ptemp != '\0') && (*ptemp != ' '))
    {
        if(len >= DATE_TO_INT_LENGTH)
            break;

        if(*ptemp != '-')
        {
            date = date * 10 + *ptemp - '0';
            len++;
        }

        ptemp++;
    }

    APP_LOG("formated time value is:%ld\r", date);

    if(date > MIN_CURRENT_DATE)
    {
        current_date = date;
    }
}

void set_wifi_arch_platform_state(const char *pState)
{
    if(strncmp(pState, WIFI_ARCH_PLATFORM, strlen(WIFI_ARCH_PLATFORM)) == 0)
    {
        set_wifi_arch_platform();
    }
}

TBool is_local_date_valid(void)
{
    if(current_date != 0)
        return TRUE;

    return FALSE;
}

void sync_fridge_params_set_by_iot(callbackSetFridgeParam callback)
{
    IotGeneralPropVal_t genPropVal;
    IotExecuteRet_e result = IOT_EXECUTE_OK;
    TByte response_num = get_fridge_param_response_nums();
    IotPropOper_result_t *p_propResult;

    for(TByte id = 0; id < response_num; id++)
    {
        p_propResult = get_fridge_param_response(id);
        if(need_sync_fridge_param_to_dev((IotExecuteRet_e)p_propResult->code, p_propResult->prop) && (!is_fridge_param_ready(id)))
        {
            if(p_propResult->code == IOT_EXECUTE_OK)
            {
                result = get_fridge_param_setby_iot(p_propResult->prop, &genPropVal);
            }
            else
            {
                result = get_fridge_param_from_dev(p_propResult->prop, &genPropVal);
            }

            if(result == IOT_EXECUTE_OK)
            {
                if(callback != NULL)
                {
                    //prop type set to DWORD, maybe changed in other products
                    if(genPropVal.propType == IOT_TYPE_STRING)
                    {
                        genPropVal.lValue = (TLong)genPropVal.sValue;
                    }
                    result = callback(p_propResult->prop, genPropVal.lValue);
                }

                if(result != IOT_EXECUTE_NEED_WAIT)
                {
                    p_propResult->code = (result == IOT_EXECUTE_OK) ? IOT_EXECUTE_OK : IOT_EXECUTE_DEV_FAIL;
                    p_propResult->ready = true;

                    if(p_propResult->code == IOT_EXECUTE_OK)
                    {
                        finish_iot_setprop_execute(p_propResult->prop, result);
                    }
                }
                else
                {
                    break;
                }
            }
            else
            {
                finish_iot_setprop_execute(p_propResult->prop, IOT_EXECUTE_FAIL);
                p_propResult->code = IOT_EXECUTE_FAIL;
                p_propResult->ready = true;
            }
        }
    }
}

TBool handled_action_by_iot(IotActionName_e actionName, TLong *value, TByte length)
{
    if(actionName == IOT_ACTION_PLUGIN_CONNECT)
    {
        receive_plugin_connect();
        return TRUE;
    }
    else if(actionName == IOT_ACTION_PLUGIN_DISCONNECT)
    {
        receive_plugin_disconnect();
        return TRUE;
    }

    return FALSE;
}

void sync_action_cached_by_iot(callbackInvokeAction callback)
{
    if(is_fridge_action_cached())
    {
        IotAction_result_t *p_actionResult = get_fridge_action_cached();
        if(p_actionResult->code == IOT_EXECUTE_OK)
        {
            TLong *params;
            TByte param_len;
            IotExecuteRet_e result = IOT_EXECUTE_OK;

            params = get_action_fridge_params(p_actionResult->action, &param_len);
            if(params != NULL)
            {
                if(!handled_action_by_iot(p_actionResult->action, params, param_len))
                {
                    iot_operated = true;
                    if(is_remote_control_allowed())
                    {
                        if(callback != NULL)
                        {
                            result = (callback(p_actionResult->action, params, param_len) == IOT_EXECUTE_OK) ? IOT_EXECUTE_OK : IOT_EXECUTE_DEV_FAIL;
                        }
                    }
                    else
                    {
                        result = IOT_EXECUTE_CHILD_PROTECTED;
                    }
                }

                if(result != IOT_EXECUTE_NEED_WAIT)
                {
                    finish_iot_action_execute(p_actionResult->action, result);
                    p_actionResult->code = result;
                }
            }
            else
            {
                finish_iot_action_execute(p_actionResult->action, IOT_EXECUTE_FAIL);
                p_actionResult->code = IOT_EXECUTE_FAIL;
            }
        }
    }
}

TBool is_wait_dev_async_execute(void)
{
    TByte response_num = get_fridge_param_response_nums();
    TBool action_cached = is_fridge_action_cached();

    if(response_num > 0 || action_cached)
    {
        return TRUE;
    }

    return FALSE;
}

TBool response_for_last_execute(char *pResult)
{
    TByte response_num = get_fridge_param_response_nums();

    if(response_num > 0)
    {
        TBool bReady = TRUE;
        TByte id = 0;

        for(id = 0; id < response_num; id++)
        {
            if(!is_fridge_param_ready(id))
            {
                bReady = FALSE;
                break;
            }
        }

        if(bReady)
        {
            IotPropOper_result_t *p_propResult;

            str_n_cat(pResult, 1, "result ");
            for(id = 0; id < response_num; id++)
            {
                p_propResult = get_fridge_param_response(id);
                iot_property_operation_encode(p_propResult, pResult, CMD_RESULT_BUF_SIZE, TRUE);
            }

            iot_operation_encode_end(pResult, CMD_RESULT_BUF_SIZE);
            if(miio_uart_send_str(pResult) <= 0)
            {
                APP_LOG("set property send failed");
            }

            reset_iot_setprop_excute_state();
        }

        return TRUE;
    }

    if(is_fridge_action_cached())
    {
        if(is_fridge_action_ready())
        {
            IotAction_result_t *p_actionResult = get_fridge_action_cached();

            if(iot_action_operation_encode(p_actionResult, pResult, CMD_RESULT_BUF_SIZE) == MIIO_OK)
            {
                TByte paramNum;
                char piid_buf[ID_MAX_LEN + 1] = { 0 };
                IotGeneralPropVal_t genPropVal;

                paramNum = get_action_fridge_out_param_num(p_actionResult->action);

                for(int i = 0; i < paramNum; i++)
                {
                    get_action_fridge_out_param_value(p_actionResult->action, i, &genPropVal);

                    sprintf(piid_buf, "%d", get_spec_piid_by_action_out_index(p_actionResult->action, i));
                    str_n_cat(pResult, 2, piid_buf, " ");

                    iot_operation_value_encode(&genPropVal, pResult, CMD_RESULT_BUF_SIZE);
                }
            }

            iot_operation_encode_end(pResult, CMD_RESULT_BUF_SIZE);

            if(miio_uart_send_str(pResult) <= 0)
            {
                APP_LOG("invoke action send failed");
            }

            reset_fridge_action_cached();
        }

        return TRUE;
    }

    return FALSE;
}

IotExecuteRet_e custom_get_factorydata_value(IotPropName_e prop, IotGeneralPropVal_t *pValue)
{
    IotExecuteRet_e ret = IOT_EXECUTE_FAIL;
    return ret;
}

IotExecuteRet_e custom_get_datalogging_value(IotPropName_e prop, IotGeneralPropVal_t *pValue)
{
    switch(prop)
    {
        default:
            return IOT_EXECUTE_FAIL;
    }

    return IOT_EXECUTE_OK;
}

TBool report_properties_changed_by_event(TWord event, char *pResult)
{
    TBool sendHead = FALSE;
    IotGeneralPropVal_t genPropVal;
    IotPropOper_result_t prop_Oper;
    TByte report_num = 0;

    prop_Oper.pValue = &genPropVal;

    for(IotPropName_e prop = IOT_PROP_MIN; prop < IOT_PROP_MAX; prop++)
    {
        TBool needReport = FALSE;
        TBool eventTrig = FALSE;

        if(is_fridge_param_updated(prop))
        {
            needReport = TRUE;
        }

        if(!needReport)
        {
            switch(event)
            {
                case EVENT_FIRST_CONNECT_NETWORK:
                    if(is_fridge_param_policy_matched(prop, PROP_POLICY_BASIC_INFO))
                        needReport = TRUE;
                    break;
                default:
                    break;
            }

            if(needReport)
                eventTrig = TRUE;
        }

        if(needReport)
        {
            report_num++;
            if(report_num <= REPORT_PROP_CHANGED_MAX_NUM)
            {
                if(!sendHead)
                {
                    memset(pResult, 0, CMD_RESULT_BUF_SIZE);
                    str_n_cat(pResult, 2, "properties_changed", " ");
                    sendHead = TRUE;
                }

                prop_Oper.siid = get_spec_siid_by_propname(prop);
                prop_Oper.piid = get_spec_piid_by_propname(prop);

                get_fridge_param_from_dev(prop, prop_Oper.pValue);
                iot_changed_operation_encode(&prop_Oper, pResult, CMD_RESULT_BUF_SIZE);
                clear_fridge_param_updated(prop);
            }
            else
            {
                if(eventTrig)
                {
                    set_fridge_param_updated_by_event(prop);
                }
            }
        }
    }

    if(sendHead)
    {
        iot_operation_encode_end(pResult, CMD_RESULT_BUF_SIZE);
        return TRUE;
    }

    return FALSE;
}

TBool report_properties_changed_to_iot(char *pResult)
{
    //TODO:events need consider timeliness, if network not ready when event occured, just drop it
    if(is_dev_connected_iot_svr())
    {
        //report on device init
        if(is_fridge_event_exist(EVENT_FIRST_CONNECT_NETWORK))
        {
            clear_fridge_event(EVENT_FIRST_CONNECT_NETWORK);
            return report_properties_changed_by_event(EVENT_FIRST_CONNECT_NETWORK, pResult);
        }

        //report on network_connected
        if(is_fridge_event_exist(EVENT_CONNECT_NETWORK))
        {
            clear_fridge_event(EVENT_CONNECT_NETWORK);
            return report_properties_changed_by_event(EVENT_CONNECT_NETWORK, pResult);
        }

        return report_properties_changed_by_event(EVENT_HAS_NONE, pResult);
    }

    return FALSE;
}

TBool report_event_to_iot(char *pResult)
{
    return FALSE;
}

static void on_property_set_to_buffer(IotPropOper_result_t *pIotResult)
{
    prepare_iot_setprop_execute(pIotResult->siid, pIotResult->piid, pIotResult->pValue);
    iot_operated = true;
}

static void on_property_get_from_buffer(IotPropOper_result_t *pIotResult)
{
    IotExecuteRet_e ret;
    IotPropName_e propName;

    propName = get_propname_by_spec_id((TByte)pIotResult->siid, (TByte)pIotResult->piid);
    ret = get_fridge_param_from_dev(propName, pIotResult->pValue);

    if(ret == IOT_EXECUTE_OK)
    {
        pIotResult->code = IOT_EXECUTE_OK;
        if(is_plugin_connected() && (!is_prop_updated_by_plugin(propName)))
        {
            set_prop_updated_by_plugin(propName);
        }
    }
    else
    {
        pIotResult->code = ret;
    }
}

static void on_action_cached(const char *pbuf, size_t buf_sz)
{
    const char *temp = pbuf;
    int pos = 0;

    if((NULL == pbuf) || (buf_sz == 0))
        return;

    do
    {
        uint16_t siid;
        uint16_t aiid;
        uint16_t piid;

        // get siid
        siid = miio_atoi(temp);

        // get aiid
        pos = miio_strtok(temp, " ");
        temp = temp + pos;
        aiid = miio_atoi(temp);

        if(prepare_iot_action_execute(siid, aiid))
        {
            // get piid & value
            int index = 0;
            IotGeneralPropVal_t genPropVal;

            while((pos = miio_strtok(temp, " ")) != 0)
            {
                temp = temp + pos;
                piid = miio_atoi(temp);
                pos = miio_strtok(temp, " ");
                temp = temp + pos;

                if(0 == pos)
                {
                    index = 0xFF;
                }
                else
                {
                    iot_operation_value_decode(temp, &genPropVal);
                }

                if(!prepare_iot_action_param(siid, piid, index, &genPropVal))
                {
                    break;
                }

                index++;
            }
        }

    } while(0);
}

int execute_property_operation_async(const char *pbuf, int buf_sz, bool set_value, char *presult)
{
    int params_pairs = 1;
    const char *temp = pbuf;
    int pos = 0;
    int step = set_value ? 3 : 2;

    if((NULL == pbuf) || (buf_sz == 0))
        return -1;

    while(1)
    {
        pos = miio_strtok(temp, " ");
        if(0 != pos)
        {
            params_pairs++;
            temp += pos;
        }
        else
        {
            break;
        }
    }

    if(params_pairs % step != 0)
    {
        APP_LOG("params error");
        return -1;
    }
    else
    {
        params_pairs /= step;
    }

    do
    {
        uint32_t i;
        IotGeneralPropVal_t genPropVal;
        IotPropOper_result_t iotPropResult;

        iotPropResult.pValue = &genPropVal;
        temp = pbuf;
        pos = 0;

        if(set_value)
        {
            reset_iot_setprop_excute_state();
        }
        else
        {
            str_n_cat(presult, 1, "result ");
        }

        for(i = 0; i < params_pairs; ++i)
        {
            iotPropResult.siid = miio_atoi(temp);

            // get piid string
            pos = miio_strtok(temp, " ");
            temp = temp + pos;
            iotPropResult.piid = miio_atoi(temp);

            if(set_value)
            {
                // get value string
                pos = miio_strtok(temp, " ");
                temp = temp + pos;
                iot_operation_value_decode(temp, iotPropResult.pValue);
                on_property_set_to_buffer(&iotPropResult);
            }
            else
            {
                if(get_propname_by_spec_id(iotPropResult.siid, iotPropResult.piid) == IOT_PROP_FACTORY_DATA)
                {
                    get_factory_data(presult, CMD_RESULT_BUF_SIZE);
                }
                else
                {
                    on_property_get_from_buffer(&iotPropResult);
                    iot_property_operation_encode(&iotPropResult, presult, CMD_RESULT_BUF_SIZE, set_value);
                }
            }

            if(i >= REPORT_PROP_CHANGED_MAX_NUM - 1) //21*3=63
            {
                APP_LOG("prop operate param exceed 20:%d\r", params_pairs);
                break;
            }

            pos = miio_strtok(temp, " ");
            temp = temp + pos;
        }

        if(!set_value)
            iot_operation_encode_end(presult, CMD_RESULT_BUF_SIZE);
    } while(false);

    return 0;
}

int execute_action_invocation_async(const char *pbuf, int buf_sz, char *presult)
{
    int ret = 0;
    int params_pairs = 1;
    const char *temp = pbuf;
    int pos = 0;

    while(1)
    {
        pos = miio_strtok(temp, " ");
        if(0 != pos)
        {
            params_pairs++;
            temp += pos;
        }
        else
        {
            break;
        }
    }

    if(params_pairs >= 4 && params_pairs % 2 != 0)
    {
        APP_LOG("params error");
        ret = -1;
        miio_encode_error_response(ERROR_MESSAGE_UNPARAMS, ERROR_CODE_UNPARAMS, presult);
        return ret;
    }

    on_action_cached(pbuf, buf_sz);

    return ret;
}
