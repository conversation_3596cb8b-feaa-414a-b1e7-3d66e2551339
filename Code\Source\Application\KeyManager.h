/*!
 * @file
 * @brief This file defines public constants, types and functions for the key manager.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef KEY_MANAGER_H
#define KEY_MANAGER_H

#include <stdint.h>
#include <stdbool.h>

#define KEY_VALUE_INVALID 0x00
#define KEY_VALUE_VAR_CHAMBER 0x01
#define KEY_VALUE_VAR 0x02
#define KEY_VALUE_LOCK 0x04
#define KEY_VALUE_MODE 0x08
#define KEY_VALUE_REF 0x10
#define KEY_VALUE_FRZ 0x20
#define KEY_VALUE_BABY 0x40

typedef struct
{
    uint16_t u16_CurrentKeyValue;
    uint16_t u16_PreviousKeyValue;
    uint16_t u16_ValidKeyValue;
    uint16_t u16_ReleaseKeyTimer;
    uint16_t u16_PcbTestTimer;
    uint16_t u16_HoldKeyTimer;
    uint16_t u16_NoKeyTimer;
    uint8_t u8_RefSetpoint;
    uint8_t u8_RefVarSetpoint;
    uint8_t u8_FrzSetpoint;
    uint8_t u8_ModeSetpoint;

    bool b_RefAdjust;
    bool b_RefVarAdjust;
    bool b_FrzAdjust;
    bool b_ModeAdjust;
    bool b_RefDisp;
    bool b_FrzDisp;
    bool b_KeyLock;
    bool b_KeyWorked;
    bool b_WifiReset;
} KeyAction_st;

void Handle_KeyManager(void);
void SetOver(void);
void Set_LockStatus(bool status);
bool Get_LockStatus(void);

#endif
