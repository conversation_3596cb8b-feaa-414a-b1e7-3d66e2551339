/*!
 * @file
 * @brief Manages all the state variables of the zone parameter.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Parameter_TemperatureZone.h"
#include "Driver_AdSample.h"
#include "DisplayInterface.h"
#include "CoolingCycle.h"
#include "Driver_Flash.h"
#include "FridgeRunner.h"

static uint8_t u8_RefVarSetTemp = (RefVarSet_t)eRefVar_Treasure;
static uint8_t u8_RefSetTemp = (uint8_t)REF_LEVEL_5;
static uint8_t u8_FrzSetTemp = (uint8_t)FRZ_LEVEL_F18;
Zone_st st_Zone;

static const ZoneOnOffTemp_st ary_RefVarOnOffTemp[][(RefVarSet_t)eRefVar_Max] = {
    //  on temp   off temp
    {
        // RT <= 13
        { 500 + 65, 500 + 50 }, // Treasure 3 degree
        { 500 + 65, 500 + 50 }, // Baby 4 degree
        { 500 + 20, 500 + 5 }, // zero 0 degree
    },
    //  on temp   off temp
    {
        // 13 < RT <= 18
        { 500 + 60, 500 + 40 }, // Treasure 3 degree
        { 500 + 70, 500 + 55 }, // Baby 4 degree
        { 500 + 30, 500 + 15 }, // zero 0 degree
    },
    //  on temp   off temp
    {
        // 18 < RT <= 23
        { 500 + 60, 500 + 40 }, // Treasure 3 degree
        { 500 + 70, 500 + 55 }, // Baby 4 degree
        { 500 + 30, 500 + 15 }, // zero 0 degree
    },
    // on temp   off temp
    {
        // 23 < RT <= 28
        { 500 + 65, 500 + 50 }, // Treasure 3 degree
        { 500 + 80, 500 + 65 }, // Baby 4 degree
        { 500 + 35, 500 + 20 }, // zero 0 degree
    },
    // on temp   off temp
    {
        // 28 < RT <= 35
        { 500 + 70, 500 + 50 }, // Treasure 3 degree
        { 500 + 85, 500 + 70 }, // Baby 4 degree
        { 500 + 40, 500 + 25 }, // zero 0 degree
    },
    // on temp   off temp
    {
        // 35 < RT <= 40
        { 500 + 70, 500 + 55 }, // Treasure 3 degree
        { 500 + 85, 500 + 70 }, // Baby 4 degree
        { 500 + 45, 500 + 30 }, // zero 0 degree
    },
    // on temp   off temp
    {
        // 40 < RT
        { 500 + 70, 500 + 55 }, // Treasure 3 degree
        { 500 + 90, 500 + 75 }, // Baby 4 degree
        { 500 + 45, 500 + 30 }, // zero 0 degree
    },
};

static const ZoneOnOffTemp_st ary_RefOnOffTemp[][U8_REF_LEVEL_LENGTH] = {
    //  on temp   off temp
    {
        // RT <= 13
        { 500 + 40, 500 + 30 }, // REF_LEVEL_2
        { 500 + 45, 500 + 35 }, // REF_LEVEL_3
        { 500 + 55, 500 + 45 }, // REF_LEVEL_4
        { 500 + 65, 500 + 55 }, // REF_LEVEL_5
        { 500 + 70, 500 + 60 }, // REF_LEVEL_6
        { 500 + 80, 500 + 70 }, // REF_LEVEL_7
        { 500 + 85, 500 + 75 }, // REF_LEVEL_8
    },
    //  on temp   off temp
    {
        // 13 < RT <= 18
        { 500 + 45, 500 + 35 }, // REF_LEVEL_2
        { 500 + 50, 500 + 40 }, // REF_LEVEL_3
        { 500 + 60, 500 + 50 }, // REF_LEVEL_4
        { 500 + 70, 500 + 60 }, // REF_LEVEL_5
        { 500 + 75, 500 + 65 }, // REF_LEVEL_6
        { 500 + 85, 500 + 75 }, // REF_LEVEL_7
        { 500 + 90, 500 + 80 }, // REF_LEVEL_8
    },
    //  on temp   off temp
    {
        // 18 < RT <= 23
        { 500 + 45, 500 + 35 }, // REF_LEVEL_2
        { 500 + 50, 500 + 40 }, // REF_LEVEL_3
        { 500 + 60, 500 + 50 }, // REF_LEVEL_4
        { 500 + 70, 500 + 60 }, // REF_LEVEL_5
        { 500 + 75, 500 + 65 }, // REF_LEVEL_6
        { 500 + 85, 500 + 75 }, // REF_LEVEL_7
        { 500 + 90, 500 + 80 }, // REF_LEVEL_8
    },
    // on temp   off temp
    {
        // 23 < RT <= 28
        { 500 + 50, 500 + 40 }, // REF_LEVEL_2
        { 500 + 55, 500 + 45 }, // REF_LEVEL_3
        { 500 + 65, 500 + 55 }, // REF_LEVEL_4
        { 500 + 75, 500 + 65 }, // REF_LEVEL_5
        { 500 + 80, 500 + 70 }, // REF_LEVEL_6
        { 500 + 90, 500 + 80 }, // REF_LEVEL_7
        { 500 + 95, 500 + 85 }, // REF_LEVEL_8
    },
    // on temp   off temp
    {
        // 28 < RT <= 35
        { 500 + 55, 500 + 45 }, // REF_LEVEL_2
        { 500 + 60, 500 + 50 }, // REF_LEVEL_3
        { 500 + 70, 500 + 60 }, // REF_LEVEL_4
        { 500 + 80, 500 + 70 }, // REF_LEVEL_5
        { 500 + 85, 500 + 75 }, // REF_LEVEL_6
        { 500 + 95, 500 + 85 }, // REF_LEVEL_7
        { 500 + 100, 500 + 90 }, // REF_LEVEL_8
    },
    // on temp   off temp
    {
        // 35 < RT <= 40
        { 500 + 65, 500 + 55 }, // REF_LEVEL_2
        { 500 + 70, 500 + 60 }, // REF_LEVEL_3
        { 500 + 80, 500 + 70 }, // REF_LEVEL_4
        { 500 + 90, 500 + 80 }, // REF_LEVEL_5
        { 500 + 95, 500 + 85 }, // REF_LEVEL_6
        { 500 + 105, 500 + 95 }, // REF_LEVEL_7
        { 500 + 110, 500 + 100 }, // REF_LEVEL_8
    },
    // on temp   off temp
    {
        // 40 < RT
        { 500 + 65, 500 + 55 }, // REF_LEVEL_2
        { 500 + 75, 500 + 65 }, // REF_LEVEL_3
        { 500 + 85, 500 + 75 }, // REF_LEVEL_4
        { 500 + 95, 500 + 85 }, // REF_LEVEL_5
        { 500 + 100, 500 + 90 }, // REF_LEVEL_6
        { 500 + 110, 500 + 100 }, // REF_LEVEL_7
        { 500 + 115, 500 + 105 }, // REF_LEVEL_8
    },
};

static const ZoneOnOffTime_st ary_RefSnrErrorOnOffTime[] = {
    //  on time   off time
    { 60 * 13, 60 * 118 }, // RT <= 13
    { 60 * 13, 60 * 75 }, // 13 < RT <= 18
    { 60 * 14, 60 * 38 }, // 18 < RT <= 23
    { 60 * 14, 60 * 38 }, // 23 < RT <= 28
    { 60 * 23, 60 * 38 }, // 28 < RT <= 35
    { 60 * 23, 60 * 38 }, // 35 < RT <= 40
    { 60 * 24, 60 * 18 }, // 40 < RT
};

static const ZoneOnOffTime_st ary_FrzSnrErrorOnOffTime[] = {
    //  on time   off time
    { 60 * 13, 60 * 35 }, // RT <= 13
    { 60 * 13, 60 * 30 }, // 13 < RT <= 18
    { 60 * 28, 60 * 23 }, // 18 < RT <= 23
    { 60 * 28, 60 * 23 }, // 23 < RT <= 28
    { 60 * 45, 60 * 17 }, // 28 < RT <= 35
    { 60 * 42, 60 * 21 }, // 35 < RT <= 40
    { 60 * 98, 60 * 16 }, // 40 < RT
};

static const ZoneOnOffTemp_st ary_FrzOnOffTemp[][U8_FRZ_LEVEL_LENGTH] = {
    //  on temp   off temp
    {
        // RT <= 13
        { 500 - 225, 500 - 255 }, // FRZ_LEVEL_F24
        { 500 - 215, 500 - 245 }, // FRZ_LEVEL_F23
        { 500 - 200, 500 - 230 }, // FRZ_LEVEL_F22
        { 500 - 195, 500 - 225 }, // FRZ_LEVEL_F21
        { 500 - 190, 500 - 220 }, // FRZ_LEVEL_F20
        { 500 - 185, 500 - 215 }, // FRZ_LEVEL_F19
        { 500 - 180, 500 - 210 }, // FRZ_LEVEL_F18
        { 500 - 170, 500 - 200 }, // FRZ_LEVEL_F17
        { 500 - 155, 500 - 185 }, // FRZ_LEVEL_F16
    },
    //  on temp   off temp
    {
        // 13 < RT <= 18
        { 500 - 230, 500 - 260 }, // FRZ_LEVEL_F24
        { 500 - 220, 500 - 250 }, // FRZ_LEVEL_F23
        { 500 - 205, 500 - 235 }, // FRZ_LEVEL_F22
        { 500 - 200, 500 - 230 }, // FRZ_LEVEL_F21
        { 500 - 195, 500 - 225 }, // FRZ_LEVEL_F20
        { 500 - 190, 500 - 220 }, // FRZ_LEVEL_F19
        { 500 - 185, 500 - 215 }, // FRZ_LEVEL_F18
        { 500 - 175, 500 - 205 }, // FRZ_LEVEL_F17
        { 500 - 160, 500 - 190 }, // FRZ_LEVEL_F16
    },
    //  on temp   off temp
    {
        // 18 < RT <= 23
        { 500 - 230, 500 - 260 }, // FRZ_LEVEL_F24
        { 500 - 220, 500 - 250 }, // FRZ_LEVEL_F23
        { 500 - 205, 500 - 235 }, // FRZ_LEVEL_F22
        { 500 - 200, 500 - 230 }, // FRZ_LEVEL_F21
        { 500 - 195, 500 - 225 }, // FRZ_LEVEL_F20
        { 500 - 190, 500 - 220 }, // FRZ_LEVEL_F19
        { 500 - 185, 500 - 215 }, // FRZ_LEVEL_F18
        { 500 - 175, 500 - 205 }, // FRZ_LEVEL_F17
        { 500 - 160, 500 - 190 }, // FRZ_LEVEL_F16
    },
    // on temp   off temp
    {
        // 23 < RT <= 28
        { 500 - 230, 500 - 260 }, // FRZ_LEVEL_F24
        { 500 - 220, 500 - 250 }, // FRZ_LEVEL_F23
        { 500 - 205, 500 - 235 }, // FRZ_LEVEL_F22
        { 500 - 200, 500 - 230 }, // FRZ_LEVEL_F21
        { 500 - 195, 500 - 225 }, // FRZ_LEVEL_F20
        { 500 - 190, 500 - 220 }, // FRZ_LEVEL_F19
        { 500 - 185, 500 - 215 }, // FRZ_LEVEL_F18
        { 500 - 175, 500 - 205 }, // FRZ_LEVEL_F17
        { 500 - 160, 500 - 190 }, // FRZ_LEVEL_F16
    },
    // on temp   off temp
    {
        // 28 < RT <= 35
        { 500 - 225, 500 - 265 }, // FRZ_LEVEL_F24
        { 500 - 215, 500 - 255 }, // FRZ_LEVEL_F23
        { 500 - 200, 500 - 240 }, // FRZ_LEVEL_F22
        { 500 - 195, 500 - 235 }, // FRZ_LEVEL_F21
        { 500 - 190, 500 - 230 }, // FRZ_LEVEL_F20
        { 500 - 185, 500 - 225 }, // FRZ_LEVEL_F19
        { 500 - 180, 500 - 220 }, // FRZ_LEVEL_F18
        { 500 - 170, 500 - 210 }, // FRZ_LEVEL_F17
        { 500 - 155, 500 - 195 }, // FRZ_LEVEL_F16
    },
    // on temp   off temp
    {
        // 35 < RT <= 40
        { 500 - 230, 500 - 270 }, // FRZ_LEVEL_F24
        { 500 - 220, 500 - 260 }, // FRZ_LEVEL_F23
        { 500 - 205, 500 - 245 }, // FRZ_LEVEL_F22
        { 500 - 200, 500 - 240 }, // FRZ_LEVEL_F21
        { 500 - 195, 500 - 235 }, // FRZ_LEVEL_F20
        { 500 - 190, 500 - 230 }, // FRZ_LEVEL_F19
        { 500 - 185, 500 - 225 }, // FRZ_LEVEL_F18
        { 500 - 175, 500 - 215 }, // FRZ_LEVEL_F17
        { 500 - 160, 500 - 200 }, // FRZ_LEVEL_F16
    },
    // on temp   off temp
    {
        // 40 < RT
        { 500 - 230, 500 - 270 }, // FRZ_LEVEL_F24
        { 500 - 220, 500 - 260 }, // FRZ_LEVEL_F23
        { 500 - 205, 500 - 245 }, // FRZ_LEVEL_F22
        { 500 - 200, 500 - 240 }, // FRZ_LEVEL_F21
        { 500 - 195, 500 - 235 }, // FRZ_LEVEL_F20
        { 500 - 190, 500 - 230 }, // FRZ_LEVEL_F19
        { 500 - 185, 500 - 225 }, // FRZ_LEVEL_F18
        { 500 - 175, 500 - 215 }, // FRZ_LEVEL_F17
        { 500 - 160, 500 - 200 }, // FRZ_LEVEL_F16
    },
};

static const ZoneOnOffTemp_st ary_TurboFrzOnOffTemp[] = {
    //  on temp   off temp
    { 500 - 285, 500 - 315 }, // RT <= 13
    { 500 - 290, 500 - 320 }, // 13 < RT <= 18
    { 500 - 290, 500 - 320 }, // 18 < RT <= 23
    { 500 - 310, 500 - 340 }, // 23 < RT <= 28
    { 500 - 305, 500 - 345 }, // 28 < RT <= 35
    { 500 - 310, 500 - 350 }, // 35 < RT <= 40
    { 500 - 310, 500 - 350 }, // 40 < RT
};

static const ZoneOnOffTemp_st ary_EnergyFrzOnOffTemp[U8_FRZ_LEVEL_LENGTH] = {
    //  on temp   off temp    // 13 < RT <= 18
    { 500 - 220, 500 - 270 }, // FRZ_LEVEL_F24
    { 500 - 210, 500 - 260 }, // FRZ_LEVEL_F23
    { 500 - 195, 500 - 245 }, // FRZ_LEVEL_F22
    { 500 - 190, 500 - 240 }, // FRZ_LEVEL_F21
    { 500 - 185, 500 - 235 }, // FRZ_LEVEL_F20
    { 500 - 180, 500 - 230 }, // FRZ_LEVEL_F19
    { 500 - 175, 500 - 225 }, // FRZ_LEVEL_F18
    { 500 - 165, 500 - 215 }, // FRZ_LEVEL_F17
    { 500 - 150, 500 - 200 }, // FRZ_LEVEL_F16
};

void Update_RefVarSetTemp(uint8_t u8_SetTemp)
{
    uint8_t set_temp;
    if(u8_SetTemp >= (RefVarSet_t)eRefVar_Max)
    {
        set_temp = (RefVarSet_t)eRefVar_Treasure;
    }
    else
    {
        set_temp = u8_SetTemp;
    }
    u8_RefVarSetTemp = set_temp;
    SetSysParam(SYSPARAM_INFANT_MODE, u8_RefVarSetTemp);
}

void Update_RefSetTemp(uint8_t u8_SetTemp)
{
    uint8_t set_temp;
    if(u8_SetTemp <= U8_REF_LEVEL_MIN)
    {
        set_temp = U8_REF_LEVEL_MIN;
    }
    else if(u8_SetTemp > U8_REF_LEVEL_MAX)
    {
        set_temp = U8_REF_LEVEL_MAX;
    }
    else
    {
        set_temp = u8_SetTemp;
    }
    u8_RefSetTemp = set_temp;
}

void Update_FrzSetTemp(uint8_t u8_SetTemp)
{
    uint8_t set_temp;
    if(u8_SetTemp <= U8_FRZ_LEVEL_MIN)
    {
        set_temp = U8_FRZ_LEVEL_MIN;
    }
    else if((u8_SetTemp > U8_FRZ_LEVEL_MIN) && (u8_SetTemp < U8_FRZ_ON_OFFLEVEL_MIN))
    {
        set_temp = U8_FRZ_ON_OFFLEVEL_MIN;
    }
    else if(u8_SetTemp > U8_FRZ_LEVEL_MAX)
    {
        set_temp = U8_FRZ_LEVEL_MAX;
    }
    else
    {
        set_temp = u8_SetTemp;
    }
    u8_FrzSetTemp = set_temp;
}

static uint8_t Get_RefOnOffSetTempIndex(RoomTempRange_t room_range)
{
    uint8_t ref_onoff_set_temp = U8_REF_LEVEL_MIN;
    uint8_t ref_temp_index = 0;
    UserMode_t user_mode = Get_UserMode();

    if(u8_RefSetTemp >= U8_REF_LEVEL_MIN)
    {
        ref_onoff_set_temp = u8_RefSetTemp;
        if(((u8_FrzSetTemp <= (uint8_t)FRZ_LEVEL_F23) || ((UserMode_t)eTurboFreeze_Mode == user_mode)) &&
            (u8_RefVarSetTemp <= (RefVarSet_t)eRefVar_Baby))
        {
            if((RoomTempRange_t)RT_BELOW13 == room_range)
            {
                if(ref_onoff_set_temp < (uint8_t)REF_LEVEL_6)
                {
                    ref_onoff_set_temp = (uint8_t)REF_LEVEL_6;
                }
            }
            else if(((RoomTempRange_t)RT_BELOW18 == room_range) || ((RoomTempRange_t)RT_BELOW23 == room_range))
            {
                if(ref_onoff_set_temp < (uint8_t)REF_LEVEL_5)
                {
                    ref_onoff_set_temp = (uint8_t)REF_LEVEL_5;
                }
            }
            else if((RoomTempRange_t)RT_BELOW28 == room_range)
            {
                if((RefVarSet_t)eRefVar_Baby == u8_RefVarSetTemp)
                {
                    if(ref_onoff_set_temp < (uint8_t)REF_LEVEL_4)
                    {
                        ref_onoff_set_temp = (uint8_t)REF_LEVEL_4;
                    }
                }
                else if((RefVarSet_t)eRefVar_Treasure == u8_RefVarSetTemp)
                {
                    if(ref_onoff_set_temp < (uint8_t)REF_LEVEL_3)
                    {
                        ref_onoff_set_temp = (uint8_t)REF_LEVEL_3;
                    }
                }
            }
        }
    }

    ref_temp_index = ref_onoff_set_temp - U8_REF_LEVEL_MIN;

    return (ref_temp_index);
}

void Update_TempParameter(void)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t ref_temp_index = 0;
    uint8_t frz_temp_index = 0;
    UserMode_t user_mode = Get_UserMode();
    CoolingEntryMode_t entry_mode = Get_CoolingEntryMode();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    bool b_condensation_mode = Get_CondensationModeState();

    ref_temp_index = Get_RefOnOffSetTempIndex(room_range);
    if(u8_FrzSetTemp >= U8_FRZ_ON_OFFLEVEL_MIN)
    {
        frz_temp_index = u8_FrzSetTemp - U8_FRZ_ON_OFFLEVEL_MIN;
    }

    st_Zone.b_RefSnrError = Get_SensorError((SensorType_t)SENSOR_REF);
    st_Zone.b_RefVarSnrError = Get_SensorError((SensorType_t)SENSOR_VV);
    st_Zone.b_FrzSnrError = Get_SensorError((SensorType_t)SENSOR_FRZ);

    st_Zone.u16_RefVarSnrTemp = Get_SensorValue((SensorType_t)SENSOR_VV);

    if(true == st_Zone.b_RefSnrError)
    {
        st_Zone.u16_RefSnrErrTime++;
        st_Zone.u16_RefOnTime = ary_RefSnrErrorOnOffTime[room_range].u16_OnTime;
        st_Zone.u16_RefOffTime = ary_RefSnrErrorOnOffTime[room_range].u16_OffTime;

        if(st_Zone.u16_RefSnrErrTime <= st_Zone.u16_RefOffTime)
        {
            st_Zone.u16_RefSnrTemp = U16_ZONE_TEMP_MIN;
        }
        else if(st_Zone.u16_RefSnrErrTime <= (st_Zone.u16_RefOffTime + st_Zone.u16_RefOnTime))
        {
            st_Zone.u16_RefSnrTemp = U16_ZONE_TEMP_MAX;
        }
        else
        {
            st_Zone.u16_RefSnrErrTime = 0;
        }
    }
    else
    {
        st_Zone.u16_RefSnrErrTime = 0;
        st_Zone.u16_RefSnrTemp = Get_SensorValue((SensorType_t)SENSOR_REF);
    }

    if(true == st_Zone.b_FrzSnrError)
    {
        st_Zone.u16_FrzSnrErrTime++;
        st_Zone.u16_FrzOnTime = ary_FrzSnrErrorOnOffTime[room_range].u16_OnTime;
        st_Zone.u16_FrzOffTime = ary_FrzSnrErrorOnOffTime[room_range].u16_OffTime;

        if(st_Zone.u16_FrzSnrErrTime <= st_Zone.u16_FrzOffTime)
        {
            st_Zone.u16_FrzSnrTemp = U16_ZONE_TEMP_MIN;
        }
        else if(st_Zone.u16_FrzSnrErrTime <= (st_Zone.u16_FrzOffTime + st_Zone.u16_FrzOnTime))
        {
            st_Zone.u16_FrzSnrTemp = U16_ZONE_TEMP_MAX;
        }
        else
        {
            st_Zone.u16_FrzSnrErrTime = 0;
        }
    }
    else
    {
        st_Zone.u16_FrzSnrErrTime = 0;
        st_Zone.u16_FrzSnrTemp = Get_SensorValue((SensorType_t)SENSOR_FRZ);
    }

    st_Zone.u16_RefOnTemp = ary_RefOnOffTemp[room_range][ref_temp_index].u16_OnTemp;
    st_Zone.u16_RefOffTemp = ary_RefOnOffTemp[room_range][ref_temp_index].u16_OffTemp;
    if(true == b_energy_mode)
    {
        if(RT_BELOW35 == room_range)
        {
            st_Zone.u16_RefOnTemp -= U16_ENERGY_MODE_REF_SNR_ON_TEMP_OFFSET;
            st_Zone.u16_RefOffTemp -= U16_ENERGY_MODE_REF_SNR_OFF_TEMP_OFFSET;
        }
    }
    else if((room_range > (RoomTempRange_t)RT_BELOW13) &&
        ((CoolingEntryMode_t)eMode_DefrostingCompleted == entry_mode) &&
        (0 == st_Zone.u16_RefCoolingCycleNumber))
    {
        st_Zone.u16_RefOnTemp += U16_AFTER_DEFROST_REF_SNR_ON_TEMP_OFFSET;
        st_Zone.u16_RefOffTemp += U16_AFTER_DEFROST_REF_SNR_OFF_TEMP_OFFSET;
    }
    else if((room_range > (RoomTempRange_t)RT_BELOW13) &&
        ((CoolingEntryMode_t)eMode_DefrostingCompleted == entry_mode) &&
        (1 == st_Zone.u16_RefCoolingCycleNumber))
    {
        st_Zone.u16_RefOnTemp += U16_AFTER_DEFROST_REF_SNR_ON_TEMP_OFFSET;
    }
    st_Zone.u16_RefOnTemp = (uint16_t)((int16_t)st_Zone.u16_RefOnTemp + (int16_t)st_Zone.s8_RefOnInchValue);
    st_Zone.u16_RefOffTemp = (uint16_t)((int16_t)st_Zone.u16_RefOffTemp + (int16_t)st_Zone.s8_RefOffInchValue);
    st_Zone.u16_RefOnPlusTemp = st_Zone.u16_RefOnTemp + U16_REF_ON_PLUS_TEMP_OFFSET;

    st_Zone.u16_RefVarOnTemp = ary_RefVarOnOffTemp[room_range][u8_RefVarSetTemp].u16_OnTemp;
    st_Zone.u16_RefVarOffTemp = ary_RefVarOnOffTemp[room_range][u8_RefVarSetTemp].u16_OffTemp;

    if(true == b_energy_mode)
    {
        st_Zone.u16_RefVarOnTemp -= U16_ENERGY_MODE_REFVAR_SNR_ON_TEMP_OFFSET;
        st_Zone.u16_RefVarOffTemp -= U16_ENERGY_MODE_REFVAR_SNR_OFF_TEMP_OFFSET;
    }

    st_Zone.u16_RefVarOnTemp = (uint16_t)((int16_t)st_Zone.u16_RefVarOnTemp + (int16_t)st_Zone.s8_RefVarOnInchValue);
    st_Zone.u16_RefVarOffTemp = (uint16_t)((int16_t)st_Zone.u16_RefVarOffTemp + (int16_t)st_Zone.s8_RefVarOffInchValue);

    if((UserMode_t)eTurboFreeze_Mode == user_mode)
    {
        st_Zone.u16_FrzOnTemp = ary_TurboFrzOnOffTemp[room_range].u16_OnTemp;
        st_Zone.u16_FrzOffTemp = ary_TurboFrzOnOffTemp[room_range].u16_OffTemp;
    }
    else if((true == b_energy_mode) && (RT_BELOW18 == room_range))
    {
        st_Zone.u16_FrzOnTemp = ary_EnergyFrzOnOffTemp[frz_temp_index].u16_OnTemp;
        st_Zone.u16_FrzOffTemp = ary_EnergyFrzOnOffTemp[frz_temp_index].u16_OffTemp;
    }
    else
    {
        st_Zone.u16_FrzOnTemp = ary_FrzOnOffTemp[room_range][frz_temp_index].u16_OnTemp;
        st_Zone.u16_FrzOffTemp = ary_FrzOnOffTemp[room_range][frz_temp_index].u16_OffTemp;
    }
    st_Zone.u16_FrzOnTemp = (uint16_t)((int16_t)st_Zone.u16_FrzOnTemp + (int16_t)st_Zone.s8_FrzOnInchValue);
    st_Zone.u16_FrzOffTemp = (uint16_t)((int16_t)st_Zone.u16_FrzOffTemp + (int16_t)st_Zone.s8_FrzOffInchValue);

    if(true == b_condensation_mode)
    {
        st_Zone.u16_RefOnTemp += U16_CONDENSATION_MODE_REF_SNR_ON_TEMP_OFFSET;
        st_Zone.u16_RefOffTemp += U16_CONDENSATION_MODE_REF_SNR_OFF_TEMP_OFFSET;
        st_Zone.u16_RefOnPlusTemp += U16_CONDENSATION_MODE_REF_SNR_ON_TEMP_OFFSET;

        st_Zone.u16_RefVarOnTemp += U16_CONDENSATION_MODE_REFVAR_SNR_ON_TEMP_OFFSET;
        st_Zone.u16_RefVarOffTemp += U16_CONDENSATION_MODE_REFVAR_SNR_OFF_TEMP_OFFSET;

        st_Zone.u16_FrzOnTemp += U16_CONDENSATION_MODE_FRZ_SNR_ON_TEMP_OFFSET;
        st_Zone.u16_FrzOffTemp += U16_CONDENSATION_MODE_FRZ_SNR_OFF_TEMP_OFFSET;
    }

    if((false == st_Zone.b_RefSnrError) &&
        (st_Zone.u16_RefSnrTemp >= st_Zone.u16_RefOnTemp + U16_OVERLOAD_DEFROST_REF_ON_TEMP_OFFSET))
    {
        st_Zone.u16_RefOverLoadSecCount++;
    }
    else
    {
        st_Zone.u16_RefOverLoadSecCount = 0;
    }
}

bool CoolingCycle_GetRefVarCoolingState(void)
{
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    bool b_power_on_delay = Get_PowerOnDelayRefVarCoolingState();
    CoolingCompState_t comp_state = Get_CoolingCompState();

    if((true == st_Zone.b_RefVarSnrError) || ((true == b_energy_mode) && (eCooling_CompProtect >= comp_state)) || (true == b_power_on_delay))
    {
        st_Zone.b_RefVarCoolingState = false;
    }
    else
    {
        if(st_Zone.u16_RefVarSnrTemp >= st_Zone.u16_RefVarOnTemp)
        {
            st_Zone.b_RefVarCoolingState = true;
        }

        if(st_Zone.u16_RefVarSnrTemp <= st_Zone.u16_RefVarOffTemp)
        {
            st_Zone.b_RefVarCoolingState = false;
        }
    }

    return (st_Zone.b_RefVarCoolingState);
}

static void Update_RefCoolingCycleNumber(void)
{
    if(true == st_Zone.b_RefCooling)
    {
        st_Zone.u16_RefCoolingCycleNumber++;
    }
}

void Clear_RefCoolingCycleNumber(void)
{
    st_Zone.u16_RefCoolingCycleNumber = 0;
}

ZoneCoolingState_t CompOff_GetZoneCoolingState(void)
{
    st_Zone.b_ComOnFrzReachedOffTempOnce = false;
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    bool b_power_on_delay = Get_PowerOnDelayRefVarCoolingState();

    if(st_Zone.u16_FrzSnrTemp >= st_Zone.u16_FrzOnTemp)
    {
        if(true == b_power_on_delay)
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;
            st_Zone.b_RefCooling = false;
        }
        else if(st_Zone.u16_RefSnrTemp >= st_Zone.u16_RefOffTemp)
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_RefFrz;
            st_Zone.b_RefCooling = true;
        }
        else
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;
            Update_RefCoolingCycleNumber();
            st_Zone.b_RefCooling = false;
        }
    }
    else
    {
        if((true == b_energy_mode) || (true == b_power_on_delay))
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
            st_Zone.b_RefCooling = false;
        }
        else if(st_Zone.u16_RefSnrTemp >= st_Zone.u16_RefOnPlusTemp)
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Ref;
            st_Zone.b_RefCooling = true;
        }
        else if(st_Zone.u16_RefSnrTemp <= st_Zone.u16_RefOffTemp)
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
            Update_RefCoolingCycleNumber();
            st_Zone.b_RefCooling = false;
        }
        else
        {
            ; // Stay
        }
    }
    return (st_Zone.zoneCoolingState);
}

ZoneCoolingState_t CompOn_GetZoneCoolingState(void)
{
    bool b_power_on_delay = Get_PowerOnDelayRefVarCoolingState();
    bool b_energy_mode = Get_EnergyConsumptionModeState();

    if(true == b_power_on_delay)
    {
        st_Zone.b_RefCooling = false;
    }
    else
    {
        if(true == b_energy_mode)
        {
            if(st_Zone.u16_RefSnrTemp >= (st_Zone.u16_RefOnTemp + U16_ENERGY_MODE_REF_ON_PLUS_TEMP_OFFSET))
            {
                st_Zone.b_RefCooling = true;
            }
        }
        else
        {
            if(st_Zone.u16_RefSnrTemp >= st_Zone.u16_RefOnTemp)
            {
                st_Zone.b_RefCooling = true;
            }
        }

        if(st_Zone.u16_RefSnrTemp <= st_Zone.u16_RefOffTemp)
        {
            Update_RefCoolingCycleNumber();
            st_Zone.b_RefCooling = false;
        }
    }

    if(st_Zone.u16_FrzSnrTemp > st_Zone.u16_FrzOffTemp)
    {
        if(true == st_Zone.b_RefCooling)
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_RefFrz;
        }
        else
        {
            if(false == st_Zone.b_ComOnFrzReachedOffTempOnce)
            {
                st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;
            }
            else
            {
                st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
            }
        }
    }
    else
    {
        st_Zone.b_ComOnFrzReachedOffTempOnce = true;

        if((ZoneCoolingState_t)eZoneCooling_RefFrz == st_Zone.zoneCoolingState)
        {
            if(false == st_Zone.b_RefCooling)
            {
                st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
            }
        }
        else
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
        }
    }

    return (st_Zone.zoneCoolingState);
}

ZoneCoolingState_t PreCooling_GetFirstCoolingState(void)
{
    uint16_t pre_cooling_frz_off_temp = 0;

    pre_cooling_frz_off_temp = st_Zone.u16_FrzOffTemp - PRE_COOLING_FRZ_SNR_OFF_TEMP_OFFSET;

    if(st_Zone.u16_RefSnrTemp >= st_Zone.u16_RefOnTemp)
    {
        st_Zone.b_RefCooling = true;
    }
    else if(st_Zone.u16_RefSnrTemp <= st_Zone.u16_RefOffTemp)
    {
        st_Zone.b_RefCooling = false;
    }

    if(st_Zone.u16_FrzSnrTemp >= pre_cooling_frz_off_temp)
    {
        if(true == st_Zone.b_RefCooling)
        {
            st_Zone.zonePreCoolingState = (ZoneCoolingState_t)eZoneCooling_RefFrz;
        }
        else
        {
            st_Zone.zonePreCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;
        }
    }
    else
    {
        st_Zone.zonePreCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
    }
    return (st_Zone.zonePreCoolingState);
}

ZoneCoolingState_t PreCooling_GetSecondCoolingState(void)
{
    uint16_t pre_cooling_frz_off_temp = 0;

    pre_cooling_frz_off_temp = st_Zone.u16_FrzOffTemp - PRE_COOLING_FRZ_SNR_OFF_TEMP_OFFSET;

    if(st_Zone.u16_FrzSnrTemp >= pre_cooling_frz_off_temp)
    {
        st_Zone.zonePreCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;
    }
    else
    {
        st_Zone.zonePreCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
    }
    return (st_Zone.zonePreCoolingState);
}

void Update_TempPullDownState(void)
{
    if((st_Zone.u16_RefSnrTemp >= U16_PULL_DOWN_REF_TEMP) && (false == st_Zone.b_RefSnrError) &&
        (st_Zone.u16_RefVarSnrTemp >= U16_PULL_DOWN_REFVAR_TEMP) && (false == st_Zone.b_RefVarSnrError) &&
        (st_Zone.u16_FrzSnrTemp >= U16_PULL_DOWN_FRZ_TEMP) && (false == st_Zone.b_FrzSnrError))
    {
        st_Zone.b_PullDownState = true;
    }
    else
    {
        st_Zone.b_PullDownState = false;
    }
}

bool Get_TempPullDownState(void)
{
    return (st_Zone.b_PullDownState);
}

bool Get_RefTempHighLoadState(void)
{
    bool ref_high_load_flag = false;

    if((false == st_Zone.b_FrzSnrError) &&
        (st_Zone.u16_FrzSnrTemp <= U16_HIGH_LOAD_FRZ_TEMP) &&
        (false == st_Zone.b_RefSnrError) &&
        (st_Zone.u16_RefSnrTemp >= (st_Zone.u16_RefOnTemp + HIGH_LOAD_REF_SNR_ON_PLUS_TEMP_OFFSET)))
    {
        ref_high_load_flag = true;
    }

    return (ref_high_load_flag);
}

bool Get_FrzTempLessZero(void)
{
    bool frz_less_zero = false;

    if((false == st_Zone.b_FrzSnrError) &&
        (st_Zone.u16_FrzSnrTemp < U16_HIGH_LOAD_FRZ_TEMP))
    {
        frz_less_zero = true;
    }

    return (frz_less_zero);
}

bool Get_FrzTempHighLoadState(void)
{
    bool frz_high_load_flag = false;

    if((false == st_Zone.b_FrzSnrError) &&
        (st_Zone.u16_FrzSnrTemp < U16_HIGH_LOAD_FRZ_TEMP) &&
        (st_Zone.u16_FrzSnrTemp >= (st_Zone.u16_FrzOnTemp + HIGH_LOAD_FRZ_SNR_ON_PLUS_TEMP_OFFSET)))
    {
        frz_high_load_flag = true;
    }

    return (frz_high_load_flag);
}

bool Get_RefTempOverLoadDefrost(void)
{
    bool b_state = false;
    uint16_t deforst_temp = Get_SensorValue((SensorType_t)SENSOR_DEFROST);

    if((st_Zone.u16_RefOverLoadSecCount > U16_OVERLOAD_DEFROST_REF_ON_TEMP_TIME_SECOND) &&
        (deforst_temp <= CON_F12P0_DEGREE))
    {
        b_state = true;
    }
    return (b_state);
}

bool Get_FrzTempOverLoadDefrost(void)
{
    bool b_state = false;

    if((false == st_Zone.b_FrzSnrError) &&
        (st_Zone.u16_FrzSnrTemp >= st_Zone.u16_FrzOnTemp + U16_OVERLOAD_DEFROST_FRZ_ON_TEMP_OFFSET))
    {
        b_state = true;
    }
    return (b_state);
}

ZoneCoolingState_t Get_ZoneCoolingState(void)
{
    return (st_Zone.zoneCoolingState);
}

uint8_t Get_RefSetTemp(void)
{
    return (u8_RefSetTemp);
}

uint8_t Get_RefVarSetTemp(void)
{
    return (u8_RefVarSetTemp);
}

uint8_t Get_FrzSetTemp(void)
{
    return (u8_FrzSetTemp);
}

uint16_t Get_RefZoneOnTemp(void)
{
    return (st_Zone.u16_RefOnTemp);
}

uint16_t Get_RefZoneOffTemp(void)
{
    return (st_Zone.u16_RefOffTemp);
}

uint16_t Get_RefOnTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_RefOnInchValue);
}

uint16_t Get_RefOffTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_RefOffInchValue);
}

void Set_RefOnTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_RefOnInchValue = (int8_t)u16_parm;
}

void Set_RefOffTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_RefOffInchValue = (int8_t)u16_parm;
}

uint16_t Get_RefVarZoneOnTemp(void)
{
    return (st_Zone.u16_RefVarOnTemp);
}

uint16_t Get_RefVarZoneOffTemp(void)
{
    return (st_Zone.u16_RefVarOffTemp);
}

uint16_t Get_RefVarOnTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_RefVarOnInchValue);
}

uint16_t Get_RefVarOffTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_RefVarOffInchValue);
}

void Set_RefVarOnTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_RefVarOnInchValue = (int8_t)u16_parm;
}

void Set_RefVarOffTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_RefVarOffInchValue = (int8_t)u16_parm;
}

uint16_t Get_DefPreCoolingRefZoneOffTemp(uint8_t refSetTemp)
{
    uint16_t u16_off_temp;
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t ref_temp_index = 0;

    if(refSetTemp >= U8_REF_LEVEL_MIN)
    {
        ref_temp_index = refSetTemp - U8_REF_LEVEL_MIN;
    }

    u16_off_temp = ary_RefOnOffTemp[room_range][ref_temp_index].u16_OffTemp;
    return (u16_off_temp);
}

uint16_t Get_FrzZoneOnTemp(void)
{
    return (st_Zone.u16_FrzOnTemp);
}

uint16_t Get_FrzZoneOffTemp(void)
{
    return (st_Zone.u16_FrzOffTemp);
}

uint16_t Get_FrzOnTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_FrzOnInchValue);
}

uint16_t Get_FrzOffTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_FrzOffInchValue);
}

void Set_FrzOnTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_FrzOnInchValue = (int8_t)u16_parm;
}

void Set_FrzOffTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_FrzOffInchValue = (int8_t)u16_parm;
}
