
#ifndef __ARCH_UART_H
#define __ARCH_UART_H

#include "syslog.h"

typedef enum
{
    UART_MIIO_COMM = 0, /* used for miio communication */
} UART_COMM_T;

/*
* uart init
* return: 0 success
*/
int uart_init(UART_COMM_T uart);

/*
* uart send 1 byte
* return: 1 success
*         0 fail
*/
int uart_send_byte(UART_COMM_T uart, const unsigned char u8data);

/*
* uart recieve 1 byte
* return: 1 success
*         0 fail
*/
int uart_recv_byte(UART_COMM_T uart, unsigned char *p_u8data);

int uart_recv_buffer_is_empty(void);

#if 1
#define APP_LOG(format, ...) printf(format, ##__VA_ARGS__)
#else
#define APP_LOG(format, ...)
#endif

#endif
