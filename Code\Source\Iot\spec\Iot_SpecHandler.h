/*
 * Iot_SpecHandler.h
 *
 *  Created on: 2023年7月3日
 *      Author: Mi
 */

#ifndef USER_APPLICATION_IOT_SPEC_IOT_SPECHANDLER_H_
#define USER_APPLICATION_IOT_SPEC_IOT_SPECHANDLER_H_


#include "Iot_Spec.h"

#define PLUGIN_LINK_TIMEOUT				120000		//2min
#define FACTORY_TEST_DATA_TIMEOUT		5000		//5s
#define DATE_INFO_LENGTH				10
#define DATE_TO_INT_LENGTH				8

typedef IotExecuteRet_e (*callbackSetFridgeParam)(IotPropName_e prop, TLong value);

typedef IotExecuteRet_e (*callbackInvokeAction)(IotActionName_e action, TLong* value, TByte length);

void sync_fridge_params_set_by_iot(callbackSetFridgeParam callback);
void sync_action_cached_by_iot(callbackInvokeAction callback);
TBool is_wait_dev_async_execute(void);
TBool response_for_last_execute(char *pResult);
TBool report_properties_changed_to_iot(char *pResult);
TBool report_event_to_iot(char *pResult);
void set_network_state(const char* pState);
void update_current_date(const char* pDate);
void set_wifi_arch_platform_state(const char* pState);
TBool is_local_date_valid(void);

void countdown_for_plugin_link(void);
void receive_plugin_connect(void);
void receive_plugin_disconnect(void);

int execute_property_operation_async(const char *pbuf, int buf_sz, bool set_value, char *presult);
int execute_action_invocation_async(const char *pbuf, int buf_sz, char *presult);

#endif /* USER_APPLICATION_IOT_SPEC_IOT_SPECHANDLER_H_ */
