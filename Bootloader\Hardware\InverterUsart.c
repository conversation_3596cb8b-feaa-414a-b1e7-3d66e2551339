/*!
 * @file
 * @brief Initialize MCU.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <string.h>
#include "Adpt_Usart.h"
#include "InverterUsart.h"
#include "UartComm.h"

static UartComm_st st_UartInverter;

static void InverterEnableTx(bool enable)
{
    if(enable)
    {
        return InverterUart_EnalbeTxInterrupts();
    }
    return InverterUart_DisalbeTxInterrupts();
}

static void InverterEnableRx(bool enable)
{
    if(enable)
    {
        return InverterUart_EnalbeRxInterrupts();
    }
    return InverterUart_DisalbeRxInterrupts();
}

void Init_UartInverter(void)
{
    UartCommInit(&st_UartInverter, U8_INVERTER_SEND_TIMEOUT_MILLISECOND,
                 U8_INVERTER_RECV_TIMEOUT_MILLISECOND, U16_RECE_DATA_ERROR_TIME_WITH_100MS);
    st_UartInverter.uart_ops.enableRx = InverterEnableRx;
    st_UartInverter.uart_ops.enableTx = InverterEnableTx;
    st_UartInverter.uart_ops.sendOneData = InverterUart_SendOneData;
    st_UartInverter.fw.fwAddr = FIREWARE_INVERTER_ADDR;
    st_UartInverter.fw.ops.transmit = UartCommTransmit;
    st_UartInverter.fw.ops.receive = UartCommReceive;
    st_UartInverter.fw.enable = true;
    FirewareCommRegister(&st_UartInverter.fw);
}

void Handle_UartInverterSendData(void)
{
    Handle_UartCommSendData(&st_UartInverter);
}

void Handle_UartInverterReceData(const uint8_t u8_rece_data)
{
    Handle_UartCommReceData(&st_UartInverter, u8_rece_data);
}

void Handle_UartInverterOverTime(void)
{
    Handle_UartCommOverTime(&st_UartInverter);
}

