/*!
 * @file
 * @brief This file defines public constants, types and functions for the fault code.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef FAULT_CODE_H
#define FAULT_CODE_H

#include <stdint.h>
#include <stdbool.h>
#include "Core_Types.h"

#define U8_FAULT_CODE_NUMNER (uint8_t)14

enum
{
    eFCode_IotByte0 = 0,
    eFCode_IotByte1,
    eFCode_IotByte2,
    eFCode_IotByte3,
    eFCode_DoorSwitch,
    eFCode_Inverter,
    eFCode_FactoryDevice,
    eFCode_Other,
    eFCode_Max
};
typedef uint8_t FaultCodeByteIndex_t;

enum
{
    FAULT_REF_SNR, // 冷藏传感器故障 rS
    FAULT_REF_VAR_SNR, // 母婴室传感器故障 US
    FAULT_FRZ_SNR, // 冷冻传感器故障 FS
    FAULT_DEF_SNR, // 冷冻化霜传感器故障 Fd
    FAULT_ROOM_SNR, // 环境温度传感器故障 ES
    FAULT_FRZ_FAN, // 冷冻风机故障 FF
    FAULT_COOL_FAN, // 冷凝风机故障 LF
    FAULT_DEF_FUNC, // 化霜故障 Ed
    FAULT_INVERTER_COMM, // 变频板通信故障 C0
    FAULT_INVERTER, // 变频故障 C1~CC
    FAULT_DOOR_REF_LEFT, // 冷藏左门超时 dL
    FAULT_DOOR_REF_RIGHT, // 冷藏右门超时 dr
    FAULT_DOOR_FRZ_LEFT, // 冷冻左门超时 dS
    FAULT_DOOR_FRZ_RIGHT, // 冷冻右门超时 dF
    FAULT_WIFI_MODULE, // WiFi模组不匹配
    FAULT_HUM_SNR, // 湿度传感器故障 CH
    FAULT_FACTORY_DEFHEATER,
    FAULT_FACTORY_VBHEATER,
    FAULT_FACTORY_REFLAMP,
    FAULT_FACTORY_FRZFAN,
    FAULT_FACTORY_CONDFAN,
    FAULT_FACTORY_DAMPER,
    FAULT_MAX
};
typedef uint8_t FaultType_t;

typedef struct
{
    uint8_t u8_FaultType;
    uint8_t u8_HighDisp;
    uint8_t u8_LowDisp;
    uint8_t u8_ByteNum;
    uint8_t u8_BitValue;
} DispFault_st;

typedef struct
{
    uint8_t ary_FaultCodebuffer[U8_FAULT_CODE_NUMNER];
    uint8_t ary_ActiveFaultbuffer[U8_FAULT_CODE_NUMNER];
    MyWord_st st_DispData;
    uint8_t u8_FaultCodeOrder;
    uint8_t u8_FaultNumber;
    uint8_t u8_ActiveFaultNumber;
} DispFaultData_st;

/*!
 * @brief 1s period
 */
void Collect_FaultCode(void);
uint8_t Get_FaultCodeByte(uint8_t index);
/*!
 * @brief 0.5s period
 */
uint16_t Get_CurrentDispFaultCode(void);
uint8_t Get_FaultCodeNumber(void);
#endif
