/*
 * Iot_Spec.h
 *
 *  Created on: 2023年7月3日
 *      Author: Mi
 */

#ifndef USER_APPLICATION_IOT_SPEC_IOT_SPEC_H_
#define USER_APPLICATION_IOT_SPEC_IOT_SPEC_H_

//#include "UserTypes.h"
//#include "Typedefine.h"
#include "IotUsr.h"

//property status, can updated
#define PROP_FLAG_NOT_REPORT_AFTER_POWER_ON 0x1
#define PROP_FLAG_NOT_REPORT_AFTER_BIND 0x2
#define PROP_FLAG_UPDATED_BY_DEV 0x4
#define PROP_FLAG_UPDATED_BY_IOT 0x8
#define PROP_FLAG_UPDATED_WHEN_APP_CONNECT 0x10

//property policy, read only
#define PROP_POLICY_BASIC_INFO 0x1
#define PROP_POLICY_WRITE_ONLY 0x4
#define PROP_POLICY_REPORT_WHEN_VALUE_DUP 0x80

//define device event
#define EVENT_HAS_NONE 0x0000
#define EVENT_CONNECT_NETWORK 0x0010
#define EVENT_DISCONNECT_NETWORK 0x0020
#define EVENT_FIRST_CONNECT_NETWORK 0x0040

#define PROP_STRING_MAX_LENGTH 20
#define PROP_SETPROPERTY_LIST_LENGTH 20

#define ACTION_INPRAM_PROP_MAX_LENGTH 5

//#define DEFAULT_FACTORY_REPORT_DEADLINE		19700101
#define MIN_CURRENT_DATE 20230701

#define NET_STATE_OFFLINE "offline" //连接中（或掉线）
#define NET_STATE_LOCAL "local" //连上路由器但未连上小米云服务器
#define NET_STATE_CLOUD "cloud" //连上小米云服务器
#define NET_STATE_UPDATING "updating" //固件升级中
#define NET_STATE_UAP "uap" //等待连接
#define NET_STATE_UNPROV "unprov" //关闭wifi（半小时未快连）
#define NET_STATE_UPDATING_AUTO "updating auto" //固件自动升级中
#define NET_STATE_UPDATING_FORCE "updating force" //固件强制升级中

typedef enum
{
    ZM_APP_NET_STATE_NONE = 0,
    ZM_APP_NET_STATE_OFFLINE,
    ZM_APP_NET_STATE_LOCAL,
    ZM_APP_NET_STATE_CLOUD,
    ZM_APP_NET_STATE_UPDATING,
    ZM_APP_NET_STATE_UAP,
    ZM_APP_NET_STATE_UNPROV,
    ZM_APP_NET_STATE_UPDATING_AUTO,
    ZM_APP_NET_STATE_UPDATING_FORCE,
} net_state_e;

//data transfer direction
typedef enum
{
    IOT_DIR_IOT_TO_DEV = 1,
    IOT_DIR_DEV_TO_IOT,
} IotDataDir_e;

typedef enum
{
    IOT_EXECUTE_OK,
    IOT_EXECUTE_FAIL,
    IOT_EXECUTE_DUP_VALUE,
    IOT_EXECUTE_INVALID_VALUE,
    IOT_EXECUTE_CANNOT_READ,
    IOT_EXECUTE_CANNOT_WRITE,
    IOT_EXECUTE_INDIRECT_VALUE,
    IOT_EXECUTE_NEED_WAIT,
    IOT_EXECUTE_DEV_FAIL,
    IOT_EXECUTE_CHILD_PROTECTED,
} IotExecuteRet_e;

typedef enum
{
    //global param
    IOT_PROP_MIN,
    IOT_PROP_FAULT = IOT_PROP_MIN,
    IOT_PROP_MODE,
    IOT_PROP_DOOR_ALARM,
    IOT_PROP_REF_TEMP,
    IOT_PROP_REF_SET,
    IOT_PROP_FRZ_TEMP,
    IOT_PROP_FRZ_SET,
    IOT_PROP_SUPER_FRZ_SET,
    IOT_PROP_REFVAR_TEMP,
    IOT_PROP_REFVAR_SET,
    IOT_DL_PROP_DEFROST_STATUS, //1
    IOT_DL_PROP_DEFROST_TEMP,
    IOT_DL_PROP_REF_DAMPER,
    IOT_DL_PROP_FRZ_FAN,
    IOT_DL_PROP_COMP_SPEED,
    IOT_DL_PROP_COOL_FAN, //6
    IOT_DL_PROP_VAR_DAMPER,
    IOT_DL_PROP_REFVAR_DAMPER,
    IOT_DL_PROP_ROOMTTEMP,
    IOT_DL_PROP_HUMIDITY,
    IOT_PROP_DOOR_STATE,
    IOT_PROP_FACTORY_SN,
    IOT_PROP_FACTORY_DATA,
    IOT_PROP_MAX,

    //datalogging param
    IOT_DL_PROP_MIN,
    IOT_FCT_PROP_MIN,
} IotPropName_e;

typedef enum
{
    IOT_TYPE_BYTE,
    IOT_TYPE_WORD,
    IOT_TYPE_BOOL,
    IOT_TYPE_LONG,
    IOT_TYPE_STRING,
    IOT_TYPE_FLOAT,
} IotPropType_e;

typedef struct
{
    TWord key;
    TWord value;
} IotPropList_t;

typedef struct
{
    IotPropName_e name;
    TByte size;
    IotPropList_t *pData;
} IotPropListTable_e;

typedef struct
{
    void *p_DevDataAddress;
    void *p_IotDataAddress;
    IotPropType_e propType;
    TByte siid;
    TByte piid;
    TByte propPolicy;
    TByte propState;
} IotPropState_t;

typedef struct
{
    TBool bWritable;
    TLong minVal;
    TLong maxVal;
    TWord stepVal;
} IotPropCheckValue_t;

typedef enum
{
    IOT_ACTION_MIN,
    IOT_ACTION_RESET = IOT_ACTION_MIN,
    IOT_ACTION_PLUGIN_CONNECT,
    IOT_ACTION_PLUGIN_DISCONNECT,
    IOT_ACTION_MAX,
} IotActionName_e;

typedef enum
{
    IOT_EVENT_MIN,
    IOT_EVENT_WASH_END = IOT_EVENT_MIN,
    IOT_EVENT_START_DATALOGGING,
    IOT_EVENT_END_DATALOGGING,
    IOT_EVENT_DATA_REPORT,
    IOT_EVENT_CLEAN_COMPLETED,
    IOT_EVENT_MAX,
} IotEventName_e;

typedef struct
{
    TByte siid;
    TByte aiid;
    TByte param_nums;
    TByte out_nums;
    IotPropName_e *props;
    IotPropName_e *results;
} IotActionProtoType_t;

typedef struct
{
    TByte siid;
    TByte eiid;
    TByte param_nums;
    const IotPropName_e *params;
} IotEventProtoType_t;

typedef struct
{
    IotPropType_e propType;
    TLong lValue;
    TFSingle fValue;
    TChar sValue[PROP_STRING_MAX_LENGTH + 1];
} IotGeneralPropVal_t;

typedef struct
{
    TByte siid;
    TByte piid;
    IotPropName_e prop;
    IotGeneralPropVal_t *pValue;
    TByte code;
    TBool ready;
} IotPropOper_result_t;

typedef struct
{
    TByte siid;
    TByte aiid;
    IotActionName_e action;
    TByte code;
    TBool ready;
} IotAction_result_t;

typedef struct
{
    uint32_t error1;
    uint8_t mode;
    int8_t refTemp;
    int8_t refSet;
    int8_t frzTemp;

    int8_t frzSet;
    int8_t refvarTemp;
    int8_t refvarSet;
    int8_t ver1;
} IotGlobalInfo_t;

typedef struct
{
    uint32_t error1;
    uint8_t mode;
    uint8_t doorAlarm;
    int8_t refTemp;
    int8_t refSet;

    int8_t frzTemp;
    int8_t frzSet;
    int8_t superfrzSet;
    int8_t refvarTemp;

    int8_t refvarSet;
    uint8_t defroststatus;
    int8_t defrosttemp;
    uint8_t refdamper;

    uint32_t compspeed;
    uint8_t frzfanlevel;
    uint8_t coolfanlevel;
    uint8_t vardamper;
    uint8_t refvardamper;

    int8_t roomtemp;
    int8_t humidity;
    int8_t doorState;
    int8_t ver1[1];
    uint8_t sn[PROP_STRING_MAX_LENGTH];
} IotDevInfo_t;

typedef struct
{
    IotGlobalInfo_t dev_global;
    IotDevInfo_t dev_down;

    IotGlobalInfo_t iot_global;
    IotDevInfo_t iot_down;

    //IOT setproperties execute asynced,  one instruction can set multi prop
    IotPropOper_result_t setprop_commands[PROP_SETPROPERTY_LIST_LENGTH];
    TByte commands_num;

    //IOT action execute asynced, at most one action received per getdown poll
    IotAction_result_t action_command;
    TBool action_trig;

    TBool plugin_connect;
    TBool init_data;
    TBool is_arch_platform;
    TBool module_ready;
    TWord event_state;
    net_state_e net_state;

} IotFridgeHouse_t;

typedef IotExecuteRet_e (*callbackCustomGetFridgeParam)(IotPropName_e prop, IotGeneralPropVal_t *pValue);

extern IotFridgeHouse_t gIotFridge;

void initIotFridgeHouse(void);
IotExecuteRet_e set_fridge_param(IotPropName_e propName, IotGeneralPropVal_t *pValue, IotDataDir_e dir);
IotExecuteRet_e get_fridge_param(IotPropName_e propName, IotGeneralPropVal_t *pValue, IotDataDir_e dir);
//TBool map_list_idx_and_val(IotPropName_e propName, TLong srcValue, TLong* dstValue, TBool id_to_val);
TSWord map_result_to_iot_code(TByte result);

IotPropName_e get_propname_by_spec_id(TByte siid, TByte piid);
IotActionName_e get_actionname_by_spec_id(TByte siid, TByte piid);
TByte get_spec_siid_by_propname(IotPropName_e propName);
TByte get_spec_piid_by_propname(IotPropName_e propName);

TByte get_event_report_param_num(IotEventName_e eventName);
IotExecuteRet_e get_event_report_param_value(IotEventName_e eventName, TByte index, callbackCustomGetFridgeParam callback, IotGeneralPropVal_t *pValue);
TByte get_spec_siid_by_eventname(IotEventName_e eventName);
TByte get_spec_eiid_by_eventname(IotEventName_e eventName);
TByte get_spec_piid_by_event_index(IotEventName_e eventName, TByte index);

TBool prepare_iot_setprop_execute(TByte siid, TByte piid, IotGeneralPropVal_t *pValue);
void reset_iot_setprop_excute_state(void);
void finish_iot_setprop_execute(IotPropName_e propName, TByte code);

TBool prepare_iot_action_execute(TByte siid, TByte aiid);
TBool prepare_iot_action_param(TByte siid, TByte piid, TByte index, IotGeneralPropVal_t *pValue);
void finish_iot_action_execute(IotActionName_e actionName, TByte code);
TLong *get_action_fridge_params(IotActionName_e actionName, TByte *len);
TByte get_action_fridge_out_param_num(IotActionName_e actionName);
IotExecuteRet_e get_action_fridge_out_param_value(IotActionName_e actionName, TByte index, IotGeneralPropVal_t *pValue);
TByte get_spec_piid_by_action_out_index(IotActionName_e actionName, TByte index);
TBool need_sync_fridge_param_to_dev(IotExecuteRet_e set_result, IotPropName_e propName);

TBool is_fridge_param_policy_matched(IotPropName_e propName, TByte policy);
TBool is_fridge_param_updated(IotPropName_e propName);
void clear_fridge_param_updated(IotPropName_e propName);
void set_fridge_param_updated_by_event(IotPropName_e propName);
TBool is_result_successed(IotExecuteRet_e result);
TBool is_remote_control_allowed(void);
//void reset_all_prop_to_unreported(void);
void reset_all_prop_updated_by_plugin(void);
void set_prop_updated_by_plugin(IotPropName_e propName);
TBool is_prop_updated_by_plugin(IotPropName_e propName);
TBool is_allowed_execute_ota(void);

#define set_fridge_param_by_iot(propName, value) set_fridge_param(propName, value, IOT_DIR_IOT_TO_DEV)

#define set_fridge_param_by_dev(propName, value) set_fridge_param(propName, value, IOT_DIR_DEV_TO_IOT)

#define get_fridge_param_from_dev(propName, pValue) get_fridge_param(propName, pValue, IOT_DIR_DEV_TO_IOT)

#define get_fridge_param_setby_iot(propName, pValue) get_fridge_param(propName, pValue, IOT_DIR_IOT_TO_DEV)

#define assemble_fridge_param_command(siid_val, piid_val, prop_val, ret)                                                          \
    do                                                                                                                            \
    {                                                                                                                             \
        gIotFridge.setprop_commands[gIotFridge.commands_num].siid = siid_val;                                                     \
        gIotFridge.setprop_commands[gIotFridge.commands_num].piid = piid_val;                                                     \
        gIotFridge.setprop_commands[gIotFridge.commands_num].prop = prop_val;                                                     \
        gIotFridge.setprop_commands[gIotFridge.commands_num].code = ret;                                                          \
        gIotFridge.setprop_commands[gIotFridge.commands_num].ready = need_sync_fridge_param_to_dev(ret, prop_val) ? false : true; \
        gIotFridge.commands_num++;                                                                                                \
    } while(0)

#define is_module_ready() \
    gIotFridge.module_ready

#define set_module_ready() \
    gIotFridge.module_ready = TRUE

#define set_plugin_connected() \
    gIotFridge.plugin_connect = TRUE

#define set_plugin_disconnected() \
    gIotFridge.plugin_connect = FALSE

#define is_plugin_connected() \
    gIotFridge.plugin_connect

#define trig_fridge_event(event) \
    gIotFridge.event_state |= event

#define clear_fridge_event(event) \
    gIotFridge.event_state &= ~event

#define is_fridge_event_exist(event) \
    (gIotFridge.event_state & event) > 0

#define is_dev_connected_iot_svr() \
    gIotFridge.net_state == ZM_APP_NET_STATE_CLOUD

#define is_dev_net_not_init() \
    gIotFridge.net_state == ZM_APP_NET_STATE_NONE

#define get_dev_net_state() \
    gIotFridge.net_state

#define set_dev_net_state(state) \
    gIotFridge.net_state = state

#define is_dev_has_inited() \
    gIotFridge.init_data

#define set_dev_inited() \
    gIotFridge.init_data = TRUE

#define get_fridge_param_response_nums() \
    gIotFridge.commands_num

#define get_fridge_param_response(id) \
    &(gIotFridge.setprop_commands[id])

#define is_fridge_param_ready(id) \
    gIotFridge.setprop_commands[id].ready

#define is_fridge_action_cached() \
    gIotFridge.action_trig

#define is_fridge_action_ready() \
    gIotFridge.action_command.ready

#define get_fridge_action_cached() \
    ((IotAction_result_t *)(&(gIotFridge.action_command)))

#define reset_fridge_action_cached() \
    gIotFridge.action_trig = FALSE

#define set_wifi_arch_platform() \
    gIotFridge.is_arch_platform = true;

#define is_wifi_arch_platform() \
    gIotFridge.is_arch_platform

#endif /* USER_APPLICATION_IOT_SPEC_IOT_SPEC_H_ */
