/*
 * Iot_Spec.c
 *
 *  Created on: 2023骞?7鏈?3鏃?
 *      Author: Mi
 */

#include <string.h>
#include "arch_os.h"
#include "Iot_Spec.h"

IotFridgeHouse_t gIotFridge;
TLong gActionParams[ACTION_INPRAM_PROP_MAX_LENGTH];

void initIotFridgeHouse(void)
{
    memset(&gIotFridge, 0, sizeof(gIotFridge));
}

static const IotPropCheckValue_t sIotPropValueCheckTable[IOT_PROP_MAX] = {
    //IOT_PROP_FAULT
    { FALSE, 0, 65535, 1 },

    //IOT_PROP_MODE
    { TRUE, 0, 3, 0 },

    //IOT_PROP_DOOR_ALARM
    { FALSE, 0, 255, 0 },

    //IOT_PROP_REF_TEMP
    { FALSE, -50, 100, 1 },

    //IOT_PROP_REF_SET
    { TRUE, 2, 8, 1 },

    //IOT_PROP_FRZ_TEMP
    { FALSE, -50, 100, 1 },

    //IOT_PROP_FRZ_SET
    { TRUE, -24, -16, 1 },

    //IOT_PROP_SUPER_FRZ_SET
    { TRUE, -30, -16, 1 },

    //IOT_PROP_REFVAR_TEMP
    { FALSE, -50, 100, 1 },

    //IOT_PROP_REFVAR_SET
    { TRUE, 0, 2, 1 },

    //IOT_DL_PROP_DEFROST_STATUS
    { FALSE, 0, 1, 1 },

    //IOT_DL_PROP_DEFROST_TEMP
    { FALSE, -50, 100, 1 },

    //IOT_DL_PROP_REF_DAMPER
    { FALSE, 0, 1, 1 },

    //IOT_DL_PROP_FRZ_FAN
    { FALSE, 0, 20, 1 },

    //IOT_DL_PROP_COMP_SPEED
    { FALSE, 0, 10000, 1 },

    //IOT_DL_PROP_COOL_FAN
    { FALSE, 0, 20, 1 },

    //IOT_DL_PROP_VAR_DAMPER
    { FALSE, 0, 1, 1 },

    //IOT_DL_PROP_REFVAR_DAMPER
    { FALSE, 0, 1, 1 },

    //IOT_DL_PROP_ROOMTTEMP
    { FALSE, -50, 100, 1 },

    //IOT_DL_PROP_HUMIDITY
    { FALSE, 0, 100, 1 },

    //IOT_PROP_DOOR_STATE
    { FALSE, 0, 65535, 1 },
    //IOT_PROP_FACTORY_SN
    { TRUE, 0, 65535, 1 },

    //IOT_PROP_FACTORY_DATA
    { FALSE, 0, 65535, 1 },
};

static const IotPropListTable_e propListTable[] = {
    //	{IOT_PROP_TEMPERATURE, sizeof(temperature_list)/sizeof(IotPropList_t), (IotPropList_t*)&temperature_list},
    //{IOT_PROP_SPIN_SPEED, sizeof(spin_speed_list)/sizeof(IotPropList_t), (IotPropList_t*)&spin_speed_list},
};

static IotPropState_t sIotPropStateTable[IOT_PROP_MAX] = {
    //Dev Info

    //IOT_PROP_FAULT
    {
        &gIotFridge.dev_down.error1, &gIotFridge.iot_down.error1, IOT_TYPE_WORD, 2, 1, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },
    //IOT_PROP_MODE
    {
        &gIotFridge.dev_down.mode, &gIotFridge.iot_down.mode, IOT_TYPE_BYTE, 2, 2, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_WHEN_APP_CONNECT },
    //IOT_PROP_DOOR_ALARM
    {
        &gIotFridge.dev_down.doorAlarm, &gIotFridge.iot_down.doorAlarm, IOT_TYPE_BYTE, 2, 3, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },
    //IOT_PROP_REF_TEMP
    {
        &gIotFridge.dev_down.refTemp, &gIotFridge.iot_down.refTemp, IOT_TYPE_BYTE, 3, 1, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },
    //IOT_PROP_REF_SET
    {
        &gIotFridge.dev_down.refSet, &gIotFridge.iot_down.refSet, IOT_TYPE_BYTE, 3, 2, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_WHEN_APP_CONNECT },
    //IOT_PROP_FRZ_TEMP
    {
        &gIotFridge.dev_down.frzTemp, &gIotFridge.iot_down.frzTemp, IOT_TYPE_BYTE, 4, 1, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },
    //IOT_PROP_FRZ_SET
    {
        &gIotFridge.dev_down.frzSet, &gIotFridge.iot_down.frzSet, IOT_TYPE_BYTE, 4, 2, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_WHEN_APP_CONNECT },
    //IOT_PROP_SUPER_FRZ_SET
    {
        &gIotFridge.dev_down.superfrzSet, &gIotFridge.iot_down.superfrzSet, IOT_TYPE_BYTE, 4, 3, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_WHEN_APP_CONNECT },
    //IOT_PROP_REFVAR_TEMP
    {
        &gIotFridge.dev_down.refvarTemp, &gIotFridge.iot_down.refvarTemp, IOT_TYPE_BYTE, 6, 1, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },
    //IOT_PROP_REFVAR_SET
    {
        &gIotFridge.dev_down.refvarSet, &gIotFridge.iot_down.refvarSet, IOT_TYPE_BYTE, 6, 3, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_WHEN_APP_CONNECT },
    //IOT_DL_PROP_DEFROST_STATUS
    {
        &gIotFridge.dev_down.defroststatus, &gIotFridge.iot_down.defroststatus, IOT_TYPE_BYTE, 10, 1, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },
    //IOT_DL_PROP_DEFROST_TEMP
    {
        &gIotFridge.dev_down.defrosttemp, &gIotFridge.iot_down.defrosttemp, IOT_TYPE_BYTE, 10, 2, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },

    //IOT_DL_PROP_REF_DAMPER
    {
        &gIotFridge.dev_down.refdamper, &gIotFridge.iot_down.refdamper, IOT_TYPE_BYTE, 10, 3, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },

    //IOT_DL_PROP_FRZ_FAN
    {
        &gIotFridge.dev_down.frzfanlevel, &gIotFridge.iot_down.frzfanlevel, IOT_TYPE_BYTE, 10, 4, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },

    //IOT_DL_PROP_COMP_SPEED
    {
        &gIotFridge.dev_down.compspeed, &gIotFridge.iot_down.compspeed, IOT_TYPE_WORD, 10, 5, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },

    //IOT_DL_PROP_COOL_FAN
    {
        &gIotFridge.dev_down.coolfanlevel, &gIotFridge.iot_down.coolfanlevel, IOT_TYPE_BYTE, 10, 6, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },

    //IOT_DL_PROP_VAR_DAMPER
    {
        &gIotFridge.dev_down.vardamper, &gIotFridge.iot_down.vardamper, IOT_TYPE_BYTE, 10, 7, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },

    //IOT_DL_PROP_REFVAR_DAMPER
    {
        &gIotFridge.dev_down.refvardamper, &gIotFridge.iot_down.refvardamper, IOT_TYPE_BYTE, 10, 8, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },

    //IOT_DL_PROP_ROOMTTEMP
    {
        &gIotFridge.dev_down.roomtemp, &gIotFridge.iot_down.roomtemp, IOT_TYPE_BYTE, 10, 9, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },

    //IOT_DL_PROP_HUMIDITY
    {
        &gIotFridge.dev_down.humidity, &gIotFridge.iot_down.humidity, IOT_TYPE_BYTE, 10, 10, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },

    //IOT_PROP_DOOR_STATE
    {
        &gIotFridge.dev_down.doorState, &gIotFridge.iot_down.doorState, IOT_TYPE_BYTE, 10, 11, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },
    //IOT_PROP_FACTORY_SN
    {
        &gIotFridge.dev_down.sn, &gIotFridge.iot_down.sn, IOT_TYPE_STRING, 1, 5, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV },
    //IOT_PROP_FACTORY_DATA
    {
        NULL, NULL, IOT_TYPE_STRING, 254, 1, PROP_POLICY_BASIC_INFO, PROP_FLAG_UPDATED_BY_DEV }
};

#define PROPLIST_TABLE_LENTH (sizeof(propListTable) / sizeof(propListTable[0]))

//static const IotPropName_e sIotCleanActionProps[] = {IOT_PROP_G_MATERIAL_CLEAN_SLOT};
//static const IotPropName_e sIotStartShakeActionProps[] = {};

static const IotActionProtoType_t sIotActionTable[IOT_ACTION_MAX] = {
    //IOT_ACTION_RESET
    { 7, 1, 0, 0, NULL, NULL },

    //IOT_ACTION_PLUGIN_CONNECT
    { 9, 1, 0, 0, NULL, NULL },

    //IOT_ACTION_PLUGIN_DISCONNECT
    { 9, 2, 0, 0, NULL, NULL },

};

static const IotPropName_e datalogging_list[] = {};

static const IotPropName_e factorydata_list[] = {};

static const IotEventProtoType_t sIotEventTable[IOT_EVENT_MAX] = {
    //IOT_EVENT_WASH_END
    { 2, 1, 0, NULL },

    //IOT_EVENT_START_DATALOGGING
    { 8, 1, 11, (const IotPropName_e *)&datalogging_list },

    //IOT_EVENT_END_DATALOGGING
    { 8, 2, 12, (const IotPropName_e *)&datalogging_list },

    //IOT_EVENT_DATA_REPORT
    { 10, 1, 14, (const IotPropName_e *)&factorydata_list },

    //IOT_EVENT_CLEAN_COMPLETED
    { 7, 1, 0, NULL },
};

static TBool check_fridge_param_valid(IotPropName_e propName, IotGeneralPropVal_t *pValue)
{
    if(sIotPropStateTable[propName].propType != pValue->propType)
        return FALSE;

    if(pValue->propType == IOT_TYPE_STRING)
        return TRUE;

    TLong value = pValue->lValue;
    if(value >= sIotPropValueCheckTable[propName].minVal && value <= sIotPropValueCheckTable[propName].maxVal)
    {
        return TRUE; //当有步进>1的值时，需要放出下面的代码
        TWord offsetValue;

        if(sIotPropValueCheckTable[propName].stepVal == 0)
            return TRUE;

        offsetValue = value - sIotPropValueCheckTable[propName].minVal;
        if(offsetValue == 0)
        {
            return TRUE;
        }

        if(offsetValue % sIotPropValueCheckTable[propName].stepVal == 0)
        {
            return TRUE;
        }
    }

    return FALSE;
}

static void copy_fridge_param_from_iot_to_dev(IotPropName_e propName)
{
    void *pDestPtr = sIotPropStateTable[propName].p_DevDataAddress;
    void *pSrcPtr = sIotPropStateTable[propName].p_IotDataAddress;

    switch(sIotPropStateTable[propName].propType)
    {
        case IOT_TYPE_BYTE:
        case IOT_TYPE_BOOL:
        {
            *((TByte *)pDestPtr) = *((TByte *)pSrcPtr);
            break;
        }
        case IOT_TYPE_WORD:
        {
            *((TWord *)pDestPtr) = *((TWord *)pSrcPtr);
            break;
        }
        case IOT_TYPE_LONG:
        {
            *((TLong *)pDestPtr) = *((TLong *)pSrcPtr);
            break;
        }
        case IOT_TYPE_STRING:
        {
            strncpy((TChar *)pDestPtr, (TChar *)pSrcPtr, strlen((TChar *)pSrcPtr));
            break;
        }
        default:
            break;
    }
}

void reset_all_prop_updated_by_plugin(void)
{
    for(IotPropName_e prop = IOT_PROP_MIN; prop < IOT_PROP_MAX; prop++)
    {
        sIotPropStateTable[prop].propState &= ~(PROP_FLAG_UPDATED_WHEN_APP_CONNECT);
    }
}

TBool is_prop_updated_by_plugin(IotPropName_e propName)
{
    if(propName >= IOT_PROP_MAX)
        return TRUE;

    return (sIotPropStateTable[propName].propState & PROP_FLAG_UPDATED_WHEN_APP_CONNECT) > 0 ? TRUE : FALSE;
}

void set_prop_updated_by_plugin(IotPropName_e propName)
{
    if(propName < IOT_PROP_MAX)
    {
        sIotPropStateTable[propName].propState |= (PROP_FLAG_UPDATED_BY_IOT | PROP_FLAG_UPDATED_WHEN_APP_CONNECT);
    }
}

TBool is_result_successed(IotExecuteRet_e result)
{
    if(result == IOT_EXECUTE_OK || result == IOT_EXECUTE_DUP_VALUE)
        return TRUE;

    return FALSE;
}

TBool need_sync_fridge_param_to_dev(IotExecuteRet_e set_result, IotPropName_e propName)
{
    if(set_result == IOT_EXECUTE_OK)
        return TRUE;

    if(set_result == IOT_EXECUTE_DUP_VALUE)
    {
        return is_fridge_param_policy_matched(propName, PROP_POLICY_REPORT_WHEN_VALUE_DUP);
    }

    return FALSE;
}

TBool check_fridge_param_need_copy(IotPropName_e propName)
{
    void *pDevDestPtr = NULL;
    void *pIotDestPtr = NULL;

    if(propName >= IOT_PROP_MAX)
        return FALSE;

    pDevDestPtr = sIotPropStateTable[propName].p_DevDataAddress;
    pIotDestPtr = sIotPropStateTable[propName].p_IotDataAddress;

    switch(sIotPropStateTable[propName].propType)
    {
        case IOT_TYPE_BYTE:
        case IOT_TYPE_BOOL:
        {
            if(*((TByte *)pIotDestPtr) != *((TByte *)pDevDestPtr))
            {
                return TRUE;
            }
            break;
        }
        case IOT_TYPE_WORD:
        {
            if(*((TWord *)pIotDestPtr) != *((TWord *)pDevDestPtr))
            {
                return TRUE;
            }
            break;
        }
        case IOT_TYPE_LONG:
        {
            if(*((TLong *)pIotDestPtr) != *((TLong *)pDevDestPtr))
            {
                return TRUE;
            }
            break;
        }
        case IOT_TYPE_STRING:
        {
            if(strncmp((TChar *)pDevDestPtr, (TChar *)pIotDestPtr, strlen((TChar *)pDevDestPtr)))
            {
                return TRUE;
            }
            break;
        }
        default:
            break;
    }

    return FALSE;
}

void staging_fridge_param_rawdata(IotPropName_e propName, IotGeneralPropVal_t *pValue)
{
    void *pRealDestPtr = sIotPropStateTable[propName].p_IotDataAddress;

    if(sIotPropStateTable[propName].propType <= IOT_TYPE_LONG && pValue->propType == IOT_TYPE_LONG)
    {
        pValue->propType = sIotPropStateTable[propName].propType;
    }

    switch(pValue->propType)
    {
        case IOT_TYPE_BYTE:
        case IOT_TYPE_BOOL:
        {
            *((TByte *)pRealDestPtr) = (TByte)pValue->lValue;
            break;
        }
        case IOT_TYPE_WORD:
        {
            *((TWord *)pRealDestPtr) = (TWord)pValue->lValue;
            break;
        }
        case IOT_TYPE_LONG:
        {
            *((TLong *)pRealDestPtr) = (TLong)pValue->lValue;
            break;
        }
        case IOT_TYPE_STRING:
        {
            strncpy((TChar *)pRealDestPtr, pValue->sValue, strlen(pValue->sValue));
            break;
        }
        default:
            break;
    }
}

IotExecuteRet_e set_fridge_param_rawdata(IotPropName_e propName, IotGeneralPropVal_t *pValue, IotDataDir_e dir)
{
    IotExecuteRet_e ret = IOT_EXECUTE_OK;
    void *pDevDestPtr = sIotPropStateTable[propName].p_DevDataAddress;
    void *pRealDestPtr = pDevDestPtr;

    if(dir == IOT_DIR_IOT_TO_DEV)
        pRealDestPtr = sIotPropStateTable[propName].p_IotDataAddress;

    if(propName >= IOT_PROP_MAX)
        return IOT_EXECUTE_FAIL;

    if(pValue == NULL)
        return IOT_EXECUTE_FAIL;

    if(dir == IOT_DIR_IOT_TO_DEV && sIotPropValueCheckTable[propName].bWritable == FALSE)
        return IOT_EXECUTE_CANNOT_WRITE;

    if(sIotPropStateTable[propName].propType <= IOT_TYPE_LONG && pValue->propType == IOT_TYPE_LONG)
    {
        pValue->propType = sIotPropStateTable[propName].propType;
    }

    if(check_fridge_param_valid(propName, pValue) == FALSE)
        return IOT_EXECUTE_INVALID_VALUE;

    switch(pValue->propType)
    {
        case IOT_TYPE_BYTE:
        case IOT_TYPE_BOOL:
        {
            if(pValue->lValue != *((TByte *)pDevDestPtr))
            {
                *((TByte *)pRealDestPtr) = (TByte)pValue->lValue;
            }
            else
            {
                ret = IOT_EXECUTE_DUP_VALUE;
            }
            break;
        }
        case IOT_TYPE_WORD:
        {
            if(pValue->lValue != *((TWord *)pDevDestPtr))
            {
                *((TWord *)pRealDestPtr) = (TWord)pValue->lValue;
            }
            else
            {
                ret = IOT_EXECUTE_DUP_VALUE;
            }

            break;
        }
        case IOT_TYPE_LONG:
        {
            if(pValue->lValue != *((TLong *)pDevDestPtr))
            {
                *((TLong *)pRealDestPtr) = (TLong)pValue->lValue;
            }
            else
            {
                ret = IOT_EXECUTE_DUP_VALUE;
            }
            break;
        }
        case IOT_TYPE_STRING:
        {
            if(!strncmp((TChar *)pDevDestPtr, pValue->sValue, strlen(pValue->sValue)))
            {
                ret = IOT_EXECUTE_DUP_VALUE;
            }
            else
            {
                strncpy((TChar *)pRealDestPtr, pValue->sValue, strlen(pValue->sValue));
            }
            break;
        }
        default:
            return IOT_EXECUTE_FAIL;
    }

    if(ret == IOT_EXECUTE_DUP_VALUE)
    {
        if(sIotPropStateTable[propName].propState & PROP_FLAG_NOT_REPORT_AFTER_POWER_ON)
        {
            sIotPropStateTable[propName].propState &= ~PROP_FLAG_NOT_REPORT_AFTER_POWER_ON;

            if(sIotPropStateTable[propName].propState & PROP_FLAG_NOT_REPORT_AFTER_BIND)
            {
                sIotPropStateTable[propName].propState &= ~PROP_FLAG_NOT_REPORT_AFTER_BIND;

                if(!(sIotPropStateTable[propName].propPolicy & PROP_POLICY_REPORT_WHEN_VALUE_DUP))
                {
                    sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
                }
            }

            if(dir == IOT_DIR_IOT_TO_DEV)
                sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
            else
                sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_DEV;
        }
        else if((dir == IOT_DIR_IOT_TO_DEV) && (sIotPropStateTable[propName].propState & PROP_FLAG_UPDATED_BY_DEV))
        {
            sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
        }
    }

    return ret;
}

IotExecuteRet_e set_fridge_param(IotPropName_e propName, IotGeneralPropVal_t *pValue, IotDataDir_e dir)
{
    IotExecuteRet_e ret = set_fridge_param_rawdata(propName, pValue, dir);

    if(ret == IOT_EXECUTE_OK)
    {
        if(dir == IOT_DIR_DEV_TO_IOT)
        {
            sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_DEV;
        }
    }

    return ret;
}

IotExecuteRet_e get_fridge_param_rawdata(IotPropName_e propName, IotGeneralPropVal_t *pValue, IotDataDir_e dir)
{
    IotExecuteRet_e ret = IOT_EXECUTE_OK;
    void *pDestPtr;

    if(propName >= IOT_PROP_MAX)
        return IOT_EXECUTE_FAIL;

    if(pValue == NULL)
        return IOT_EXECUTE_FAIL;

    pValue->propType = sIotPropStateTable[propName].propType;

    if(dir == IOT_DIR_IOT_TO_DEV)
        pDestPtr = sIotPropStateTable[propName].p_IotDataAddress;
    else
        pDestPtr = sIotPropStateTable[propName].p_DevDataAddress;

    if(pDestPtr == NULL)
        return IOT_EXECUTE_INDIRECT_VALUE;

    switch(pValue->propType)
    {
        case IOT_TYPE_BYTE:
        case IOT_TYPE_BOOL:
        {
            pValue->lValue = *((TByte *)pDestPtr);
            break;
        }
        case IOT_TYPE_WORD:
        {
            pValue->lValue = *((TWord *)pDestPtr);
            break;
        }
        case IOT_TYPE_LONG:
        {
            pValue->lValue = *((TLong *)pDestPtr);
            break;
        }
        case IOT_TYPE_STRING:
        {
            strncpy(pValue->sValue, (TChar *)pDestPtr, strlen((TChar *)pDestPtr));
            break;
        }
        default:
            return IOT_EXECUTE_FAIL;
    }

    return ret;
}

IotExecuteRet_e get_fridge_param(IotPropName_e propName, IotGeneralPropVal_t *pValue, IotDataDir_e dir)
{
    IotExecuteRet_e ret;
    ret = get_fridge_param_rawdata(propName, pValue, dir);
    return ret;
}

IotPropName_e get_propname_by_spec_id(TByte siid, TByte piid)
{
    IotPropName_e wash_prop = IOT_PROP_MIN;

    for(; wash_prop < IOT_PROP_MAX; wash_prop++)
    {
        if(sIotPropStateTable[wash_prop].siid == siid &&
            sIotPropStateTable[wash_prop].piid == piid)
            return wash_prop;
    }

    return wash_prop;
}

TByte get_spec_siid_by_propname(IotPropName_e propName)
{
    if(propName < IOT_PROP_MAX)
    {
        return sIotPropStateTable[propName].siid;
    }

    return 0;
}

TByte get_spec_piid_by_propname(IotPropName_e propName)
{
    if(propName < IOT_PROP_MAX)
    {
        return sIotPropStateTable[propName].piid;
    }

    return 0;
}

IotActionName_e get_actionname_by_spec_id(TByte siid, TByte aiid)
{
    IotActionName_e wash_action = IOT_ACTION_MIN;

    for(; wash_action < IOT_ACTION_MAX; wash_action++)
    {
        if(sIotActionTable[wash_action].siid == siid &&
            sIotActionTable[wash_action].aiid == aiid)
            return wash_action;
    }

    return wash_action;
}

void reset_iot_setprop_excute_state(void)
{
    gIotFridge.commands_num = 0;
    memset(&gIotFridge.setprop_commands, 0, sizeof(gIotFridge.setprop_commands));
}

TBool prepare_iot_setprop_execute(TByte siid, TByte piid, IotGeneralPropVal_t *pValue)
{
    IotPropName_e propName = get_propname_by_spec_id(siid, piid);
    IotExecuteRet_e ret = IOT_EXECUTE_FAIL;

    if(propName < IOT_PROP_MAX)
    {
        if(is_remote_control_allowed())
        {
            ret = set_fridge_param_by_iot(propName, pValue);
        }
        else
        {
            ret = IOT_EXECUTE_CHILD_PROTECTED;
        }
    }

    assemble_fridge_param_command(siid, piid, propName, ret);
    return (ret == IOT_EXECUTE_OK) ? TRUE : FALSE;
}

void finish_iot_setprop_execute(IotPropName_e propName, TByte code)
{
    if(propName >= IOT_PROP_MAX)
        return;

    if(code == IOT_EXECUTE_OK)
    {
        sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
        copy_fridge_param_from_iot_to_dev(propName);
    }
}

TLong *get_action_fridge_params(IotActionName_e actionName, TByte *len)
{
    if(actionName < IOT_ACTION_MAX)
    {
        TByte nums = sIotActionTable[actionName].param_nums;
        IotPropName_e propName;
        IotGeneralPropVal_t genPropVal;

        memset(gActionParams, 0, sizeof(gActionParams));

        for(TByte i = 0; i < nums; i++)
        {
            propName = sIotActionTable[actionName].props[i];
            get_fridge_param_setby_iot(propName, &genPropVal);
            gActionParams[i] = genPropVal.lValue;
        }

        *len = nums;

        return (TLong *)&gActionParams;
    }

    return NULL;
}

TByte get_action_fridge_out_param_num(IotActionName_e actionName)
{
    if(actionName < IOT_ACTION_MAX)
    {
        return sIotActionTable[actionName].out_nums;
    }

    return 0;
}

IotExecuteRet_e get_action_fridge_out_param_value(IotActionName_e actionName, TByte index, IotGeneralPropVal_t *pValue)
{
    if(actionName >= IOT_ACTION_MAX || index >= sIotActionTable[actionName].out_nums)
        return IOT_EXECUTE_FAIL;

    return get_fridge_param_from_dev(sIotActionTable[actionName].results[index], pValue);
}

TByte get_spec_piid_by_action_out_index(IotActionName_e actionName, TByte index)
{
    if((actionName < IOT_ACTION_MAX) && (index < sIotActionTable[actionName].out_nums))
    {
        return sIotPropStateTable[sIotActionTable[actionName].results[index]].piid;
    }

    return 0;
}

TBool prepare_iot_action_param(TByte siid, TByte piid, TByte index, IotGeneralPropVal_t *pValue)
{
    IotPropName_e propName;
    IotExecuteRet_e ret = IOT_EXECUTE_FAIL;
    IotActionName_e action = gIotFridge.action_command.action;

    if(index < sIotActionTable[action].param_nums)
    {
        propName = get_propname_by_spec_id(siid, piid);
        if(propName == sIotActionTable[action].props[index])
        {
            staging_fridge_param_rawdata(propName, pValue);
            ret = IOT_EXECUTE_OK;
        }
    }

    if(!is_result_successed(ret))
    {
        gIotFridge.action_command.code = ret;
        gIotFridge.action_command.ready = true;
    }

    return is_result_successed(ret) ? TRUE : FALSE;
}

TBool prepare_iot_action_execute(TByte siid, TByte aiid)
{
    IotActionName_e actionName = get_actionname_by_spec_id(siid, aiid);
    IotExecuteRet_e ret = IOT_EXECUTE_FAIL;

    gIotFridge.action_trig = TRUE;
    gIotFridge.action_command.siid = siid;
    gIotFridge.action_command.aiid = aiid;

    if(actionName < IOT_ACTION_MAX)
    {
        ret = IOT_EXECUTE_OK;
        gIotFridge.action_command.action = actionName;
    }

    gIotFridge.action_command.code = ret;
    gIotFridge.action_command.ready = (ret == IOT_EXECUTE_OK) ? false : true;
    return (ret == IOT_EXECUTE_OK) ? TRUE : FALSE;
}

void finish_iot_action_execute(IotActionName_e actionName, TByte code)
{
    if(actionName >= IOT_ACTION_MAX)
        return;

    if(actionName != gIotFridge.action_command.action)
        return;

    if(code == IOT_EXECUTE_OK)
    {
        TByte nums = sIotActionTable[actionName].param_nums;
        IotPropName_e propName;

        //TODO: consider action execute succ, but prop value is not equal to that passed by action
        for(TByte i = 0; i < nums; i++)
        {
            propName = sIotActionTable[actionName].props[i];

            if(check_fridge_param_need_copy(propName))
            {
                sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;

                copy_fridge_param_from_iot_to_dev(propName);
            }
            else
            {
                if(sIotPropStateTable[propName].propState & PROP_FLAG_NOT_REPORT_AFTER_POWER_ON)
                {
                    sIotPropStateTable[propName].propState &= ~PROP_FLAG_NOT_REPORT_AFTER_POWER_ON;
                    sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
                }
                else if(sIotPropStateTable[propName].propState & PROP_FLAG_UPDATED_BY_DEV)
                {
                    sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
                }
            }
        }
    }

    gIotFridge.action_command.ready = true;
}

TByte get_event_report_param_num(IotEventName_e eventName)
{
    if(eventName >= IOT_EVENT_MAX)
        return 0;

    return sIotEventTable[eventName].param_nums;
}

IotExecuteRet_e get_event_report_param_value(IotEventName_e eventName, TByte index, callbackCustomGetFridgeParam callback, IotGeneralPropVal_t *pValue)
{
    if(eventName >= IOT_EVENT_MAX || index >= sIotEventTable[eventName].param_nums)
        return IOT_EXECUTE_FAIL;

    if((callback != NULL) && (callback(sIotEventTable[eventName].params[index], pValue) == IOT_EXECUTE_OK))
        return IOT_EXECUTE_OK;

    return get_fridge_param_from_dev(sIotEventTable[eventName].params[index], pValue);
}

TByte get_spec_siid_by_eventname(IotEventName_e eventName)
{
    if(eventName < IOT_EVENT_MAX)
    {
        return sIotEventTable[eventName].siid;
    }

    return 0;
}

TByte get_spec_eiid_by_eventname(IotEventName_e eventName)
{
    if(eventName < IOT_EVENT_MAX)
    {
        return sIotEventTable[eventName].eiid;
    }

    return 0;
}

static TByte get_spec_piid_by_factorydata_prop_name(IotPropName_e name)
{
    if(name >= IOT_FCT_PROP_MIN)
    {
        return name - IOT_FCT_PROP_MIN + 1;
    }

    return 0;
}

static TByte get_spec_piid_by_datalogging_prop_name(IotPropName_e name)
{
    if(name >= IOT_DL_PROP_MIN && name < IOT_FCT_PROP_MIN)
    {
        return name - IOT_DL_PROP_MIN + 1;
    }

    return 0;
}

TByte get_spec_piid_by_event_index(IotEventName_e eventName, TByte index)
{
    if((eventName < IOT_EVENT_MAX) && (index < sIotEventTable[eventName].param_nums))
    {
        if(sIotEventTable[eventName].params[index] < IOT_PROP_MAX)
            return sIotPropStateTable[sIotEventTable[eventName].params[index]].piid;
        else if(sIotEventTable[eventName].params[index] < IOT_FCT_PROP_MIN)
            return get_spec_piid_by_datalogging_prop_name(sIotEventTable[eventName].params[index]);
        else
            return get_spec_piid_by_factorydata_prop_name(sIotEventTable[eventName].params[index]);
    }

    return 0;
}

TSWord map_result_to_iot_code(TByte result)
{
    TSWord code = -4003;

    switch(result)
    {
        case IOT_EXECUTE_OK:
        case IOT_EXECUTE_DUP_VALUE:
            code = 0;
            break;
        case IOT_EXECUTE_FAIL:
            code = -4003;
            break;
        case IOT_EXECUTE_INVALID_VALUE:
            code = -4005;
            break;
        case IOT_EXECUTE_CANNOT_WRITE:
            code = -4002;
            break;
        case IOT_EXECUTE_CANNOT_READ:
            code = -4001;
            break;
        case IOT_EXECUTE_DEV_FAIL:
            code = -4404;
            break;
        case IOT_EXECUTE_CHILD_PROTECTED:
            code = -4456;
            break;
    }

    return code;
}

TBool is_fridge_param_policy_matched(IotPropName_e propName, TByte policy)
{
    return (sIotPropStateTable[propName].propPolicy & policy) > 0 ? TRUE : FALSE;
}

TBool is_remote_control_allowed(void)
{
    return TRUE;
}

TBool is_fridge_param_updated(IotPropName_e propName)
{
    TByte flag;

    flag = PROP_FLAG_UPDATED_BY_IOT;

    if(propName >= IOT_PROP_MAX)
        return FALSE;

    if(gIotFridge.plugin_connect == TRUE)
    {
        if(sIotPropStateTable[propName].propPolicy & PROP_POLICY_REPORT_WHEN_VALUE_DUP)
        {
            flag = PROP_FLAG_UPDATED_BY_IOT;
        }
        else
        {
            flag = PROP_FLAG_UPDATED_BY_IOT | PROP_FLAG_UPDATED_BY_DEV;
        }
    }
    else
    {
        if(sIotPropStateTable[propName].propPolicy & PROP_POLICY_BASIC_INFO)
        {
            flag = PROP_FLAG_UPDATED_BY_IOT | PROP_FLAG_UPDATED_BY_DEV;
        }
    }

    return (sIotPropStateTable[propName].propState & flag) > 0 ? TRUE : FALSE;
}

void clear_fridge_param_updated(IotPropName_e propName)
{
    if(propName >= IOT_PROP_MAX)
        return;

    sIotPropStateTable[propName].propState &= ~(PROP_FLAG_UPDATED_BY_IOT | PROP_FLAG_UPDATED_BY_DEV | PROP_FLAG_NOT_REPORT_AFTER_POWER_ON | PROP_FLAG_NOT_REPORT_AFTER_BIND);
}

void set_fridge_param_updated_by_event(IotPropName_e propName)
{
    if(propName < IOT_PROP_MAX)
    {
        sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
    }
}

TBool is_allowed_execute_ota(void)
{
    return true;
}
