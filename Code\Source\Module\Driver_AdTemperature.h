/*!
 * @file
 * @brief This file defines public constants, types and functions for the temperature sensor.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef DRIVER_AD_TEMPERATURE_H
#define DRIVER_AD_TEMPERATURE_H

#include <stdint.h>

#define CON_F50P0_DEGREE ((uint16_t)0)
#define CON_F40P0_DEGREE ((uint16_t)100)
#define CON_F30P0_DEGREE ((uint16_t)200)
#define CON_F20P0_DEGREE ((uint16_t)300)
#define CON_F12P0_DEGREE ((uint16_t)380)
#define CON_F10P0_DEGREE ((uint16_t)400)
#define CON_0P0_DEGREE ((uint16_t)500)
#define CON_1P0_DEGREE ((uint16_t)510)
#define CON_2P0_DEGREE ((uint16_t)520)
#define CON_3P0_DEGREE ((uint16_t)530)
#define CON_4P0_DEGREE ((uint16_t)540)
#define CON_5P0_DEGREE ((uint16_t)550)
#define CON_6P0_DEGREE ((uint16_t)560)
#define CON_7P0_DEGREE ((uint16_t)570)
#define CON_8P0_DEGREE ((uint16_t)580)
#define CON_9P0_DEGREE ((uint16_t)590)
#define CON_10P0_DEGREE ((uint16_t)600)
#define CON_13P0_DEGREE ((uint16_t)630)
#define CON_18P0_DEGREE ((uint16_t)680)
#define CON_20P0_DEGREE ((uint16_t)700)
#define CON_23P0_DEGREE ((uint16_t)730)
#define CON_28P0_DEGREE ((uint16_t)780)
#define CON_30P0_DEGREE ((uint16_t)800)
#define CON_32P0_DEGREE ((uint16_t)820)
#define CON_35P0_DEGREE ((uint16_t)850)
#define CON_40P0_DEGREE ((uint16_t)900)
#define CON_50P0_DEGREE ((uint16_t)1000)
#define CON_60P0_DEGREE ((uint16_t)1100)
#define CON_74P0_DEGREE ((uint16_t)1240)
#define CON_75P0_DEGREE ((uint16_t)1250)
#define CON_76P0_DEGREE ((uint16_t)1260)

#define HUMIDITY_AD_PCT10 ((uint16_t)253)
#define HUMIDITY_AD_PCT15 ((uint16_t)284)
#define HUMIDITY_AD_PCT20 ((uint16_t)315)
#define HUMIDITY_AD_PCT25 ((uint16_t)345)
#define HUMIDITY_AD_PCT30 ((uint16_t)373)
#define HUMIDITY_AD_PCT35 ((uint16_t)401)
#define HUMIDITY_AD_PCT40 ((uint16_t)428)
#define HUMIDITY_AD_PCT45 ((uint16_t)454)
#define HUMIDITY_AD_PCT50 ((uint16_t)481)
#define HUMIDITY_AD_PCT55 ((uint16_t)507)
#define HUMIDITY_AD_PCT60 ((uint16_t)533)
#define HUMIDITY_AD_PCT65 ((uint16_t)559)
#define HUMIDITY_AD_PCT70 ((uint16_t)585)
#define HUMIDITY_AD_PCT75 ((uint16_t)612)
#define HUMIDITY_AD_PCT80 ((uint16_t)639)
#define HUMIDITY_AD_PCT85 ((uint16_t)667)
#define HUMIDITY_AD_PCT90 ((uint16_t)696)
#define HUMIDITY_AD_PCT95 ((uint16_t)722)
#define HUMIDITY_AD_PCT100 ((uint16_t)748)

// clang-format off
const static uint16_t ary_Lntd506Temp[] =
{
 //|___0,|___1,|___2,|___3,|___4,|___5,|___6,|___7,|___8,|___9,|
 //  127   128   129   130   131   132   133   134   135   136  AD
    1001,  999,  996,  994,  992,  989,  987,  985,  983,  980,
     978,  976,  974,  972,  969,  967,  965,  963,  961,  959,
     957,  955,  953,  951,  949,  947,  945,  943,  941,  939,
     937,  935,  933,  931,  929,  927,  926,  924,  922,  920,
     918,  916,  915,  913,  911,  909,  908,  906,  904,  902,
     901,  899,  897,  896,  894,  892,  891,  889,  887,  886,
     884,  882,  881,  879,  877,  876,  874,  873,  871,  870,
     868,  867,  865,  863,  862,  860,  859,  857,  856,  854,
     853,  851,  850,  849,  847,  846,  844,  843,  841,  840,
     838,  837,  836,  834,  833,  831,  830,  829,  827,  826,
     825,  823,  822,  820,  819,  818,  816,  815,  814,  813,
     811,  810,  809,  807,  806,  805,  803,  802,  801,  800,
     798,  797,  796,  795,  793,  792,  791,  790,  788,  787,
     786,  785,  783,  782,  781,  780,  779,  777,  776,  775,
     774,  773,  772,  770,  769,  768,  767,  766,  765,  763,
     762,  761,  760,  759,  758,  757,  755,  754,  753,  752,
     751,  750,  749,  748,  746,  745,  744,  743,  742,  741,
     740,  739,  738,  737,  736,  734,  733,  732,  731,  730,
     729,  728,  727,  726,  725,  724,  723,  722,  721,  720,
     719,  718,  717,  716,  715,  714,  713,  712,  711,  710,
     708,  707,  706,  705,  704,  703,  702,  701,  700,  699,
     698,  698,  697,  696,  695,  694,  693,  692,  691,  690,
     689,  688,  687,  686,  685,  684,  683,  682,  681,  680,
     679,  678,  677,  676,  675,  674,  673,  673,  672,  671,
     670,  669,  668,  667,  666,  665,  664,  663,  662,  661,
     660,  660,  659,  658,  657,  656,  655,  654,  653,  652,
     651,  650,  650,  649,  648,  647,  646,  645,  644,  643,
     642,  642,  641,  640,  639,  638,  637,  636,  635,  634,
     634,  633,  632,  631,  630,  629,  628,  627,  627,  626,
     625,  624,  623,  622,  621,  621,  620,  619,  618,  617,
     616,  615,  615,  614,  613,  612,  611,  610,  609,  609,	
     608,  607,  606,  605,  604,  604,  603,  602,  601,  600,	
     599,  599,  598,  597,  596,  595,  594,  594,  593,  592,	
     591,  590,  589,  589,  588,  587,  586,  585,  584,  584,	
     583,  582,  581,  580,  580,  579,  578,  577,  576,  575,	
     575,  574,  573,  572,  571,  571,  570,  569,  568,  567,	
     567,  566,  565,  564,  563,  562,  562,  561,  560,  559,	
     558,  558,  557,  556,  555,  554,  554,  553,  552,  551,	
     550,  550,  549,  548,  547,  546,  546,  545,  544,  543,	
     542,  542,  541,  540,  539,  539,  538,  537,  536,  535,	
     535,  534,  533,  532,  531,  531,  530,  529,  528,  527,	
     527,  526,  525,  524,  524,  523,  522,  521,  520,  520,	
     519,  518,  517,  516,  516,  515,  514,  513,  512,  512,	
     511,  510,  509,  509,  508,  507,  506,  505,  505,  504,	
     503,  502,  502,  501,  500,  499,  498,  498,  497,  496,	
     495,  494,  494,  493,  492,  491,  491,  490,  489,  488,	
     487,  487,  486,  485,  484,  483,  483,  482,  481,  480,	
     480,  479,  478,  477,  476,  476,  475,  474,  473,  472,	
     472,  471,  470,  469,  469,  468,  467,  466,  465,  465,	
     464,  463,  462,  461,  461,  460,  459,  458,  457,  457,	
     456,  455,  454,  454,  453,  452,  451,  450,  450,  449,	
     448,  447,  446,  446,  445,  444,  443,  442,  442,  441,	
     440,  439,  438,  438,  437,  436,  435,  434,  434,  433,	
     432,  431,  430,  430,  429,  428,  427,  426,  425,  425,	
     424,  423,  422,  421,  421,  420,  419,  418,  417,  417,	
     416,  415,  414,  413,  412,  412,  411,  410,  409,  408,	
     407,  407,  406,  405,  404,  403,  403,  402,  401,  400,	
     399,  398,  398,  397,  396,  395,  394,  393,  392,  392,	
     391,  390,  389,  388,  387,  387,  386,  385,  384,  383,	
     382,  381,  381,  380,  379,  378,  377,  376,  375,  375,	
     374,  373,  372,  371,  370,  369,  368,  368,  367,  366,	
     365,  364,  363,  362,  361,  361,  360,  359,  358,  357,	
     356,  355,  354,  353,  352,  352,  351,  350,  349,  348,	
     347,  346,  345,  344,  343,  342,  342,  341,  340,  339,	
     338,  337,  336,  335,  334,  333,  332,  331,  330,  329,	
     328,  328,  327,  326,  325,  324,  323,  322,  321,  320,	
     319,  318,  317,  316,  315,  314,  313,  312,  311,  310,	
     309,  308,  307,  306,  305,  304,  303,  302,  301,  300,	
     299,  298,  297,  296,  295,  294,  293,  292,  291,  290,	
     289,  288,  287,  286,  285,  283,  282,  281,  280,  279,	
     278,  277,  276,  275,  274,  273,  272,  270,  269,  268,	
     267,  266,  265,  264,  263,  262,  260,  259,  258,  257,	
     256,  255,  253,  252,  251,  250,  249,  248,  246,  245,	
     244,  243,  242,  240,  239,  238,  237,  235,  234,  233,	
     232,  230,  229,  228,  227,  225,  224,  223,  222,  220,	
     219,  218,  216,  215,  214,  212,  211,  210,  208,  207,	
     205,  204,  203,  201,  200,  198,  197,  196,  194,  193,	
     191,  190,  188,  187,  185,  184,  182,  181,  179,  178,	
     176,  175,  173,  172,  170,  168,  167,  165,  163,  162,	
     160,  159,  157,  155,  153,  152,  150,  148,  147,  145,	
     143,  141,  139,  138,  136,  134,  132,  130,  128,  126,	
     124,  122,  120,  118,  116,  114,  112,  110,  108,  106,	
     104,  102,   99,   97,   95,   93,   90,   88,   86,   83,	
      81,	79,   76,   74,   71,   69,   66,   63,   61,   58,	
      55,	53,   50,   47,   44,   41,   38,   35,   32,	29,	
      25,	22,   19,   15,   12,	 8,    5,    1
//AD 977,  978,  979,  980,  981,  982,  983,  984,
};

const static uint16_t ary_RoomTemp[] =
{
 //|___0,|___1,|___2,|___3,|___4,|___5,|___6,|___7,|___8,|___9,|
 //  235   236   237   238   239   240   241   242   243   244  AD
    1101, 1099, 1098, 1096, 1094, 1092, 1091, 1089, 1087, 1086, 
    1084, 1082, 1081, 1079, 1077, 1075, 1074, 1072, 1070, 1069, 
    1067, 1066, 1064, 1062, 1061, 1059, 1057, 1056, 1054, 1053,
    1051, 1049, 1048, 1046, 1045, 1043, 1042, 1040, 1038, 1037, 
    1035, 1034, 1032, 1031, 1029, 1028, 1026, 1025, 1023, 1022, 
    1020, 1019, 1017, 1016, 1014, 1013, 1011, 1010, 1008, 1007, 
    1006, 1004, 1003, 1001, 1000,  998,  997,  995,  994,  993,
     991,  990,  988,  987,  986,  984,  983,  981,  980,  979,	
     977,  976,  975,  973,  972,  970,  969,  968,  966,  965,	
     964,  962,  961,  960,  958,  957,  956,  954,  953,  952,	
     950,  949,  948,  947,  945,  944,  943,  941,  940,  939,	
     937,  936,  935,  934,  932,  931,  930,  929,  927,  926,	
     925,  924,  922,  921,  920,  919,  917,  916,  915,  914,	
     912,  911,  910,  909,  907,  906,  905,  904,  903,  901,	
     900,  899,  898,  896,  895,  894,  893,  892,  891,  889,	
     888,  887,  886,  885,  883,  882,  881,  880,  879,  878,	
     876,  875,  874,  873,  872,  871,  869,  868,  867,  866,	
     865,  864,  862,  861,  860,  859,  858,  857,  856,  854,	
     853,  852,  851,  850,  849,  848,  847,  845,  844,  843,	
     842,  841,  840,  839,  838,  836,  835,  834,  833,  832,	
     831,  830,  829,  828,  826,  825,  824,  823,  822,  821,	
     820,  819,  818,  817,  816,  814,  813,  812,  811,  810,	
     809,  808,  807,  806,  805,  804,  803,  802,  800,  799,	
     798,  797,  796,  795,  794,  793,  792,  791,  790,  789,	
     788,  787,  786,  784,  783,  782,  781,  780,  779,  778,	
     777,  776,  775,  774,  773,  772,  771,  770,  769,  768,	
     767,  766,  765,  764,  762,  761,  760,  759,  758,  757,	
     756,  755,  754,  753,  752,  751,  750,  749,  748,  747,	
     746,  745,  744,  743,  742,  741,  740,  739,  738,  737,	
     736,  735,  734,  733,  732,  731,  730,  728,  727,  726,	
     725,  724,  723,  722,  721,  720,  719,  718,  717,  716,	
     715,  714,  713,  712,  711,  710,  709,  708,  707,  706,	
     705,  704,  703,  702,  701,  700,  699,  698,  697,  696,	
     695,  694,  693,  692,  691,  690,  689,  688,  687,  686,	
     685,  684,  683,  682,  681,  680,  679,  678,  677,  676,	
     675,  674,  673,  672,  671,  670,  669,  668,  667,  666,	
     665,  664,  663,  662,  661,  660,  659,  658,  657,  656,	
     655,  654,  653,  652,  651,  650,  649,  648,  647,  646,	
     645,  644,  643,  642,  641,  640,  639,  637,  636,  635,	
     634,  633,  632,  631,  630,  629,  628,  627,  626,  625,	
     624,  623,  622,  621,  620,  619,  618,  617,  616,  615,	
     614,  613,  612,  611,  610,  609,  608,  607,  606,  605,	
     604,  603,  602,  601,  600,  599,  598,  597,  596,  595,	
     594,  593,  591,  590,  589,  588,  587,  586,  585,  584,	
     583,  582,  581,  580,  579,  578,  577,  576,  575,  574,	
     573,  572,  571,  570,  569,  567,  566,  565,  564,  563,	
     562,  561,  560,  559,  558,  557,  556,  555,  554,  553,	
     552,  550,  549,  548,  547,  546,  545,  544,  543,  542,	
     541,  540,  539,  538,  536,  535,  534,  533,  532,  531,	
     530,  529,  528,  527,  526,  524,  523,  522,  521,  520,	
     519,  518,  517,  516,  514,  513,  512,  511,  510,  509,	
     508,  507,  505,  504,  503,  502,  501,  500,  499,  498,	
     496,  495,  494,  493,  492,  491,  489,  488,  487,  486,	
     485,  484,  482,  481,  480,  479,  478,  477,  475,  474,	
     473,  472,  471,  469,  468,  467,  466,  465,  463,  462,	
     461,  460,  459,  457,  456,  455,  454,  453,  451,  450,	
     449,  448,  446,  445,  444,  443,  441,  440,  439,  438,	
     436,  435,  434,  432,  431,  430,  429,  427,  426,  425,	
     423,  422,  421,  419,  418,  417,  415,  414,  413,  411,	
     410,  409,  407,  406,  405,  403,  402,  401,  399,  398,	
     396,  395,  394,  392,  391,  390,  388,  387,  385,  384,	
     382,  381,  380,  378,  377,  375,  374,  372,  371,  369,	
     368,  366,  365,  363,  362,  360,  359,  357,  356,  354,	
     353,  351,  350,  348,  346,  345,  343,  342,  340,  338,	
     337,  335,  334,  332,  330,  329,  327,  325,  324,  322,	
     320,  319,  317,  315,  314,  312,  310,  308,  307,  305,	
     303,  301,  299,  298,  296,  294,  292,  290,  288,  286,	
     285,  283,  281,  279,  277,  275,  273,  271,  269,  267,	
     265,  263,  261,  259,  257,  255,  253,  251,  248,  246,	
     244,  242,  240,  238,  235,  233,  231,  229,  226,  224,	
     222,  219,  217,  214,  212,  210,  207,  205,  202,  200,	
     197,  194,  192,  189,  186,  184,  181,  178,  175,  173,	
     170,  167,  164,  161,  158,  155,  152,  149,  146,  142,	
     139,  136,  133,  129,  126,  122,  119,  115,  111,  108,	
     104,  100,   96,   92,   88,	84,   80,   76,   71,   67,	
      62,	57,	53,	48,	43,   38,   32,   27,   21,   16,	
      10,	 4
//AD 995   996
};
// clang-format on

#endif
