/*!
 * @file
 * @brief This file defines public constants, types and functions for the vertical beam heater.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef VERTICAL_BEAM_HEATER_H
#define VERTICAL_BEAM_HEATER_H

#include <stdint.h>
#include <stdbool.h>

void Ctrl_VerticalBeamHeater(void);
void Drive_VerticalBeamHeaterOutPut(void);
void Forced_VerticalBeamHeaterState(bool state);
bool Get_VerticalBeamHeaterState(void);
uint8_t Get_VerticalBeamHeaterOnSecond(void);

#endif
