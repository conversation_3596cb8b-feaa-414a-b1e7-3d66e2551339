/*
 * multi_mcu_ota.h
 *
 *  Created on: 2023年11月1日
 *      Author: Ha<PERSON><PERSON>iangZhou
 */

#ifndef OTA_PORT_OTA_MULTI_MCU_H_
#define OTA_PORT_OTA_MULTI_MCU_H_

#include "hw_port.h"

#define OTA_FIREWARE_MAX   (5)

typedef enum
{
	FIREWARE_ADDR_NONE = 0x0,
	FIREWARE_MAIN_ADDR = 0x1,
	FIREWARE_DISPLAY_ADDR = 0x2,
	FIREWARE_PARTIALFRZ_ADDR = 0x3,
	FIREWARE_ICEMAKER_ADDR = 0x4,
	FIREWARE_INVERTER_ADDR = 0x5,
} fireware_addr_e;

typedef struct {
	uint32_t id;
	uint32_t version;
	uint32_t offset;
	uint32_t length;
	uint32_t crc;
}ota_fireware_header_st;

typedef struct{

	uint8_t  hw_id_str[8];
	uint32_t total_version;
	ota_fireware_header_st fheader[OTA_FIREWARE_MAX];
	uint32_t filldata[4]; //aligned to 128
}MULTI_MCU_HEADER;


#endif /* OTA_PORT_OTA_MULTI_MCU_H_ */
