#include "miio_define.h"
#include "miio_uart.h"
#include "Adpt_Usart.h"

int miio_uart_send_str(uint8_t ch, const char* str)
{
    int n_send = 0;
    int len = strlen(str);
    uint32_t timeout_ms = 200;
    
    if (len <= 0) { return 0; }
    while( n_send<len )
    {
        if( uart_send_byte(ch, str[n_send]) )
            n_send++;
        else{
            if( timeout_ms == 0 )
                break;
            udelay(1000);
            timeout_ms--;
        }
    }

    return n_send;
}

int miio_uart_send_byte(uint8_t ch, unsigned char c)
{
    int n_send = 0;
    uint32_t timeout_ms = 200;

    while( n_send< 1 )
    {
        if( uart_send_byte(ch, c) )
            n_send++;
        else{
            if( timeout_ms == 0 )
                break;
            udelay(1000);
            timeout_ms--;
        }
    }

    return n_send;
}

int miio_xmodem_recv_str(uint8_t ch, uint8_t* pbuf,uint32_t len, uint32_t timeout_ms)
{
    int n_read = 0;
    uint32_t delay_count = 48 * timeout_ms;
    uint32_t gap_delay = 500;

    while( 1 )
    {
        if( uart_recv_byte(ch, pbuf+n_read) )
        {
            n_read++;
            gap_delay = 500;
        }
        else{
            if( delay_count == 0 )
            {
                break;
            }
            if(n_read >= len)
            {
                if(--gap_delay == 0)
                {
                    break;
                }
            }
            udelay(1);
            delay_count--;
        }
    }
    return n_read;
}

int miio_xmodem_recv_byte(uint8_t ch, unsigned char *c, uint32_t timeout_ms)
{
    int n_read = 0;
    uint32_t delay_count = 20*timeout_ms;
    
    while( 1 > n_read )
    {
        if( uart_recv_byte(ch, c) )
        {
            n_read++;
        }
        else{
            if( delay_count == 0 )
            {
                break;
            }
            udelay(1);
            delay_count--;
        }
    }
    return n_read;
}


int miio_uart_recv_str(uint8_t ch, uint8_t* pbuf, int buf_len, uint32_t timeout_ms)
{
    int n_read = 0;
    uint32_t delay_count = 20*timeout_ms;
    
    while( buf_len>n_read )
    {
        if( uart_recv_byte(ch, pbuf+n_read) )
        {
            n_read++;
            if( pbuf[n_read-1] == '\r' )
                break;
        }
        else{
            if( delay_count == 0 )
            {
                break;
            }
            udelay(1);
            delay_count--;
        }
    }
    return n_read;
}

int miio_uart_recv_str_aync(uint8_t ch, uint8_t** ppbuf)
{
    static uint8_t recv_buf[CMD_STR_MAX_LEN];
    static int recv_len=0;

    int n_read = 0;
    while( recv_len < CMD_STR_MAX_LEN )
    {
        if( uart_recv_byte(ch, &recv_buf[recv_len]) )
        {
            recv_len++;
            if( recv_buf[recv_len-1] == '\r' )
            {
                *ppbuf = recv_buf;
                n_read = recv_len;
                recv_len = 0;
                return n_read;
            }
        }
        else{
            break;
        }
    }

    if( recv_len == CMD_STR_MAX_LEN )
    {
        recv_len = 0;
    }
    return n_read;
}


int miio_uart_send_str_wait_ack(uint8_t ch, const char* str, uint32_t timeout_ms)
{
    static uint8_t ack_buf[ACK_BUF_SIZE] = { 0 };
    int n_send = miio_uart_send_str(ch, str);

    memset(ack_buf, 0, ACK_BUF_SIZE);

    miio_uart_recv_str(ch, ack_buf, ACK_BUF_SIZE, timeout_ms);
    if (0 != strncmp((const char*)ack_buf, "ok", strlen("ok"))) {
        return UART_RECV_ACK_ERROR;
    }

    return n_send;
}

int miio_uart_has_more_data(uint8_t ch)
{
    if (uart_recv_buffer_is_empty(ch) == 0)
    {
        return 1;
    }
    return 0;
}

