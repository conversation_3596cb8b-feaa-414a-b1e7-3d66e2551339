/*!
 * @file
 * @brief Manages all the state variables of the led controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "LedController.h"
#include "Core_Types.h"
#include "KeyManager.h"
#include "FaultCode.h"
#include "Iot_Spec.h"
#include "FactoryMode.h"
#include "SpecialKeyManager.h"
#include "FridgeRunner.h"
#include "Defrosting.h"
#include "CoolingCycle.h"
#include "Parameter_TemperatureZone.h"
#include "Driver_AdSample.h"
#include "Parameter_Device.h"
#include "Driver_DoorSwitch.h"
#include "Driver_CompFrequency.h"
#include "Driver_Fan.h"
#include "Driver_DoubleDamper.h"
#include "IO_Device.h"
#include "Driver_GradualLamp.h"
#include "SystemTimerModule.h"
#include "Core_Types.h"
#include "VerticalBeamHeater.h"
#include "DisplayInterface.h"
#include "InverterUsart.h"
#include "ResolverDevice.h"
#include "FaultCode.h"

static uint8_t u8_DisplayByte1;
static uint8_t u8_DisplayByte2;
static uint8_t u8_DisplayByte3;
static uint8_t u8_NumberFlashCount;
static uint8_t u8_ModeFlashCount;
static uint8_t u8_RefVarFlashCount;
static uint8_t u8_LockFlashCount;
static uint8_t u8_AnimationCount;
static uint8_t u8_Animation100msCount;
static uint8_t u8_Wifi100msCount;
static uint8_t u8_QueryMode100msCount;
static uint8_t u8_RefDisplayValue;
static uint8_t u8_FrzDisplayValue;
static uint8_t u8_AdjustParaDisplayValue;
static uint8_t u8_DisplayZone;
static uint8_t u8_DisplayUserMode;
static uint8_t u8_DisplayRefVarSet;
static bool b_NumberFlashState;
static bool b_ModeFlashState;
static bool b_RefVarFlashState;
static bool b_NumberLedFlashState;
static bool b_ModeLedFlashState;
static bool b_RefVarLedFlashState;
static bool b_LockFlashState;
static MyWord_st st_DispFaultCode;
static uint8_t u8_FaultOccursNumber;
static bool b_ForceHeater;
static bool b_ForceHeateDisplay;
static bool b_ShowRoomDisplay;
static bool b_FactoryResetDisplay;
static bool b_TestModeDisplay;
static bool b_QueryModeDisplay;
static bool b_AdjustParaDisplay;
static bool b_PcbTestDisplay;
static bool b_WiFiFactoryConnected;
static bool b_QueryModeNegativeState;

// bit1-bit7十位 依次对应段码GFEDCBA
static uint8_t ARY_LED_DATA[CON_MaxDispNum] = {
    0xFC, // 0
    0x60, // 1
    0xDA, // 2
    0xF2, // 3
    0x66, // 4
    0xB6, // 5
    0xBE, // 6
    0xE0, // 7                 - A -
    0xFE, // 8                |     |
    0xF6, // 9                F     B
    0x00, // 10 NC            |     |
    0x8C, // 11 T              - G -
    0x8E, // 12 F             |     |
    0x1C, // 13 L             E     C
    0x6E, // 14 H             |     |
    0x9E, // 15 E              - D -
    0x9C, // 16 C
    0x7A, // 17 d
    0xEE, // 18 A
    0x0A, // 19 r
    0x20, // 20 i
    0x3A, // 21 o
    0x22, // 22 t
    0xEC, // 23 N
    0x80, // 24 CON_DispDispVer
    0x82, // 25 CON_DispMainVer
    0x93, // 26 CON_DispSlaveVer
    0x02, // 27 CON_DispSubSign
    0x3E, // 28 b
    0x7C, // 29 U
    0xCE, // 30 P
};

void Set_LockLedState(bool state)
{
    BIT_WRITE(u8_DisplayByte3, (uint8_t)eLockLed_BitIndex, state);
}

void Set_FuzzyLedState(bool state)
{
    BIT_WRITE(u8_DisplayByte3, (uint8_t)eFuzzyLed_BitIndex, state);
}

void Set_TurboCoolLedState(bool state)
{
    BIT_WRITE(u8_DisplayByte3, (uint8_t)eTurboCoolLed_BitIndex, state);
}

void Set_TurboFreezeLedState(bool state)
{
    BIT_WRITE(u8_DisplayByte3, (uint8_t)eTurboFreezeLed_BitIndex, state);
}

void Set_TreasureLedState(bool state)
{
    BIT_WRITE(u8_DisplayByte3, (uint8_t)eTreasureLed_BitIndex, state);
}

void Set_BabyLedState(bool state)
{
    BIT_WRITE(u8_DisplayByte3, (uint8_t)eBabyLed_BitIndex, state);
}

void Set_ZeroLedState(bool state)
{
    BIT_WRITE(u8_DisplayByte3, (uint8_t)eZeroLed_BitIndex, state);
}

void Set_RefVarLedState(RefVarSet_t setpoint)
{
    switch(setpoint)
    {
        case(RefVarSet_t)eRefVar_Treasure:
            Set_TreasureLedState(true);
            Set_BabyLedState(false);
            Set_ZeroLedState(false);
            break;
        case(RefVarSet_t)eRefVar_Baby:
            Set_BabyLedState(true);
            Set_TreasureLedState(false);
            Set_ZeroLedState(false);
            break;
        case(RefVarSet_t)eRefVar_Zero:
            Set_ZeroLedState(true);
            Set_TreasureLedState(false);
            Set_BabyLedState(false);
            break;
        case(RefVarSet_t)eRefVar_Max:
            Set_ZeroLedState(false);
            Set_TreasureLedState(false);
            Set_BabyLedState(false);
            break;
        default:
            break;
    }
}

void Set_ModeLedState(UserMode_t mode)
{
    switch(mode)
    {
        case(UserMode_t)eFuzzy_Mode:
            Set_FuzzyLedState(true);
            Set_TurboCoolLedState(false);
            Set_TurboFreezeLedState(false);
            break;
        case(UserMode_t)eTurboCool_Mode:
            Set_TurboCoolLedState(true);
            Set_FuzzyLedState(false);
            Set_TurboFreezeLedState(false);
            break;
        case(UserMode_t)eTurboFreeze_Mode:
            Set_TurboFreezeLedState(true);
            Set_FuzzyLedState(false);
            Set_TurboCoolLedState(false);
            break;
        case(UserMode_t)eManual_Mode:
            Set_TurboFreezeLedState(false);
            Set_FuzzyLedState(false);
            Set_TurboCoolLedState(false);
            break;
        default:
            break;
    }
}

void Set_WiFiLedState(bool state)
{
    BIT_WRITE(u8_DisplayByte3, (uint8_t)eWiFiLed_BitIndex, state);
}

void Set_DegreeCentigradeLedState(bool state)
{
    BIT_WRITE(u8_DisplayByte1, 0, state);
}

void Set_88LowLed(uint8_t displayIndex)
{
    u8_DisplayByte2 = ARY_LED_DATA[displayIndex];
}

void Set_NegativeSignLedState(bool state)
{
    BIT_WRITE(u8_DisplayByte2, 0, state);
}

void Set_88HighLed(uint8_t displayIndex)
{
    u8_DisplayByte1 = ARY_LED_DATA[displayIndex];
}

void Set_AllLedsState(bool state)
{
    if(false == state)
    {
        Set_88HighLed(CON_DispNC);
        Set_88LowLed(CON_DispNC);
    }
    else
    {
        Set_88HighLed(CON_Disp8);
        Set_88LowLed(CON_Disp8);
    }
    Set_DegreeCentigradeLedState(state);
    Set_NegativeSignLedState(state);
    Set_LockLedState(state);
    Set_FuzzyLedState(state);
    Set_TurboCoolLedState(state);
    Set_TurboFreezeLedState(state);
    Set_WiFiLedState(state);
    Set_TreasureLedState(state);
    Set_BabyLedState(state);
    Set_ZeroLedState(state);
}

void Set_NumberDisplay(DisplayZone_t zone, bool flashState, uint8_t numberIndex)
{
    u8_DisplayZone = zone;
    b_NumberFlashState = flashState;
    if(true == b_NumberFlashState)
    {
        u8_NumberFlashCount = 0;
        u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
        u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;
    }
    else
    {
        u8_NumberFlashCount = U8_FLASH_COUNT_MAX;
    }
    if((DisplayZone_t)eDispRef_Zone == zone)
    {
        u8_RefDisplayValue = numberIndex;
    }
    else
    {
        u8_FrzDisplayValue = numberIndex;
    }
}

void Set_ModeDisplay(bool flashState, UserMode_t mode)
{
    b_ModeFlashState = flashState;
    if(true == b_ModeFlashState)
    {
        u8_ModeFlashCount = 0;
        u8_NumberFlashCount = U8_FLASH_COUNT_MAX;
        u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;
    }
    else
    {
        u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
    }
    u8_DisplayUserMode = mode;
    switch(u8_DisplayUserMode)
    {
        case(UserMode_t)eFuzzy_Mode:
            u8_DisplayZone = (DisplayZone_t)eDispRef_Zone;
            break;
        case(UserMode_t)eTurboCool_Mode:
            u8_DisplayZone = (DisplayZone_t)eDispRef_Zone;
            break;
        case(UserMode_t)eTurboFreeze_Mode:
            u8_DisplayZone = (DisplayZone_t)eDispFrz_Zone;
            break;
        default:
            break;
    }
}

void Set_RefVarDisplay(bool flashState, RefVarSet_t setpoint)
{
    b_RefVarFlashState = flashState;
    if(true == b_RefVarFlashState)
    {
        u8_RefVarFlashCount = 0;
        u8_NumberFlashCount = U8_FLASH_COUNT_MAX;
        u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
    }
    else
    {
        u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;
    }
    u8_DisplayRefVarSet = setpoint;
}

void Set_LockDisplay(uint8_t flashCount)
{
    u8_LockFlashCount = flashCount;
}

static void RefDisp_UnlockStatus(void)
{
    UserMode_t user_mode = Get_UserMode();
    uint8_t ref_setpoint = Get_ManualRefSetTemp();

    if((0 != u8_FaultOccursNumber) && (U8_FLASH_COUNT_MAX == u8_NumberFlashCount) && (false == b_NumberFlashState))
    {
        st_DispFaultCode.u16_Word = Get_CurrentDispFaultCode();
        Set_88HighLed(st_DispFaultCode.sByte.u8_MSB);
        Set_88LowLed(st_DispFaultCode.sByte.u8_LSB);

        Set_NegativeSignLedState(false);
        Set_DegreeCentigradeLedState(false);
    }
    else
    {
        if((0 == u8_NumberFlashCount) || (U8_FLASH_COUNT_MAX == u8_NumberFlashCount))
        {
            b_NumberLedFlashState = true;
        }
        else if(0 == (u8_NumberFlashCount % 5))
        {
            b_NumberLedFlashState = !b_NumberLedFlashState;
        }

        if((U8_FLASH_COUNT_MAX == u8_NumberFlashCount) && (true == b_NumberFlashState))
        {
            b_NumberFlashState = false;
            if(Get_RefSetTemp() != u8_RefDisplayValue)
            {
                if(ref_setpoint != u8_RefDisplayValue)
                {
                    Update_ManualRefSetTemp(u8_RefDisplayValue);
                }
                if(((UserMode_t)eFuzzy_Mode == user_mode) || ((UserMode_t)eTurboCool_Mode == user_mode))
                {
                    Set_UserMode((UserMode_t)eManual_Mode);
                    u8_DisplayUserMode = (UserMode_t)eManual_Mode;
                }
                Update_RefSetTemp(u8_RefDisplayValue);
            }
        }

        if(false == b_NumberLedFlashState)
        {
            Set_88HighLed(CON_DispNC);
            Set_88LowLed(CON_DispNC);
        }
        else
        {
            Set_88HighLed(CON_DispNC);
            Set_88LowLed(u8_RefDisplayValue - 1); // TODO
        }
        Set_NegativeSignLedState(false);
        Set_DegreeCentigradeLedState(true);
    }
}

static void FrzDisp_UnlockStatus(void)
{
    uint8_t u8_high;
    uint8_t u8_low;
    UserMode_t user_mode = Get_UserMode();
    uint8_t frz_setpoint = Get_ManualFrzSetTemp();

    if((0 != u8_FaultOccursNumber) && (U8_FLASH_COUNT_MAX == u8_NumberFlashCount) && (false == b_NumberFlashState))
    {
        st_DispFaultCode.u16_Word = Get_CurrentDispFaultCode();
        Set_88HighLed(st_DispFaultCode.sByte.u8_MSB);
        Set_88LowLed(st_DispFaultCode.sByte.u8_LSB);

        Set_NegativeSignLedState(false);
        Set_DegreeCentigradeLedState(false);
    }
    else
    {
        if((0 == u8_NumberFlashCount) || (U8_FLASH_COUNT_MAX == u8_NumberFlashCount))
        {
            b_NumberLedFlashState = true;
        }
        else if(0 == (u8_NumberFlashCount % 5))
        {
            b_NumberLedFlashState = !b_NumberLedFlashState;
        }

        if((U8_FLASH_COUNT_MAX == u8_NumberFlashCount) && (true == b_NumberFlashState))
        {
            b_NumberFlashState = false;
            if(Get_FrzSetTemp() != u8_FrzDisplayValue)
            {
                if(frz_setpoint != u8_FrzDisplayValue)
                {
                    Update_ManualFrzSetTemp(u8_FrzDisplayValue);
                }
                if(((UserMode_t)eFuzzy_Mode == user_mode) || ((UserMode_t)eTurboFreeze_Mode == user_mode))
                {
                    Set_UserMode((UserMode_t)eManual_Mode);
                    u8_DisplayUserMode = (UserMode_t)eManual_Mode;
                }
                Update_FrzSetTemp(u8_FrzDisplayValue);
            }
        }

        if(false == b_NumberLedFlashState)
        {
            Set_88HighLed(CON_DispNC);
            Set_88LowLed(CON_DispNC);
            Set_NegativeSignLedState(false);
        }
        else
        {
            u8_high = (30 - u8_FrzDisplayValue) / 10;
            u8_low = (30 - u8_FrzDisplayValue) % 10; // TODO

            Set_88HighLed(u8_high);
            Set_88LowLed(u8_low);
            Set_NegativeSignLedState(true);
        }
        Set_DegreeCentigradeLedState(true);
    }
}

static void ModeDisp_UnlockStatus(void)
{
    if((0 == u8_ModeFlashCount) || (U8_FLASH_COUNT_MAX == u8_ModeFlashCount))
    {
        b_ModeLedFlashState = true;
    }
    else if(0 == (u8_ModeFlashCount % 5))
    {
        b_ModeLedFlashState = !b_ModeLedFlashState;
    }

    if(false == b_ModeLedFlashState)
    {
        Set_ModeLedState((UserMode_t)eManual_Mode);
    }
    else
    {
        Set_ModeLedState(u8_DisplayUserMode);
    }

    // Confirm mode Setting
    if((U8_FLASH_COUNT_MAX == u8_ModeFlashCount) && (true == b_ModeFlashState))
    {
        b_ModeFlashState = false;
        if(Get_UserMode() != u8_DisplayUserMode)
        {
            Set_UserMode(u8_DisplayUserMode);
        }

        switch(u8_DisplayUserMode)
        {
            case(UserMode_t)eFuzzy_Mode:
                u8_RefDisplayValue = Get_RefSetTemp();
                u8_FrzDisplayValue = Get_FrzSetTemp();
                break;
            case(UserMode_t)eTurboCool_Mode:
                u8_RefDisplayValue = Get_RefSetTemp();
                break;
            case(UserMode_t)eTurboFreeze_Mode:
                u8_FrzDisplayValue = Get_FrzSetTemp();
                break;
            default:
                break;
        }
    }
}

static void RefVarDisp_UnlockStatus(void)
{
    if((0 == u8_RefVarFlashCount) || (U8_FLASH_COUNT_MAX == u8_RefVarFlashCount))
    {
        b_RefVarLedFlashState = true;
    }
    else if(0 == (u8_RefVarFlashCount % 5))
    {
        b_RefVarLedFlashState = !b_RefVarLedFlashState;
    }

    if(false == b_RefVarLedFlashState)
    {
        Set_RefVarLedState((RefVarSet_t)eRefVar_Max);
    }
    else
    {
        Set_RefVarLedState(u8_DisplayRefVarSet);
    }

    // Confirm mode Setting
    if((U8_FLASH_COUNT_MAX == u8_RefVarFlashCount) && (true == b_RefVarFlashState))
    {
        b_RefVarFlashState = false;
        if(Get_RefVarSetTemp() != u8_DisplayRefVarSet)
        {
            Update_RefVarSetTemp(u8_DisplayRefVarSet);
        }
    }
}

static void RefDisp_LockStatus(void)
{
    if((0 != u8_FaultOccursNumber) && (U8_FLASH_COUNT_MAX == u8_NumberFlashCount))
    {
        st_DispFaultCode.u16_Word = Get_CurrentDispFaultCode();
        Set_88HighLed(st_DispFaultCode.sByte.u8_MSB);
        Set_88LowLed(st_DispFaultCode.sByte.u8_LSB);

        Set_NegativeSignLedState(false);
        Set_DegreeCentigradeLedState(false);
    }
    else
    {
        u8_RefDisplayValue = Get_RefSetTemp();
        Set_88HighLed(CON_DispNC);
        Set_88LowLed(u8_RefDisplayValue - 1);

        Set_NegativeSignLedState(false);
        Set_DegreeCentigradeLedState(true);
    }
}

static void FrzDisp_LockStatus(void)
{
    uint8_t u8_high;
    uint8_t u8_low;

    if((0 != u8_FaultOccursNumber) && (U8_FLASH_COUNT_MAX == u8_NumberFlashCount))
    {
        st_DispFaultCode.u16_Word = Get_CurrentDispFaultCode();
        Set_88HighLed(st_DispFaultCode.sByte.u8_MSB);
        Set_88LowLed(st_DispFaultCode.sByte.u8_LSB);

        Set_NegativeSignLedState(false);
        Set_DegreeCentigradeLedState(false);
    }
    else
    {
        u8_FrzDisplayValue = Get_FrzSetTemp();

        u8_high = (30 - u8_FrzDisplayValue) / 10;
        u8_low = (30 - u8_FrzDisplayValue) % 10; // TODO

        Set_88HighLed(u8_high);
        Set_88LowLed(u8_low);
        Set_NegativeSignLedState(true);
        Set_DegreeCentigradeLedState(true);
    }
}

void Update_ZoneDispData(ZoneDisp_st *pst_disp)
{
    Set_NegativeSignLedState(pst_disp->b_Minus);
    Set_DegreeCentigradeLedState(pst_disp->b_C);
    Set_88HighLed(pst_disp->u8_High);
    Set_88LowLed(pst_disp->u8_Low);
}

void Set_FactoryResetDisplay(void)
{
    u8_NumberFlashCount = 0;
    u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
    u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;

    b_FactoryResetDisplay = true;
}

static void Display_FactoryReset(void)
{
    Set_88HighLed(CON_DispP);
    Set_88LowLed(CON_DispR);

    Set_NegativeSignLedState(false);
    Set_DegreeCentigradeLedState(false);

    Set_LockLedState(b_LockFlashState);
    Set_ModeLedState(eManual_Mode);
    Set_RefVarLedState(eRefVar_Max);

    if(U8_FLASH_COUNT_MAX == u8_NumberFlashCount)
    {
        b_FactoryResetDisplay = false;
    }
}

void Set_ShowRoomDisplay(void)
{
    u8_NumberFlashCount = 0;
    u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
    u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;

    b_ShowRoomDisplay = true;
}

static void Display_ShowRoom(void)
{
    Set_88HighLed(CON_DispO);
    Set_88LowLed(CON_DispN);

    Set_NegativeSignLedState(false);
    Set_DegreeCentigradeLedState(false);

    Set_LockLedState(b_LockFlashState);
    Set_ModeLedState(eManual_Mode);
    Set_RefVarLedState(eRefVar_Max);

    if(U8_FLASH_COUNT_MAX == u8_NumberFlashCount)
    {
        b_ShowRoomDisplay = false;
    }
}

void Set_ForceHeaterDisplay(bool state)
{
    u8_NumberFlashCount = 0;
    u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
    u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;

    b_ForceHeater = state;
    b_ForceHeateDisplay = true;
}

static void Display_ForceHeater(void)
{
    if(false == b_ForceHeater)
    {
        Set_88HighLed(CON_DispO);
        Set_88LowLed(CON_DispN);
    }
    else
    {
        Set_88HighLed(CON_DispO);
        Set_88LowLed(CON_DispF);
    }

    Set_NegativeSignLedState(false);
    Set_DegreeCentigradeLedState(false);

    Set_LockLedState(b_LockFlashState);
    Set_ModeLedState(eManual_Mode);
    Set_RefVarLedState(eRefVar_Max);

    if(U8_FLASH_COUNT_MAX == u8_NumberFlashCount)
    {
        b_ForceHeateDisplay = false;
    }
}

void Set_PcbTestDisplay(void)
{
    u8_NumberFlashCount = U8_FLASH_COUNT_MAX;
    u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
    u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;
    u8_AnimationCount = 0;

    b_PcbTestDisplay = true;
}

void Display_PcbTestAnimation(void)
{
    if(u8_AnimationCount < 30)
    {
        Set_AllLedsState(true);
    }
    else if(u8_AnimationCount < 40)
    {
        Set_AllLedsState(false);
    }
    else if(u8_AnimationCount < 50)
    {
        Set_WiFiLedState(true);
    }
    else if(u8_AnimationCount < 60)
    {
        Set_WiFiLedState(false);
        Set_88HighLed(CON_Disp8);
        Set_88LowLed(CON_Disp8);
        Set_DegreeCentigradeLedState(true);
        Set_NegativeSignLedState(true);
        Set_LockLedState(true);
    }
    else if(u8_AnimationCount < 70)
    {
        Set_88HighLed(CON_DispNC);
        Set_88LowLed(CON_DispNC);
        Set_DegreeCentigradeLedState(false);
        Set_NegativeSignLedState(false);
        Set_LockLedState(false);
        Set_TreasureLedState(true);
        Set_BabyLedState(true);
        Set_ZeroLedState(true);
    }
    else if(u8_AnimationCount < 80)
    {
        Set_TreasureLedState(false);
        Set_BabyLedState(false);
        Set_ZeroLedState(false);
        Set_FuzzyLedState(true);
        Set_TurboCoolLedState(true);
        Set_TurboFreezeLedState(true);
    }
    else if(u8_AnimationCount < 90)
    {
        Set_AllLedsState(false);
        b_PcbTestDisplay = false;
    }
    u8_AnimationCount++;
}

static void Process_LockDisplay(void)
{
    if((0 == u8_LockFlashCount) || (U8_LOCK_FLASH_COUNT_MAX == u8_LockFlashCount))
    {
        b_LockFlashState = true;
    }
    else if(0 == (u8_LockFlashCount % 3))
    {
        b_LockFlashState = !b_LockFlashState;
    }

    if(true == b_ForceHeateDisplay)
    {
        Display_ForceHeater();
    }
    else if(true == b_PcbTestDisplay)
    {
        Display_PcbTestAnimation();
    }
    else
    {
        Set_LockLedState(b_LockFlashState);
        u8_DisplayUserMode = Get_UserMode();
        Set_ModeLedState(u8_DisplayUserMode);
        u8_DisplayRefVarSet = Get_RefVarSetTemp();
        Set_RefVarLedState(u8_DisplayRefVarSet);

        switch(u8_DisplayZone)
        {
            case(DisplayZone_t)eDispRef_Zone:
                RefDisp_LockStatus();
                break;
            case(DisplayZone_t)eDispFrz_Zone:
                FrzDisp_LockStatus();
                break;
            default:
                break;
        }
    }
}

void Refresh_Display(void)
{
    u8_RefDisplayValue = Get_RefSetTemp();
    u8_FrzDisplayValue = Get_FrzSetTemp();
    u8_DisplayUserMode = Get_UserMode();
    u8_DisplayRefVarSet = Get_RefVarSetTemp();
    u8_DisplayZone = (DisplayZone_t)eDispRef_Zone;
    u8_NumberFlashCount = U8_FLASH_COUNT_MAX;
    u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
    u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;
    u8_LockFlashCount = U8_LOCK_FLASH_COUNT_MAX;
}

void Set_TestModeDisplay(bool flashState)
{
    b_NumberFlashState = flashState;
    if(true == b_NumberFlashState)
    {
        u8_NumberFlashCount = 0;
        u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
        u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;
    }
    else
    {
        u8_NumberFlashCount = U8_FLASH_COUNT_MAX;
    }

    b_TestModeDisplay = true;
}

static void Display_TestMode(void)
{
    uint8_t test_mode = Get_TestMode();
    uint8_t u8_high;
    uint8_t u8_low;

    switch(test_mode)
    {
        case TMODE_FORCE_COOLING:
            u8_high = CON_Disp9;
            u8_low = CON_Disp9;
            break;
        case TMODE_FORCE_DEFROST:
            u8_high = CON_DispH;
            u8_low = CON_DispH;
            break;
        case TMODE_FORCE_ENERGY:
            u8_high = CON_DispP;
            u8_low = CON_Disp0;
            break;
        case TMODE_TT:
            u8_high = CON_DispT;
            u8_low = CON_DispT;
            break;
        default:
            break;
    }

    if((0 == u8_NumberFlashCount) || (U8_FLASH_COUNT_MAX == u8_NumberFlashCount))
    {
        b_NumberLedFlashState = true;
    }
    else if(0 == (u8_NumberFlashCount % 5))
    {
        b_NumberLedFlashState = !b_NumberLedFlashState;
    }

    if((U8_FLASH_COUNT_MAX == u8_NumberFlashCount) && (true == b_NumberFlashState))
    {
        b_NumberFlashState = false;
    }

    if(false == b_NumberLedFlashState)
    {
        Set_88HighLed(CON_DispNC);
        Set_88LowLed(CON_DispNC);
    }
    else
    {
        Set_88HighLed(u8_high);
        Set_88LowLed(u8_low);
    }

    Set_NegativeSignLedState(false);
    Set_DegreeCentigradeLedState(false);

    Set_ModeLedState(eManual_Mode);
    Set_RefVarLedState(eRefVar_Max);

    if(0 == test_mode)
    {
        b_TestModeDisplay = false;
    }
}

void Set_QueryModeDisplay(void)
{
    u8_NumberFlashCount = 0;
    u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
    u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;
    b_QueryModeDisplay = true;
}

void Set_AdjustParaDisplay(uint8_t display_value, bool display_state)
{
    u8_AdjustParaDisplayValue = display_value;
    b_AdjustParaDisplay = display_state;
}

static uint16_t Get_QueryTemp(uint16_t u16_data)
{
    u16_data /= 10;
    if(u16_data < 50)
    {
        u16_data = 50 - u16_data;
        b_QueryModeNegativeState = true;
    }
    else
    {
        u16_data = u16_data - 50;
        b_QueryModeNegativeState = false;
    }
    return (u16_data);
}

static uint8_t Get_HighDisplay(uint8_t value)
{
    uint8_t high_value;
    if(0 == value)
    {
        high_value = CON_DispNC;
    }
    else if(10 == value)
    {
        high_value = CON_DispA;
    }
    else if(11 == value)
    {
        high_value = CON_DispB;
    }
    else if(12 == value)
    {
        high_value = CON_DispC;
    }
    else if(13 == value)
    {
        high_value = CON_DispD;
    }
    else if(14 == value)
    {
        high_value = CON_DispE;
    }
    else if(15 == value)
    {
        high_value = CON_DispF;
    }
    else
    {
        high_value = value;
    }
    return high_value;
}

static void Display_QueryMode(void)
{
    uint8_t query_mode = Get_QueryMode();
    uint8_t u8_high;
    uint8_t u8_low;
    bool err_state;
    uint16_t u16_data;
    int8_t i8_data;
    bool state;

    Set_DegreeCentigradeLedState(false);
    Set_ModeLedState(eManual_Mode);
    Set_RefVarLedState(eRefVar_Max);

    switch(query_mode)
    {
        case CHECK_SHOWROOM_MODE:
            u8_high = CON_DispNC;
            u8_low = CON_Disp0;
            b_AdjustParaDisplay = false;
            break;
        case CHECK_REF_SNR:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispR;
                u8_low = CON_Disp5;
                b_QueryModeNegativeState = false;
            }
            else
            {
                u16_data = Get_SensorValue((SensorType_t)SENSOR_REF);
                u16_data = Get_QueryTemp(u16_data);
                u8_high = u16_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = u16_data % 10;
            }
            break;
        case CHECK_REF_VAR_SNR:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispU;
                u8_low = CON_Disp5;
                b_QueryModeNegativeState = false;
            }
            else
            {
                u16_data = Get_SensorValue((SensorType_t)SENSOR_VV);
                u16_data = Get_QueryTemp(u16_data);
                u8_high = u16_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = u16_data % 10;
            }
            break;
        case CHECK_FRZ_SNR:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispF;
                u8_low = CON_Disp5;
                b_QueryModeNegativeState = false;
            }
            else
            {
                u16_data = Get_SensorValue((SensorType_t)SENSOR_FRZ);
                u16_data = Get_QueryTemp(u16_data);
                u8_high = u16_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = u16_data % 10;
            }
            break;
        case CHECK_FRZ_DEF_SNR:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispF;
                u8_low = CON_DispD;
                b_QueryModeNegativeState = false;
            }
            else
            {
                u16_data = Get_SensorValue((SensorType_t)SENSOR_DEFROST);
                u16_data = Get_QueryTemp(u16_data);
                u8_high = u16_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = u16_data % 10;
            }
            break;
        case CHECK_ROOM_SNR:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispE;
                u8_low = CON_Disp5;
                b_QueryModeNegativeState = false;
            }
            else
            {
                u16_data = Get_SensorValue((SensorType_t)SENSOR_ROOM);
                u16_data = Get_QueryTemp(u16_data);
                u8_high = u16_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = u16_data % 10;
            }
            break;
        case CHECK_ROOM_HUMI:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispC;
                u8_low = CON_DispH;
            }
            else
            {
                u16_data = Get_HumidityRange();
                u16_data = u16_data * 5 + 10;
                u8_high = u16_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = u16_data % 10;
            }
            b_QueryModeNegativeState = false;
            break;
        case CHECK_COMP:
            if(true == b_AdjustParaDisplay)
            {
                u16_data = u8_AdjustParaDisplayValue;
                if(u16_data >= 100)
                {
                    b_QueryModeNegativeState = true;
                    u16_data -= 100;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = u16_data / 10;
                u8_low = u16_data % 10;
            }
            else if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispC;
                u8_low = CON_Disp0;
            }
            else
            {
                u16_data = Get_CompFeedbackFreq();
                if(u16_data >= 100)
                {
                    b_QueryModeNegativeState = true;
                    u16_data -= 100;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = u16_data / 10;
                if(0 == u8_high)
                {
                    u8_high = CON_DispNC;
                }
                u8_low = u16_data % 10;
            }
            break;
        case CHECK_REF_DAMPER:
            b_AdjustParaDisplay = false;
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispD;
                u8_low = CON_DispR;
            }
            else
            {
                state = Get_ActiveDamperState();
                if(true == state)
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp1;
                }
                else
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp0;
                }
            }
            b_QueryModeNegativeState = false;
            break;
        case CHECK_REF_VAR_DAMPER:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispD;
                u8_low = CON_DispC;
            }
            else
            {
                state = Get_SlaveDamperState();
                if(true == state)
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp1;
                }
                else
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp0;
                }
            }
            b_QueryModeNegativeState = false;
            break;
        case CHECK_DEF_HEATER:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispH;
                u8_low = CON_DispF;
            }
            else
            {
                if(Get_ResolvedDeviceStatus(DEVICE_FrzDefHeater) == (uint8_t)DS_On)
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp1;
                }
                else
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp0;
                }
            }
            b_QueryModeNegativeState = false;
            b_AdjustParaDisplay = false;
            break;
        case CHECK_FRZ_FAN_DUTY:
            if(true == b_AdjustParaDisplay)
            {
                u16_data = u8_AdjustParaDisplayValue;
                u8_high = u16_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = u16_data % 10;
            }
            else if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispF;
                u8_low = CON_DispF;
            }
            else
            {
                u16_data = Get_FanDuty(FRZ_FAN);
                u8_high = u16_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = u16_data % 10;
            }
            b_QueryModeNegativeState = false;
            break;
        case CHECK_COND_FAN_DUTY:
            if(true == b_AdjustParaDisplay)
            {
                u16_data = u8_AdjustParaDisplayValue * 5 + 25;
                u8_high = u16_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = u16_data % 10;
            }
            else if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispL;
                u8_low = CON_DispF;
            }
            else
            {
                u16_data = Get_FanParameter(COOL_FAN);
                u16_data /= 10;
                u8_high = u16_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = u16_data % 10;
            }
            b_QueryModeNegativeState = false;
            break;
        case CHECK_REF_LEFT_DOOR_SW:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_Disp5;
                u8_low = CON_DispL;
            }
            else
            {
                state = Get_DoorSwitchState(DOOR_REF_LEFT);
                if(true == state)
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp1;
                }
                else
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp0;
                }
            }
            b_QueryModeNegativeState = false;
            b_AdjustParaDisplay = false;
            break;
        case CHECK_REF_RIGHT_DOOR_SW:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_Disp5;
                u8_low = CON_DispR;
            }
            else
            {
                state = Get_DoorSwitchState(DOOR_REF_RIGHT);
                if(true == state)
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp1;
                }
                else
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp0;
                }
            }
            b_QueryModeNegativeState = false;
            break;
        case CHECK_FRZ_LEFT_DOOR_SW:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_Disp5;
                u8_low = CON_Disp5;
            }
            else
            {
                state = Get_DoorSwitchState(DOOR_FRZ_LEFT);
                if(true == state)
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp1;
                }
                else
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp0;
                }
            }
            b_QueryModeNegativeState = false;
            break;
        case CHECK_FRZ_RIGHT_DOOR_SW:
            if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_Disp5;
                u8_low = CON_DispF;
            }
            else
            {
                state = Get_DoorSwitchState(DOOR_FRZ_RIGHT);
                if(true == state)
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp1;
                }
                else
                {
                    u8_high = CON_DispNC;
                    u8_low = CON_Disp0;
                }
            }
            b_QueryModeNegativeState = false;
            b_AdjustParaDisplay = false;
            break;
        case CHECK_REF_ON:
            if(true == b_AdjustParaDisplay)
            {
                i8_data = (int8_t)u8_AdjustParaDisplayValue;
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            else if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispR;
                u8_low = CON_Disp1;
            }
            else
            {
                i8_data = (int8_t)Get_RefOnTempMicroAdjustParm();
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            break;
        case CHECK_REF_OFF:
            if(true == b_AdjustParaDisplay)
            {
                i8_data = (int8_t)u8_AdjustParaDisplayValue;
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            else if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispR;
                u8_low = CON_Disp0;
            }
            else
            {
                i8_data = (int8_t)Get_RefOffTempMicroAdjustParm();
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            break;
        case CHECK_FRZ_ON:
            if(true == b_AdjustParaDisplay)
            {
                i8_data = (int8_t)u8_AdjustParaDisplayValue;
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            else if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispF;
                u8_low = CON_Disp1;
            }
            else
            {
                i8_data = (int8_t)Get_FrzOnTempMicroAdjustParm();
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            break;
        case CHECK_FRZ_OFF:
            if(true == b_AdjustParaDisplay)
            {
                i8_data = (int8_t)u8_AdjustParaDisplayValue;
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            else if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispF;
                u8_low = CON_Disp0;
            }
            else
            {
                i8_data = (int8_t)Get_FrzOffTempMicroAdjustParm();
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            break;
        case CHECK_VAR_ON:
            if(true == b_AdjustParaDisplay)
            {
                i8_data = (int8_t)u8_AdjustParaDisplayValue;
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            else if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispU;
                u8_low = CON_Disp1;
            }
            else
            {
                i8_data = (int8_t)Get_RefVarOnTempMicroAdjustParm();
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            break;
        case CHECK_VAR_OFF:
            if(true == b_AdjustParaDisplay)
            {
                i8_data = (int8_t)u8_AdjustParaDisplayValue;
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            else if((u8_QueryMode100msCount % 10) < 5)
            {
                u8_high = CON_DispU;
                u8_low = CON_Disp0;
            }
            else
            {
                i8_data = (int8_t)Get_RefVarOffTempMicroAdjustParm();
                if(i8_data < 0)
                {
                    b_QueryModeNegativeState = true;
                    i8_data = 0 - i8_data;
                }
                else
                {
                    b_QueryModeNegativeState = false;
                }
                u8_high = i8_data / 10;
                u8_high = Get_HighDisplay(u8_high);
                u8_low = i8_data % 10;
            }
            break;
        default:
            break;
    }
    Set_88HighLed(u8_high);
    Set_88LowLed(u8_low);
    Set_NegativeSignLedState(b_QueryModeNegativeState);

    if(0 == query_mode)
    {
        b_QueryModeDisplay = false;
        b_AdjustParaDisplay = false;
        u8_QueryMode100msCount = 0;
    }
    else
    {
        u8_QueryMode100msCount++;
    }
}

static void Process_UnlockDisplay(void)
{
    Set_LockLedState(false);

    if(true == b_FactoryResetDisplay)
    {
        Display_FactoryReset();
    }
    else if(true == b_ShowRoomDisplay)
    {
        Display_ShowRoom();
    }
    else if(true == b_QueryModeDisplay)
    {
        Display_QueryMode();
    }
    else if(true == b_TestModeDisplay)
    {
        Display_TestMode();
    }
    else
    {
        ModeDisp_UnlockStatus();
        RefVarDisp_UnlockStatus();
        switch(u8_DisplayZone)
        {
            case(DisplayZone_t)eDispRef_Zone:
                RefDisp_UnlockStatus();
                break;
            case(DisplayZone_t)eDispFrz_Zone:
                FrzDisp_UnlockStatus();
                break;
            default:
                break;
        }
    }
}

void Process_WiFiDisplay(void)
{
    net_state_e net_state;
    bool fct_wifi_led_state = false;

    fct_wifi_led_state = Get_FctWifiLedState();

    if(true == b_QueryModeDisplay)
    {
        u8_Wifi100msCount = 0;
    }
    else if(true == fct_wifi_led_state)
    {
        Set_WiFiLedState(true);
        u8_Wifi100msCount = 0;
    }
    else
    {
        net_state = get_dev_net_state();

        switch(net_state)
        {
            case ZM_APP_NET_STATE_OFFLINE:
                if(u8_Wifi100msCount % 3)
                {
                    Set_WiFiLedState(true);
                }
                else
                {
                    Set_WiFiLedState(false);
                }
                break;
            case ZM_APP_NET_STATE_UPDATING:
                b_WiFiFactoryConnected = true;
                break;
            case ZM_APP_NET_STATE_UAP:
                if((u8_Wifi100msCount % 10) < 2)
                {
                    Set_WiFiLedState(true);
                }
                else
                {
                    Set_WiFiLedState(false);
                }
                break;
            case ZM_APP_NET_STATE_UNPROV:
                Set_WiFiLedState(false);
                break;
            case ZM_APP_NET_STATE_UPDATING_AUTO:
            case ZM_APP_NET_STATE_UPDATING_FORCE:
                break;
            case ZM_APP_NET_STATE_LOCAL:
                if((u8_Wifi100msCount % 10) < 5)
                {
                    Set_WiFiLedState(true);
                }
                else
                {
                    Set_WiFiLedState(false);
                }
                b_WiFiFactoryConnected = true;
                break;
            case ZM_APP_NET_STATE_CLOUD:
                b_WiFiFactoryConnected = true;
                Set_WiFiLedState(true);
                break;
            default:
                Set_WiFiLedState(false);
                break;
        }
        u8_Wifi100msCount++;
    }
}

void Update_DisplayStatus(void)
{
    bool lock_state = Get_LockStatus();

    u8_FaultOccursNumber = Get_FaultCodeNumber();
    if(0 != u8_FaultOccursNumber)
    {
        st_DispFaultCode.u16_Word = Get_CurrentDispFaultCode();
    }

    Set_FuzzyLedState(false);
    Set_TurboCoolLedState(false);
    Set_TurboFreezeLedState(false);
    Process_WiFiDisplay();

    if(true == lock_state)
    {
        Process_LockDisplay();
    }
    else
    {
        Process_UnlockDisplay();
    }

    if(U8_FLASH_COUNT_MAX > u8_ModeFlashCount)
    {
        u8_ModeFlashCount++;
    }

    if(U8_FLASH_COUNT_MAX > u8_RefVarFlashCount)
    {
        u8_RefVarFlashCount++;
    }

    if(U8_FLASH_COUNT_MAX > u8_NumberFlashCount)
    {
        u8_NumberFlashCount++;
    }

    if(U8_LOCK_FLASH_COUNT_MAX > u8_LockFlashCount)
    {
        u8_LockFlashCount++;
    }
}

void Init_DisplayStatus(void)
{
    u8_RefDisplayValue = Get_RefSetTemp();
    u8_FrzDisplayValue = Get_FrzSetTemp();
    u8_DisplayUserMode = Get_UserMode();
    u8_DisplayRefVarSet = Get_RefVarSetTemp();
    u8_DisplayZone = (DisplayZone_t)eDispRef_Zone;
    u8_NumberFlashCount = U8_FLASH_COUNT_MAX;
    u8_ModeFlashCount = U8_FLASH_COUNT_MAX;
    u8_RefVarFlashCount = U8_FLASH_COUNT_MAX;
    u8_LockFlashCount = U8_LOCK_FLASH_COUNT_MAX;
}

bool Get_WifiConnectState(void)
{
    return b_WiFiFactoryConnected;
}

uint8_t Get_DisplayByte1Value(void)
{
    return (u8_DisplayByte1);
}

uint8_t Get_DisplayByte2Value(void)
{
    return (u8_DisplayByte2);
}

uint8_t Get_DisplayByte3Value(void)
{
    return (u8_DisplayByte3);
}
