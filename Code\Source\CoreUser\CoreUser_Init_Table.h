/*!
 * @file
 * @brief This file contains the constant tables used for the Init module.
 *        This header file MUST only be included in the one file that needs access to the tables.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __COREUSERINITTABLE_H__
#define __COREUSERINITTABLE_H__

#include "Core_Init.h"
#include "Core_TimeBase.h"
#include "Core_Scheduler.h"
#include "Core_CallBackTimer.h"
#include "ResolverDevice.h"
#include "Driver_GradualLamp.h"
#include "Driver_DoubleDamper.h"
#include "Driver_CompFrequency.h"
#include "Driver_Flash.h"
#include "SystemTimerModule.h"
#include "DisplayUsart.h"
#include "InverterUsart.h"
#include "SystemManager.h"
#include "DisplayInterface.h"
#include "ParameterManager.h"
#include "miio_api.h"

/*!
 * @brief This is the init table -- it contains an entry for each module's init function.
 *        The entries are TInitItem structure, containing a pointer to the init function.
 */
static const st_CoreInitItem CoreUser_Init_aInitTable[] = {
    { Core_TimeBase_Init },
    { Core_CallbackTimer_Init },
    { Core_Scheduler_Init },
    { Init_Flash },
    { ParameterManagerInit },
    { Init_ResolverDeviceData },
    { Init_GradualLamp },
    { Init_DoubleDamper },
    { Driver_CompFreqInit },
    { Init_SystemTimer },
    { Init_UartDisplay },
    { Init_UartInverter },
    { SystemManager_Init },
    { UserInterface_Init },
    { Miio_Init }
};

#endif
