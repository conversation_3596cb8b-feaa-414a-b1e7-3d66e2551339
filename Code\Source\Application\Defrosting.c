/*!
 * @file
 * @brief Manages all the state variables of the fridge runner.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Defrosting.h"
#include "Core_CallBackTimer.h"
#include "FridgeRunner.h"
#include "CoolingCycle.h"
#include "Parameter_Device.h"
#include "Driver_DoorSwitch.h"
#include "ResolverDevice.h"
#include "Driver_Fan.h"
#include "Driver_DoubleDamper.h"
#include "IO_Device.h"
#include "SystemTimerModule.h"

#define U16_DEFROSTING_CYCLE_SECOND (uint16_t)1

enum
{
    Signal_Entry = SimpleFsmSignal_Entry,
    Signal_Exit = SimpleFsmSignal_Exit,
    Signal_PollTimerExpired = SimpleFsmSignal_UserStart,
    Signal_DefrostingStateUpdated
};

static st_CoreCallbackTimer st_DefrostingTimer;
static SimpleFsm_t st_DefrostingFsm;
static DefrostMode_t defrostMode;
static Defrosting_st st_Defrosting;
static uint16_t u16_DefrostingtimeCount;
static ZoneCoolingState_t zoneDefrostPreCoolingState;
static ZoneCoolingState_t zoneWaitCoolingState;
static void DefrostingState_PreCooling(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void DefrostingState_PreHold(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void DefrostingState_Defrosting(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void DefrostingState_PreWaitStart(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void DefrostingState_AfterStop(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void DefrostingState_AfterComp(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void DefrostingState_AfterWaitStart(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void DefrostingState_AfterFan(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void DefrostingState_AfterDamper(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void DefrostingState_Completed(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);

static void PollTimerExpired(void)
{
    SimpleFsm_SendSignal(&st_DefrostingFsm, Signal_PollTimerExpired, NULL);
}

static void Defrosting_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_DefrostingTimer,
        PollTimerExpired,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_DefrostingTimer);
}

static void DefrostingState_PreWaitStart(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    CoolingCompState_t comp_state = Get_CoolingCompState();
    uint16_t u16_comp_off_time = Get_CompOffTimeSecond();
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t fan_duty = Get_CompOffFanSettingIndex(room_range);
    bool varcooling_state = CoolingCycle_GetRefVarCoolingState();

    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            defrostMode = (DefrostMode_t)eDefrostMode_PreWaitStart;
            u16_DefrostingtimeCount = u16_comp_off_time;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_DefrostingtimeCount++;
            if((CoolingCompState_t)eCooling_CompOff == comp_state)
            {
                if(COOLING_PROTECT_TIME_SECONDS <= u16_DefrostingtimeCount)
                {
                    SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_PreCooling);
                }
                else
                {
                    if(true == varcooling_state)
                    {
                        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLOPEN);
                        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, fan_duty);
                    }
                    else
                    {
                        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLCLOSE);
                        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, 0);
                    }
                }
            }
            else
            {
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_PreCooling);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            break;
        default:
            break;
    }
}

static void DefrostingProcess_RefVarZoneCoolingState(void)
{
    bool cooling_state = CoolingCycle_GetRefVarCoolingState();

    if(true == cooling_state)
    {
        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLOPEN);
    }
    else
    {
        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLCLOSE);
    }
}

static void PreCooling_ProcessZoneCoolingState(void)
{
    uint16_t ref_off_temp = Get_RefZoneOffTemp();
    uint16_t ref_snr_temp = Get_SensorValue((SensorType_t)SENSOR_REF);
    uint16_t frz_snr_temp = Get_SensorValue((SensorType_t)SENSOR_FRZ);
    uint16_t frz_off_temp = Get_FrzZoneOffTemp();
    uint16_t frz_cooling_max_second = 0;
    RoomTempRange_t room_range = Get_RoomTempRange();

    if(u16_DefrostingtimeCount < U16_PRE_COOLING_REF_FRZ_MAX_TIME_SECONDS)
    {
        zoneDefrostPreCoolingState = PreCooling_GetFirstCoolingState();
    }
    else
    {
        if((room_range == RT_BELOW13) || (room_range == RT_UP40))
        {
            frz_cooling_max_second =
                U16_PRE_COOLING_REF_FRZ_MAX_TIME_SECONDS + U16_PRE_COOLING_FRZ_MAX_SECOND;
            if(u16_DefrostingtimeCount < frz_cooling_max_second)
            {
                zoneDefrostPreCoolingState = PreCooling_GetSecondCoolingState();
            }
            else
            {
                zoneDefrostPreCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
            }
        }
        else
        {
            zoneDefrostPreCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
        }
    }
}

static void PreCooling_SetOutputState(ZoneCoolingState_t state)
{
    uint8_t freq_index =
        Get_CompFreqIndex(eCoolingCapacity_Normal, st_Defrosting.u16_PreCoolingCompStillOnTimeMinute);
    uint8_t cooling_cycle_freq_index = Get_CompOnFreqIndex();
    uint8_t fan_duty = Get_CompOnFanSettingIndex(freq_index);
    uint8_t cond_fan_duty = Get_CondFanSettingIndex(freq_index);

    if(cooling_cycle_freq_index > freq_index)
    {
        freq_index = cooling_cycle_freq_index;
    }

    switch(state)
    {
        case(ZoneCoolingState_t)eZoneCooling_Idle:
            SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_PreHold);
            break;
        case(ZoneCoolingState_t)eZoneCooling_Ref:
        case(ZoneCoolingState_t)eZoneCooling_RefFrz:
            if(u16_DefrostingtimeCount < FAN_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            }
            else if(u16_DefrostingtimeCount < DAMPER_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, fan_duty);
            }
            else
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, fan_duty);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLOPEN);
            }
            break;
        case(ZoneCoolingState_t)eZoneCooling_Frz:
            if(u16_DefrostingtimeCount < FAN_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            }
            else if(u16_DefrostingtimeCount < DAMPER_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, fan_duty);
            }
            else
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, fan_duty);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);
            }
            break;
        default:
            break;
    }
}

static void DefrostingState_PreCooling(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            defrostMode = (DefrostMode_t)eDefrostMode_PreCooling;
            zoneDefrostPreCoolingState = (ZoneCoolingState_t)eZoneCooling_RefFrz;
            u16_DefrostingtimeCount = 0;
            st_Defrosting.u16_PreCoolingCompStillOnTimeStart = Get_MinuteCount();
            st_Defrosting.u16_PreCoolingCompStillOnTimeMinute = 0;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_DefrostingtimeCount++;
            PreCooling_ProcessZoneCoolingState();
            DefrostingProcess_RefVarZoneCoolingState();
            st_Defrosting.u16_PreCoolingCompStillOnTimeMinute =
                Get_MinuteElapsedTime(st_Defrosting.u16_PreCoolingCompStillOnTimeStart);
            PreCooling_SetOutputState(zoneDefrostPreCoolingState);
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            st_Defrosting.u16_PreCoolingCompStillOnTimeMinute = 0;
            break;
        default:
            break;
    }
}

static void DefrostingState_PreHold(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            defrostMode = (DefrostMode_t)eDefrostMode_PreHold;
            u16_DefrostingtimeCount = 0;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_DefrostingtimeCount++;
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, 0);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, RPM_0DUTY);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, U8_DEFROST_PRE_HOLD_FAN_DUTY);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLCLOSE);
            if(u16_DefrostingtimeCount >= U16_DEFROST_PRE_HOLD_MAX_TIME_SECONDS)
            {
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_Defrosting);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            break;
        default:
            break;
    }
}

static void DefrostingState_Defrosting(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    bool b_def_snr_error = Get_SensorError((SensorType_t)SENSOR_DEFROST);
    uint16_t u16_def_snr_temp = Get_SensorValue((SensorType_t)SENSOR_DEFROST);
    uint16_t u16_def_exit_temp = Get_DeforstExitTemp();
    bool b_energy_mode = Get_EnergyConsumptionModeState();

    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            defrostMode = (DefrostMode_t)eDefrostMode_Defrosting;
            u16_DefrostingtimeCount = 0;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_DefrostingtimeCount++;
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, 0);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, RPM_0DUTY);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, 0);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLCLOSE);
            if(u16_DefrostingtimeCount > U16_DEFROST_HEATER_MAX_ON_TIME_SECONDS)
            {
                st_Defrosting.b_DefrostFunctionError = true;
                st_Defrosting.u8_DefrostFunctionErrorCount++;
                if(st_Defrosting.u8_DefrostFunctionErrorCount >= U8_DEFROST_FUNCTION_ERROR_REPORT_COUNT)
                {
                    st_Defrosting.b_DefrostFunctionErrorReport = true;
                }
                else
                {
                    st_Defrosting.b_DefrostFunctionErrorReport = false;
                }
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzDefHeater, false);
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_AfterStop);
            }
            else if((true == b_def_snr_error) &&
                (u16_DefrostingtimeCount > U16_DEFROST_SENSOR_ERROR_DEFROST_HEATER_MAX_ON_TIME_SECNDS))
            {
                st_Defrosting.b_DefrostFunctionError = false;
                st_Defrosting.b_DefrostFunctionErrorReport = false;
                st_Defrosting.u8_DefrostFunctionErrorCount = 0;
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzDefHeater, false);
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_AfterStop);
            }
            else if((true == b_energy_mode) &&
                (u16_DefrostingtimeCount > U16_ENERGY_MODE_DEFROST_HEATER_MAX_ON_TIME_SECONDS))
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzDefHeater, false);
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_AfterStop);
            }
            else if((false == b_def_snr_error) &&
                (u16_def_snr_temp >= u16_def_exit_temp) &&
                (u16_DefrostingtimeCount > U16_DEFROST_HEATER_MIN_ON_TIME_SECONDS))
            {
                st_Defrosting.b_DefrostFunctionError = false;
                st_Defrosting.b_DefrostFunctionErrorReport = false;
                st_Defrosting.u8_DefrostFunctionErrorCount = 0;
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzDefHeater, false);
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_AfterStop);
            }
            else
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzDefHeater, true);
                st_Defrosting.u16_DefrostHeaterOnSecond = u16_DefrostingtimeCount;
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzDefHeater, false);
            st_Defrosting.u16_DefrostHeaterOnSecond = 0;
            break;
        default:
            break;
    }
}

static void DefrostingState_AfterStop(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            defrostMode = (DefrostMode_t)eDefrostMode_AfterStop;
            u16_DefrostingtimeCount = 0;
            Clear_DoorOpenTimeSecond(DOOR_ALL);
            Clear_FridgeTotalOnTimeMinute();
            Clear_CompStillOnTimeMinute();
            Clear_CompTotalOnTimeMinute();
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_DefrostingtimeCount++;
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzDefHeater, false);
            if(u16_DefrostingtimeCount > U16_DEFROST_AFTER_STOP_TIME_SECONDS)
            {
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_AfterComp);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            break;
        default:
            break;
    }
}

static void DefrostingState_AfterComp(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint16_t delay_time = U16_DEFROST_AFTER_COMP_TIME_SECONDS;

    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            defrostMode = (DefrostMode_t)eDefrostMode_AfterComp;
            u16_DefrostingtimeCount = 0;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_DefrostingtimeCount++;
            if((RoomTempRange_t)RT_UP40 <= room_range)
            {
                delay_time = U16_UP40_DEFROST_AFTER_COMP_TIME_SECONDS;
            }
            if(u16_DefrostingtimeCount >= delay_time)
            {
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_AfterWaitStart);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            break;
        default:
            break;
    }
}

static void DefrostingState_AfterWaitStart(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            defrostMode = (DefrostMode_t)eDefrostMode_AfterWaitStart;
            u16_DefrostingtimeCount = 0;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_DefrostingtimeCount++;
            zoneWaitCoolingState = CompOff_GetZoneCoolingState();
            if(((ZoneCoolingState_t)eZoneCooling_Frz == zoneWaitCoolingState) ||
                ((ZoneCoolingState_t)eZoneCooling_RefFrz == zoneWaitCoolingState))
            {
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_AfterFan);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            break;
        default:
            break;
    }
}

static void DefrostingState_FanStartup(uint8_t freq_index)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_def_snr_error = Get_SensorError((SensorType_t)SENSOR_DEFROST);
    uint16_t u16_def_snr_temp = Get_SensorValue((SensorType_t)SENSOR_DEFROST);
    bool b_frz_snr_error = Get_SensorError((SensorType_t)SENSOR_FRZ);
    uint16_t u16_frz_snr_temp = Get_SensorValue((SensorType_t)SENSOR_FRZ);
    bool b_condensation_mode = Get_CondensationModeState();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    uint8_t frz_fan_duty = Get_CompOnFanSettingIndex(freq_index);
    uint8_t cond_fan_duty = Get_CondFanSettingIndex(freq_index);
    bool b_cool_fan_on = true;

    if((true == b_condensation_mode) || ((RT_BELOW18 == room_range) && (b_energy_mode == false)))
    {
        b_cool_fan_on = false;
    }

    if(u16_DefrostingtimeCount < U16_DEFROST_AFTER_FAN_TIME_MIN_SECONDS)
    {
        frz_fan_duty = 0;
        cond_fan_duty = 0;
    }
    else if(u16_DefrostingtimeCount < U16_DEFROST_AFTER_FAN_TIME_5MIN_SECONDS)
    {
        if((false == b_def_snr_error) &&
            (false == b_frz_snr_error) &&
            (u16_def_snr_temp >= u16_frz_snr_temp) &&
            (false == st_Defrosting.b_FrzFanStartup))
        {
            frz_fan_duty = 0;
        }

        if(false == b_cool_fan_on)
        {
            cond_fan_duty = 0;
        }
    }
    else if(u16_DefrostingtimeCount < U16_DEFROST_AFTER_FAN_TIME_8MIN_SECONDS)
    {
        if((false == b_def_snr_error) &&
            (false == b_frz_snr_error) &&
            (u16_def_snr_temp >= u16_frz_snr_temp) &&
            (false == st_Defrosting.b_FrzFanStartup))
        {
            frz_fan_duty = 0;
        }

        if(true == b_condensation_mode)
        {
            cond_fan_duty = 0;
        }
    }
    else if(u16_DefrostingtimeCount < U16_DEFROST_AFTER_FAN_TIME_MAX_SECONDS)
    {
        if((false == b_def_snr_error) &&
            (false == b_frz_snr_error) &&
            (u16_def_snr_temp >= u16_frz_snr_temp) &&
            (false == st_Defrosting.b_FrzFanStartup))
        {
            frz_fan_duty = 0;
        }
    }
    else
    {
        st_Defrosting.b_FanStartupCompleted = true;
    }

    Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, frz_fan_duty);
    Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);

    if(0 != frz_fan_duty)
    {
        st_Defrosting.b_FrzFanStartup = true;
    }

    if(((0 != cond_fan_duty) || (RT_BELOW13 >= room_range)) && (0 != frz_fan_duty))
    {
        st_Defrosting.b_FanStartupCompleted = true;
    }
}

static void DefrostingState_AfterFan(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    uint8_t freq_index = 0;
    bool b_def_snr_error = Get_SensorError((SensorType_t)SENSOR_DEFROST);
    uint16_t u16_def_snr_temp = Get_SensorValue((SensorType_t)SENSOR_DEFROST);
    bool b_frz_snr_error = Get_SensorError((SensorType_t)SENSOR_FRZ);
    uint16_t u16_frz_snr_temp = Get_SensorValue((SensorType_t)SENSOR_FRZ);
    bool b_energy_mode = Get_EnergyConsumptionModeState();

    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            defrostMode = (DefrostMode_t)eDefrostMode_AfterFan;
            u16_DefrostingtimeCount = 0;
            st_Defrosting.b_FrzFanStartup = false;
            st_Defrosting.b_FanStartupCompleted = false;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_DefrostingtimeCount++;
            if(true == b_energy_mode)
            {
                freq_index = Get_CompFreqIndex(eCoolingCapacity_EnergyMode, 0);
            }
            else
            {
                freq_index = Get_CompFreqIndex(eCoolingCapacity_FastRunning, 0);
            }
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            DefrostingState_FanStartup(freq_index);
            if(true == st_Defrosting.b_FanStartupCompleted)
            {
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_AfterDamper);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            break;
        default:
            break;
    }
}

static void DefrostingState_AfterDamper(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    uint8_t freq_index = 0;
    uint8_t fan_duty = 0;
    bool b_energy_mode = Get_EnergyConsumptionModeState();

    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            defrostMode = (DefrostMode_t)eDefrostMode_AfterDamper;
            u16_DefrostingtimeCount = 0;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_DefrostingtimeCount++;
            if(true == b_energy_mode)
            {
                freq_index = Get_CompFreqIndex(eCoolingCapacity_EnergyMode, 0);
            }
            else
            {
                freq_index = Get_CompFreqIndex(eCoolingCapacity_FastRunning, 0);
            }
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            fan_duty = Get_CondFanSettingIndex(freq_index);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, fan_duty);
            fan_duty = Get_CompOnFanSettingIndex(freq_index);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, fan_duty);
            if(u16_DefrostingtimeCount >= U16_DEFROST_AFTER_DAMPER_TIME_SECONDS)
            {
                SimpleFsm_Transition(&st_DefrostingFsm, DefrostingState_Completed);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            break;
        default:
            break;
    }
}

static void DefrostingState_Completed(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            defrostMode = (DefrostMode_t)eDefrostMode_Completed;
            st_Defrosting.u16_DefrostingNumber++;
            u16_DefrostingtimeCount = 0;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_DefrostingtimeCount++;
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            break;
        default:
            break;
    }
}

static void Defrosting_Enter(EnterDefrostingState_t enterState)
{
    switch(enterState)
    {
        case(SimpleFsmSignal_t)eEnterState_First:
        case(SimpleFsmSignal_t)eEnterState_OverLoadError:
        case(SimpleFsmSignal_t)eEnterState_SensorError:
        case(SimpleFsmSignal_t)eEnterState_DefFunctionError:
        case(SimpleFsmSignal_t)eEnterState_TurboFreeze:
        case(SimpleFsmSignal_t)eEnterState_EnergyMode:
            SimpleFsm_Init(&st_DefrostingFsm, DefrostingState_PreHold, NULL);
            break;
        case(SimpleFsmSignal_t)eEnterState_Normal:
            SimpleFsm_Init(&st_DefrostingFsm, DefrostingState_PreWaitStart, NULL);
            break;
        default:
            break;
    }
}

void Defrosting_Init(EnterDefrostingState_t enterState)
{
    st_Defrosting.u16_DefrostingNumber = 0;
    Defrosting_Enter(enterState);
    Defrosting_PollTimer(U16_DEFROSTING_CYCLE_SECOND);
    u16_DefrostingtimeCount = 0;
}

void Defrosting_Exit(void)
{
    Stop_PollTimer();
}

void Clear_DefrostMode(void)
{
    if((DefrostMode_t)eDefrostMode_Completed == defrostMode)
    {
        defrostMode = (DefrostMode_t)eDefrostMode_None;
    }
}

ZoneCoolingState_t Get_PreCoolingState(void)
{
    return (zoneDefrostPreCoolingState);
}

uint16_t Get_PreCoolingtimeMinute(void)
{
    return (st_Defrosting.u16_PreCoolingCompStillOnTimeMinute);
}

uint16_t Get_DefrostingtimeSecond(void)
{
    return (u16_DefrostingtimeCount);
}

uint16_t Get_DefrostHeaterOnSecond(void)
{
    return (st_Defrosting.u16_DefrostHeaterOnSecond);
}

uint16_t Get_DefrostingNumber(void)
{
    return (st_Defrosting.u16_DefrostingNumber);
}

DefrostMode_t Get_DefrostMode(void)
{
    return (defrostMode);
}

bool Get_DefrostFunctionError(void)
{
    return (st_Defrosting.b_DefrostFunctionError);
}

bool Get_DefrostFunctionErrorReport(void)
{
    return (st_Defrosting.b_DefrostFunctionErrorReport);
}
