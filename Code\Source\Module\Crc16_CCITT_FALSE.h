/*!
 * @file
 * @brief This file defines public constants, types and functions for the crc module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef __CRC16_CCITT_FALSE_H__
#define __CRC16_CCITT_FALSE_H__
#include <stdint.h>

#define U16_CRC_INITIAL_VALUE (uint16_t)0xFFFF

uint16_t Cal_CRC_SingleData(uint16_t u16_crcvalue, uint8_t u8_data);
uint16_t Cal_CRC_MultipleData(uint8_t *p_databuff, uint8_t u8_datalength);

#endif /* __CRC16_CCITT_FALSE_H__ */
