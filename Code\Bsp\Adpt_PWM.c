/*!
 * @file
 * @brief PWM adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_PWM.h"

// TIME2: PWM  1250
#define TIME2_PERIOD_VALUE ((uint16_t)(SystemCoreClock / 1250 - 1)) // period
#define TIME2_COMPARE_VALUE (TIME2_PERIOD_VALUE + 1) // Duty  50%

static void AdtTimer2Init(void)
{
    stc_bt_mode23_cfg_t stcBtBaseCfg;
    stc_bt_m23_compare_cfg_t stcBtPortCmpCfg;

    /* 结构体初始化清零 */
    DDL_ZERO_STRUCT(stcBtBaseCfg);
    DDL_ZERO_STRUCT(stcBtPortCmpCfg);

    Sysctrl_SetPeripheralGate(SysctrlPeripheralBaseTim, TRUE); /* Base Timer外设时钟使能 */

    stcBtBaseCfg.enWorkMode = BtWorkMode2; /* 锯齿波模式 */
    stcBtBaseCfg.enCT = BtTimer; /* 定时器功能，计数时钟为内部PCLK */
    stcBtBaseCfg.enPRS = BtPCLKDiv1; /* PCLK */
    stcBtBaseCfg.enCntDir = BtCntUp; /* 向上计数，在三角波模式时只读 */
    stcBtBaseCfg.enPWMTypeSel = BtIndependentPWM; /* 独立输出PWM */
    stcBtBaseCfg.enPWM2sSel = BtSinglePointCmp; /* 单点比较功能 */
    stcBtBaseCfg.bOneShot = FALSE; /* 循环计数 */
    stcBtBaseCfg.bURSSel = FALSE; /* 上下溢更新 */
    Bt_Mode23_Init(TIM2, &stcBtBaseCfg); /* TIM0 的模式23功能初始化 */

    Bt_M23_ARRSet(TIM2, TIME2_PERIOD_VALUE, TRUE); /* 设置重载值,并使能缓存 */

    Bt_M23_CCR_Set(TIM2, BtCCR0A, TIME2_COMPARE_VALUE); /* 设置比较值A */

    stcBtPortCmpCfg.enCH0ACmpCtrl = BtPWMMode2; /* OCREFA输出控制OCMA:PWM模式2 */
    stcBtPortCmpCfg.enCH0APolarity = BtPortPositive; /* 正常输出 */
    stcBtPortCmpCfg.bCh0ACmpBufEn = TRUE; /* A通道缓存控制 */
    stcBtPortCmpCfg.enCh0ACmpIntSel = BtCmpIntNone; /* A通道比较控制:无 */

    Bt_M23_PortOutput_Cfg(TIM2, &stcBtPortCmpCfg); /* 比较输出端口配置 */

    /* 事件更新周期设置，OV和UND Mask都无效时，0表示锯齿波每个周期更新一次，每+1代表延迟1个周期 */
    Bt_M23_SetValidPeriod(TIM2, 0, FALSE, FALSE); /* 间隔周期设置 */

    Bt_M23_Cnt16Set(TIM2, 0); /* 设置计数初值 */
    Bt_M23_EnPWM_Output(TIM2, TRUE, FALSE); /* TIM0 端口输出使能 */

    Bt_M23_Run(TIM2); /* TIM0 运行 */
}

void AdtTimer3Init(void)
{
    uint16_t u16CntValue;
    stc_tim3_mode23_cfg_t stcTim3BaseCfg;
    stc_tim3_m23_compare_cfg_t stcTim3PortCmpCfg;

    // 结构体初始化清零
    DDL_ZERO_STRUCT(stcTim3BaseCfg);
    DDL_ZERO_STRUCT(stcTim3PortCmpCfg);

    Sysctrl_SetPeripheralGate(SysctrlPeripheralTim3, TRUE); // Timer3外设时钟使能

    stcTim3BaseCfg.enWorkMode = Tim3WorkMode2; // 锯齿波模式
    stcTim3BaseCfg.enCT = Tim3Timer; // 定时器功能，计数时钟为内部PCLK
    stcTim3BaseCfg.enPRS = Tim3PCLKDiv1; // PCLK
    stcTim3BaseCfg.enCntDir = Tim3CntUp; // 向上计数，在三角波模式时只读
    stcTim3BaseCfg.enPWMTypeSel = Tim3IndependentPWM; // 独立输出PWM
    stcTim3BaseCfg.enPWM2sSel = Tim3SinglePointCmp; // 单点比较功能
    stcTim3BaseCfg.bOneShot = FALSE; // 循环计数
    stcTim3BaseCfg.bURSSel = FALSE; // 上下溢更新

    Tim3_Mode23_Init(&stcTim3BaseCfg); // TIM3 的模式23功能初始化

    Tim3_M23_ARRSet(TIME3_PERIOD_VALUE, TRUE); // 设置重载值,并使能缓存

    Tim3_M23_CCR_Set(Tim3CCR1A, TIME3_COMPARE_VALUE); // 设置CH1比较值A
    Tim3_M23_CCR_Set(Tim3CCR2A, TIME3_COMPARE_VALUE); // 设置CH2比较值A

    stcTim3PortCmpCfg.enCHxACmpCtrl = Tim3PWMMode2; // OCREFB输出控制OCMA:PWM模式2(PWM互补模式下也要设置，避免强制输出)
    stcTim3PortCmpCfg.enCHxAPolarity = Tim3PortOpposite; // 反向输出
    stcTim3PortCmpCfg.bCHxACmpBufEn = TRUE; // A通道缓存控制使能
    stcTim3PortCmpCfg.enCHxACmpIntSel = Tim3CmpIntNone; // A通道比较中断控制:无

    Tim3_M23_PortOutput_Cfg(Tim3CH1, &stcTim3PortCmpCfg); // 比较输出端口配置
    stcTim3PortCmpCfg.enCHxAPolarity = Tim3PortPositive; // 正常输出
    Tim3_M23_PortOutput_Cfg(Tim3CH2, &stcTim3PortCmpCfg); // 比较输出端口配置

    // 事件更新周期设置，0表示锯齿波每个周期更新一次，每+1代表延迟1个周期
    Tim3_M23_SetValidPeriod(0, FALSE, FALSE); /* 间隔周期设置 */
    u16CntValue = 0;

    Tim3_M23_Cnt16Set(u16CntValue); // 设置计数初值

    Tim3_M23_EnPWM_Output(TRUE, FALSE); // 端口输出使能

    Tim3_M23_Run(); // 运行。
}

void Board_InitTim(void)
{
    Sysctrl_SetPeripheralGate(SysctrlPeripheralAdvTim, TRUE); // ADT外设时钟使能
    AdtTimer2Init();
    AdtTimer3Init();
}
