/*!
 * @file
 * @brief Initialize MCU.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_Usart.h"
#include "Core_Types.h"
#include "InverterUsart.h"
#include "Driver_CompFrequency.h"
#include "Crc16_CCITT_FALSE.h"
#include "SystemManager.h"

static UartInverterParm_st st_UartInverterParm;
static uint8_t u8_CompFbFreq;
static uint16_t u16_BusVoltage;
static uint16_t u16_CompPower;
static uint8_t u8_CompErrState;

static void Process_UartInverterSendData(void);
static void Process_UartInverterReceData(const uint8_t u8_rece_data);

void Init_UartInverter(void)
{
    st_UartInverterParm.f_SendIE = true;
    st_UartInverterParm.f_FrameSending = false;
    st_UartInverterParm.f_FrameReceiving = false;

    st_UartInverterParm.u8_SendTimeOutCount = 0;
    st_UartInverterParm.u8_ReceTimeOutCount = 0;
    st_UartInverterParm.u16_SendCycleCount = 0;
    st_UartInverterParm.u8_packetCnt = 0;
    st_UartInverterParm.b_BootVersion = false;
    st_UartInverterParm.b_AppVersion = false;
}

void Handle_UartInverterSendData(void)
{
    if(false == st_UartInverterParm.f_FrameSending)
    {
        st_UartInverterParm.u8_SendTimeOutCount = 0;
    }
    else
    {
        Process_UartInverterSendData();
    }
}

void Handle_UartInverterReceData(const uint8_t u8_rece_data)
{
    if(true == st_UartInverterParm.f_FrameReceiving)
    {
        Process_UartInverterReceData(u8_rece_data);
    }
}

static void Edit_UartInverterNormalFrame(void)
{
    uint8_t *data = &st_UartInverterParm.sendPacket.head;
    uint8_t index;
    uint8_t len;

    st_UartInverterParm.sendPacket.head = U8_INVERTER_FRAME_HEAD;
    st_UartInverterParm.sendPacket.srcAddr = FRAME_MAIN_ADDR;
    st_UartInverterParm.sendPacket.destAddr = FRAME_INVERTER_ADDR;
    st_UartInverterParm.sendPacket.fb1 = INVERTER_APP_FUNC;
    st_UartInverterParm.sendPacket.fb2 = INVERTER_APP_FREQ;
    st_UartInverterParm.sendPacket.fb3 = (uint8_t)Get_CompFreq();
    st_UartInverterParm.sendPacket.len = 0x00;
    st_UartInverterParm.sendPacket.end = U8_INVERTER_FRAME_END;
    st_UartInverterParm.u16_SendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(InverterFrame_st, data) + st_UartInverterParm.sendPacket.len;
    for(index = 0; index < len; index++)
    {
        st_UartInverterParm.u16_SendCrcValue =
            Cal_CRC_SingleData(st_UartInverterParm.u16_SendCrcValue, *data++);
    }
    st_UartInverterParm.sendPacket.crch = GET_U16_HIGHBYTE(st_UartInverterParm.u16_SendCrcValue);
    st_UartInverterParm.sendPacket.crcl = GET_U16_LOWBYTE(st_UartInverterParm.u16_SendCrcValue);
    return;
}

static void Edit_UartInverterOtaFrame(inverter_ota_subfunc_e func)
{
    uint8_t *data = &st_UartInverterParm.sendPacket.head;
    uint8_t index;
    uint8_t len;

    st_UartInverterParm.sendPacket.head = U8_INVERTER_FRAME_HEAD;
    st_UartInverterParm.sendPacket.srcAddr = FRAME_MAIN_ADDR;
    st_UartInverterParm.sendPacket.destAddr = FRAME_INVERTER_ADDR;
    st_UartInverterParm.sendPacket.fb1 = INVERTER_OTA_FUNC;
    st_UartInverterParm.sendPacket.fb2 = func;
    st_UartInverterParm.sendPacket.fb3 = 0x00;
    st_UartInverterParm.sendPacket.len = 0x00;
    st_UartInverterParm.sendPacket.end = U8_INVERTER_FRAME_END;
    st_UartInverterParm.u16_SendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(InverterFrame_st, data) + st_UartInverterParm.sendPacket.len;
    for(index = 0; index < len; index++)
    {
        st_UartInverterParm.u16_SendCrcValue =
            Cal_CRC_SingleData(st_UartInverterParm.u16_SendCrcValue, *data++);
    }
    st_UartInverterParm.sendPacket.crch = GET_U16_HIGHBYTE(st_UartInverterParm.u16_SendCrcValue);
    st_UartInverterParm.sendPacket.crcl = GET_U16_LOWBYTE(st_UartInverterParm.u16_SendCrcValue);
    return;
}

void Handle_UartInverterFrame(void)
{
    if((true == st_UartInverterParm.f_SendIE) &&
        ((U8_INVERTER_SEND_CYCLE_MILLISECOND <= st_UartInverterParm.u16_SendCycleCount)))
    {
        if(false == st_UartInverterParm.f_FrameSending)
        {
            if(st_UartInverterParm.u8_packetCnt % U8_INVERTER_VERSION_CYCLE_COUNT == 0 &&
                (st_UartInverterParm.b_BootVersion == false ||
                    st_UartInverterParm.b_AppVersion == false))
            {
                if(st_UartInverterParm.b_BootVersion == false)
                {
                    Edit_UartInverterOtaFrame(INVERTER_OTA_BOOT_VER);
                }
                else if(st_UartInverterParm.b_AppVersion == false)
                {
                    Edit_UartInverterOtaFrame(INVERTER_OTA_APP_VER);
                }
            }
            else
            {
                Edit_UartInverterNormalFrame();
            }
            st_UartInverterParm.f_FrameSending = true;
            st_UartInverterParm.f_FrameReceiving = false;
            st_UartInverterParm.f_SendIE = false;

            st_UartInverterParm.u8_SendCount = 0;
            st_UartInverterParm.u8_SendDataState = 0;
            st_UartInverterParm.u16_SendCycleCount = 0;
            st_UartInverterParm.u8_packetCnt++;
            InverterUart_DisalbeRxInterrupts();
            InverterUart_EnalbeTxInterrupts();
        }
    }

    if(false == st_UartInverterParm.f_FrameReceiving)
    {
        st_UartInverterParm.u8_ReceTimeOutCount = 0;
    }
}

void Handle_UartInverterOverTime(void)
{
    FridgeState_t fridge_state = Get_FridgeState();

    if(true == st_UartInverterParm.f_SendIE)
    {
        st_UartInverterParm.u16_SendCycleCount++;
    }
    else
    {
        st_UartInverterParm.u16_SendCycleCount = 0;
    }

    if(st_UartInverterParm.u8_SendTimeOutCount < 0xFF)
    {
        if(true == st_UartInverterParm.f_FrameSending)
        {
            st_UartInverterParm.u8_SendTimeOutCount++;
        }
        if(U8_INVERTER_SEND_TIMEOUT_MILLISECOND <= st_UartInverterParm.u8_SendTimeOutCount)
        {
            st_UartInverterParm.u8_SendTimeOutCount = 0;
            st_UartInverterParm.f_FrameSending = false;
            InverterUart_DisalbeTxInterrupts();
            st_UartInverterParm.f_SendIE = true;
        }
    }

    if(st_UartInverterParm.u8_ReceTimeOutCount < 0xFF)
    {
        if(true == st_UartInverterParm.f_FrameReceiving)
        {
            st_UartInverterParm.u8_ReceTimeOutCount++;
        }

        if(U8_INVERTER_RECV_TIMEOUT_MILLISECOND <= st_UartInverterParm.u8_ReceTimeOutCount)
        {
            st_UartInverterParm.u8_ReceTimeOutCount = 0;
            st_UartInverterParm.u8_RecvDataErrorCount++;
            if(st_UartInverterParm.u8_RecvDataErrorCount >= U16_INVERTER_RECE_DATA_ERROR_TIME_WITH_100MS)
            {
                st_UartInverterParm.b_ReceiveError = true;
            }
            else if(((FridgeState_t)eFridge_FunctionalCircuitTest) == fridge_state)
            {
                if(st_UartInverterParm.u8_RecvDataErrorCount >= U16_INVERTER_RECE_DATA_ERROR_FCT_100MS)
                {
                    st_UartInverterParm.b_ReceiveError = true;
                }
            }
            st_UartInverterParm.f_FrameReceiving = false;
            InverterUart_DisalbeRxInterrupts();
            st_UartInverterParm.f_SendIE = true;
        }
    }
}

static void Start_UartInverterRece(void)
{
    st_UartInverterParm.u8_ReceDataState = 0;
    st_UartInverterParm.u8_ReceCount = 0;
    st_UartInverterParm.u8_ReceLength = 0;
    st_UartInverterParm.u8_ReceTimeOutCount = 0;
    st_UartInverterParm.u16_ReceCrcValue = U16_CRC_INITIAL_VALUE;
}

static void Process_UartInverterSendData(void)
{
    uint8_t u8_send_data = 0;

    switch(st_UartInverterParm.u8_SendDataState)
    {
        case(uint8_t)INVERTER_FRAME_HEAD:
            st_UartInverterParm.u8_SendCount = 0;
            st_UartInverterParm.sendPos = &st_UartInverterParm.sendPacket.head;
            u8_send_data = *st_UartInverterParm.sendPos++;
            InverterUart_SendOneData(u8_send_data);
            st_UartInverterParm.u8_SendDataState = (uint8_t)INVERTER_FRAME_SRC;
            break;
        case(uint8_t)INVERTER_FRAME_SRC:
        case(uint8_t)INVERTER_FRAME_DEST:
        case(uint8_t)INVERTER_FRAME_FUNCBYTE1:
        case(uint8_t)INVERTER_FRAME_FUNCBYTE2:
        case(uint8_t)INVERTER_FRAME_FUNCBYTE3:
            u8_send_data = *st_UartInverterParm.sendPos++;
            InverterUart_SendOneData(u8_send_data);
            st_UartInverterParm.u8_SendDataState++;
            break;
        case(uint8_t)INVERTER_FRAME_LENGTH:
            st_UartInverterParm.u8_SendLength = *st_UartInverterParm.sendPos;
            u8_send_data = *st_UartInverterParm.sendPos++;
            InverterUart_SendOneData(u8_send_data);
            st_UartInverterParm.u8_SendDataState = (uint8_t)INVERTER_FRAME_DATA;
            break;
        case(uint8_t)INVERTER_FRAME_DATA:
            st_UartInverterParm.u8_SendCount++;
            if(st_UartInverterParm.u8_SendCount == st_UartInverterParm.u8_SendLength + 1)
            {
                st_UartInverterParm.sendPos = &st_UartInverterParm.sendPacket.crch;
            }
            u8_send_data = *st_UartInverterParm.sendPos++;
            InverterUart_SendOneData(u8_send_data);
            if(st_UartInverterParm.u8_SendCount >= (st_UartInverterParm.u8_SendLength + 3))
            {
                st_UartInverterParm.u8_SendDataState = (uint8_t)INVERTER_FRAME_OVER;
                st_UartInverterParm.f_FrameSending = false;
                st_UartInverterParm.f_FrameReceiving = true;
                Start_UartInverterRece();
                st_UartInverterParm.u8_SendTimeOutCount = 0;
                InverterUart_DisalbeTxInterrupts();
                InverterUart_EnalbeRxInterrupts();
            }
            break;
        default:
            st_UartInverterParm.f_FrameSending = false;
            st_UartInverterParm.f_FrameReceiving = true;
            Start_UartInverterRece();
            st_UartInverterParm.u8_SendTimeOutCount = 0;
            InverterUart_DisalbeTxInterrupts();
            InverterUart_EnalbeRxInterrupts();
            break;
    }
}

static void Handle_UartInverterRecvFrame(void)
{
    InverterFrame_st *rpacket = &st_UartInverterParm.recvPacket;
    InverterFrame_st *spacket = &st_UartInverterParm.sendPacket;

    if(spacket->fb1 == rpacket->fb1 && spacket->fb2 == rpacket->fb2)
    {
        if(rpacket->fb1 == INVERTER_APP_FUNC)
        {
            if(rpacket->fb2 == INVERTER_APP_FREQ && rpacket->len == 6)
            {
                u16_BusVoltage = rpacket->data[0] << 8 | rpacket->data[1];
                u16_CompPower = rpacket->data[2] << 8 | rpacket->data[3];
                u8_CompFbFreq = rpacket->data[4];
                u8_CompErrState = rpacket->data[5];
            }
        }
        else if(rpacket->fb1 == INVERTER_OTA_FUNC)
        {
            if(rpacket->fb2 == INVERTER_OTA_APP_VER && rpacket->len == 6)
            {
                st_UartInverterParm.u16_HwVersion = rpacket->data[0] << 8 | rpacket->data[1];
                st_UartInverterParm.u16_AppVersion = rpacket->data[2] << 8 | rpacket->data[3];
                st_UartInverterParm.u16_AppCrc = rpacket->data[4] << 8 | rpacket->data[5];
                st_UartInverterParm.b_AppVersion = true;
            }
            else if(rpacket->fb2 == INVERTER_OTA_BOOT_VER && rpacket->len == 4)
            {
                st_UartInverterParm.u16_BootVersion = rpacket->data[0] << 8 | rpacket->data[1];
                st_UartInverterParm.u16_BootCrc = rpacket->data[2] << 8 | rpacket->data[3];
                st_UartInverterParm.b_BootVersion = true;
            }
        }
    }
    return;
}

static void Process_UartInverterReceData(const uint8_t u8_rece_data)
{
    uint16_t crc16_value = 0;

    switch(st_UartInverterParm.u8_ReceDataState)
    {
        case(uint8_t)INVERTER_FRAME_HEAD:
            if(U8_INVERTER_FRAME_HEAD == u8_rece_data)
            {
                st_UartInverterParm.recvPos = &st_UartInverterParm.recvPacket.head;
                *st_UartInverterParm.recvPos++ = u8_rece_data;
                st_UartInverterParm.u16_ReceCrcValue =
                    Cal_CRC_SingleData(st_UartInverterParm.u16_ReceCrcValue, u8_rece_data);
                st_UartInverterParm.u8_ReceCount = 0;
                st_UartInverterParm.u8_ReceDataState = (uint8_t)INVERTER_FRAME_SRC;
            }
            break;
        case(uint8_t)INVERTER_FRAME_SRC:
        case(uint8_t)INVERTER_FRAME_DEST:
        case(uint8_t)INVERTER_FRAME_FUNCBYTE1:
        case(uint8_t)INVERTER_FRAME_FUNCBYTE2:
        case(uint8_t)INVERTER_FRAME_FUNCBYTE3:
            *st_UartInverterParm.recvPos++ = u8_rece_data;
            st_UartInverterParm.u16_ReceCrcValue =
                Cal_CRC_SingleData(st_UartInverterParm.u16_ReceCrcValue, u8_rece_data);
            st_UartInverterParm.u8_ReceDataState++;
            break;
        case(uint8_t)INVERTER_FRAME_LENGTH:
            *st_UartInverterParm.recvPos++ = u8_rece_data;
            st_UartInverterParm.u16_ReceCrcValue =
                Cal_CRC_SingleData(st_UartInverterParm.u16_ReceCrcValue, u8_rece_data);
            st_UartInverterParm.u8_ReceLength = u8_rece_data;
            st_UartInverterParm.u8_ReceDataState = (uint8_t)INVERTER_FRAME_DATA;
            break;
        case(uint8_t)INVERTER_FRAME_DATA:
            if(st_UartInverterParm.u8_ReceLength > U8_INVERTER_FRAME_LEN_MAX)
            {
                return;
            }
            if(st_UartInverterParm.u8_ReceCount < st_UartInverterParm.u8_ReceLength)
            {
                st_UartInverterParm.u16_ReceCrcValue =
                    Cal_CRC_SingleData(st_UartInverterParm.u16_ReceCrcValue, u8_rece_data);
            }
            if(st_UartInverterParm.u8_ReceCount == st_UartInverterParm.u8_ReceLength)
            {
                st_UartInverterParm.recvPos = &st_UartInverterParm.recvPacket.crch;
            }
            st_UartInverterParm.u8_ReceCount++;
            *st_UartInverterParm.recvPos++ = u8_rece_data;
            if(st_UartInverterParm.u8_ReceCount == (st_UartInverterParm.u8_ReceLength + 3))
            {
                crc16_value = st_UartInverterParm.recvPacket.crch << 8 | st_UartInverterParm.recvPacket.crcl;

                if(crc16_value == st_UartInverterParm.u16_ReceCrcValue &&
                    st_UartInverterParm.recvPacket.end == U8_INVERTER_FRAME_END)
                {
                    st_UartInverterParm.u8_ReceDataState = (uint8_t)INVERTER_FRAME_OVER;
                    st_UartInverterParm.b_ReceiveError = false;
                    st_UartInverterParm.u8_RecvDataErrorCount = 0;
                    st_UartInverterParm.f_FrameReceiving = false;
                    st_UartInverterParm.u8_ReceTimeOutCount = 0;
                    st_UartInverterParm.u8_SendTimeOutCount = 0;
                    Handle_UartInverterRecvFrame();
                    InverterUart_DisalbeRxInterrupts();
                    st_UartInverterParm.f_SendIE = true;
                }
            }
            break;
        default:
            st_UartInverterParm.u8_ReceTimeOutCount = 0;
            break;
    }
}

bool Get_InverterCommErr(void)
{
    bool err_state = false;

    if(true == st_UartInverterParm.b_ReceiveError)
    {
        err_state = true;
    }
    return (err_state);
}

uint8_t Get_CompFeedbackFreq(void)
{
    return (u8_CompFbFreq);
}

uint8_t Get_CompErrorState(void)
{
    return (u8_CompErrState);
}

uint16_t Get_CompBusVoltage(void)
{
    return (u16_BusVoltage);
}

uint16_t Get_CompPower(void)
{
    return (u16_CompPower);
}

uint32_t Get_CompBootVersion(void)
{
    if(st_UartInverterParm.b_BootVersion)
    {
        return st_UartInverterParm.u16_BootVersion;
    }

    return 0;
}

uint32_t Get_CompBootCrc(void)
{
    if(st_UartInverterParm.b_BootVersion)
    {
        return st_UartInverterParm.u16_BootCrc;
    }

    return 0;
}

uint32_t Get_CompAppVersion(void)
{
    if(st_UartInverterParm.b_AppVersion)
    {
        return st_UartInverterParm.u16_AppVersion;
    }

    return 0;
}

uint32_t Get_CompAppCrc(void)
{
    if(st_UartInverterParm.b_AppVersion)
    {
        return st_UartInverterParm.u16_AppCrc;
    }

    return 0;
}
