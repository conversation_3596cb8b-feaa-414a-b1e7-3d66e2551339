/*!
 * @file
 * @brief This file defines public constants, types and functions for display usart.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef DISPLAY_USART_
#define DISPLAY_USART_

#include <stdint.h>
#include <stdbool.h>

#define U8_SEND_FRAME_LENGTH_UART_DISPLAY ((uint8_t)18)
#define U8_SEND_DATA_LENGTH_UART_DISPLAY ((uint8_t)16)
#define U8_RECE_FRAME_LENGTH_UART_DISPLAY ((uint8_t)16)
#define U8_RECE_DATA_LENGTH_UART_DISPLAY ((uint8_t)14)
#define U8_DISPLAY_SEND_WAIT_MILLISECOND ((uint8_t)7)
#define U8_DISPLAY_SEND_TIMEOUT_MILLISECOND ((uint8_t)100)
#define U8_DISPLAY_RECV_TIMEOUT_MILLISECOND ((uint8_t)100)
#define U8_DISPLAY_COMM_ERROR_SECOND ((uint8_t)60)
#define U8_DISPLAY_FRAME_SEND_HEAD (uint8_t)0xC0
#define U8_DISPLAY_FRAME_RECV_HEAD (uint8_t)0xC1
#define U16_RECE_DATA_ERROR_TIME_WITH_100MS ((uint16_t)600)
#define U16_FLASH_MILLISECOND ((uint16_t)1000)

// 帧格式
typedef enum
{
    DISPLAY_FRAME_HEAD = 0, // 报文头
    DISPLAY_FRAME_LENGTH, // 报文长度
    DISPLAY_FRAME_DATA, // 正常报文数据
    DISPLAY_FRAME_OVER, // 完成
    DISPLAY_FRAME_MAX,
} DisplayProtocolState_em;

typedef struct
{
    uint16_t u8_RecvDataErrorCount;
    uint8_t ary_SendBuff[U8_SEND_FRAME_LENGTH_UART_DISPLAY]; // 发送缓冲区
    uint8_t ary_ReceBuff[U8_RECE_FRAME_LENGTH_UART_DISPLAY]; // 接收缓冲区

    uint8_t u8_SendWaitCount;
    uint8_t u8_SendDataState; // 发送数据状态
    uint8_t u8_ReceDataState; // 接收数据状态
    uint8_t u8_SendLength; // 发送长度
    uint8_t u8_SendCount; // 发送计数
    uint8_t u8_SendCheckSum;
    uint8_t u8_ReceLength; // 接收长度
    uint8_t u8_ReceCount; // 接收计数
    uint8_t u8_ReceCheckSum;
    uint8_t u8_SendTimeOutCount; // 发送超时计数
    uint8_t u8_ReceTimeOutCount; // 接收超时计数
    uint16_t u16_FlashCount;
    bool f_SendIE; // 发送允许
    bool f_HaveFrameToSend;
    bool f_FrameSending; // 报文发送
    bool f_FrameReceiving;
    bool f_FrameHandle; // 报文处理
    bool b_ReceiveError;
} UartDisplayParm_st;

enum
{
    eNull_Tone,
    eShort_Tone,
    eLong_Tone,
    eDoorAlarm_Tone,
    eDoorOpen_Tone,
    eInvalid_Tone,
    eMax_Tone
};

enum
{
    eLockLed_BitIndex = 0,
    eFuzzyLed_BitIndex,
    eTurboCoolLed_BitIndex,
    eTurboFreezeLed_BitIndex,
    eWiFiLed_BitIndex,
    eTreasureLed_BitIndex,
    eBabyLed_BitIndex,
    eZeroLed_BitIndex
};

enum
{
    eWifiLED_State_Off = 0,
    eWifiLED_State_On,
    eWifiLED_State_Flash,
    eWifiLED_State_Max
};

typedef uint8_t WifiState_t;
typedef uint8_t MusicType_t;

void Init_UartDisplay(void);
void Handle_UartDisplaySendData(void);
void Handle_UartDisplayReceData(const uint8_t u8_recedata);
void Handle_UartDisplayFrame(void);
void Handle_UartDisplayOverTime(void);
void Set_MusicType(MusicType_t type);
MusicType_t Get_MusicType(void);
void Set_WiFiLedState(WifiState_t state);

#endif

