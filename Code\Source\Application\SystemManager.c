/*!
 * @file
 * @brief Manages all the state variables of the system.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "SystemManager.h"
#include "Core_CallBackTimer.h"
#include "FridgeRunner.h"
#include "Driver_DoubleDamper.h"
#include "TestUsart.h"
#include "FunctionalCircuitTest.h"
#include "Driver_Flash.h"
#include "FactoryMode.h"
#include "DisplayInterface.h"
#include "TestMode.h"
#include "ShowroomMode.h"

enum
{
    Signal_Entry = SimpleFsmSignal_Entry,
    Signal_Exit = SimpleFsmSignal_Exit,
    Signal_PollTimerExpired = SimpleFsmSignal_UserStart,
    Signal_FridgeStateUpdated,
    Signal_RequestMade,
};

static st_CoreCallbackTimer st_FridgeStartupTimer;
static SimpleFsm_t st_FridgeFsm;
static FridgeState_t fridgeState;
static uint8_t u8_FactoryEntryNumber;

static void FridgeState_Startup(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void FridgeState_Running(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void FridgeState_FunctionalCircuitTest(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void FridgeState_Factory(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void FridgeState_Showroom(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void FridgeState_Test(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);

static void PollTimerExpired(void)
{
    SimpleFsm_SendSignal(&st_FridgeFsm, Signal_PollTimerExpired, NULL);
}

static void ArmPollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_FridgeStartupTimer,
        PollTimerExpired,
        tickSec,
        0,
        eCallbackTimer_Type_OneShot,
        eCallbackTimer_Priority_Normal);
}

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_FridgeStartupTimer);
}

static void Fridge_Startup(void)
{
#if(BuildMode == ReleaseMode)
    Init_UartTest();
#endif
}

static void FridgeState_Startup(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    UserMode_t user_mode = Get_UserMode();

    switch(signal)
    {
        case Signal_Entry:
            fridgeState = (uint8_t)eFridge_Startup;
            ArmPollTimer(U16_FRIDGESTARTUP_DELAY_SECOND);
            break;
        case Signal_PollTimerExpired:
            Fridge_Startup();
            if((0 == u8_FactoryEntryNumber) && ((uint8_t)eFuzzy_Mode == user_mode))
            {
                SimpleFsm_Transition(fsm, FridgeState_Factory);
            }
            else
            {
                SimpleFsm_Transition(fsm, FridgeState_Running);
            }
            break;
        case Signal_Exit:
            Stop_PollTimer();
            break;
        default:
            break;
    }
}

static void FridgeState_Running(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            fridgeState = (FridgeState_t)eFridge_Running;
            FridgeRunner_Init();
            break;
        case Signal_Exit:
            FridgeRunner_Exit();
            break;
        default:
            break;
    }
}

static void FridgeState_FunctionalCircuitTest(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            fridgeState = (FridgeState_t)eFridge_FunctionalCircuitTest;
            FunctionalCircuitTest_Init();
            break;
        case Signal_Exit:
            FunctionalCircuitTest_Exit();
            break;
        default:
            break;
    }
}

static void FridgeState_Factory(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            fridgeState = (FridgeState_t)eFridge_Factory;
            FactoryMode_Init();
            break;
        case Signal_Exit:
            FactoryMode_Exit();
            break;
        default:
            break;
    }
}

static void FridgeState_Showroom(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            fridgeState = (FridgeState_t)eFridge_Showroom;
            ShowroomMode_Init();
            break;
        case Signal_Exit:
            ShowroomMode_Exit();
            break;
        default:
            break;
    }
}

static void FridgeState_Test(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            fridgeState = (FridgeState_t)eFridge_Test;
            TestMode_Init();
            break;
        case Signal_Exit:
            TestMode_Exit();
            break;
        default:
            break;
    }
}

void SystemManager_Init(void)
{
    SimpleFsm_Init(&st_FridgeFsm, FridgeState_Startup, NULL);
}

void FridgeState_Update(FridgeState_t requestState)
{
    switch(requestState)
    {
        case(FridgeState_t)eFridge_Startup:
            SimpleFsm_Transition(&st_FridgeFsm, FridgeState_Startup);
            break;
        case(FridgeState_t)eFridge_Running:
            SimpleFsm_Transition(&st_FridgeFsm, FridgeState_Running);
            break;
        case(FridgeState_t)eFridge_FunctionalCircuitTest:
            SimpleFsm_Transition(&st_FridgeFsm, FridgeState_FunctionalCircuitTest);
            break;
        case(FridgeState_t)eFridge_Factory:
            SimpleFsm_Transition(&st_FridgeFsm, FridgeState_Factory);
            break;
        case(FridgeState_t)eFridge_Showroom:
            SimpleFsm_Transition(&st_FridgeFsm, FridgeState_Showroom);
            break;
        case(FridgeState_t)eFridge_Test:
            SimpleFsm_Transition(&st_FridgeFsm, FridgeState_Test);
            break;
        default:
            break;
    }
}

void Set_FactoryEntryNumber(uint8_t entryNumber)
{
    u8_FactoryEntryNumber = entryNumber;
}

uint8_t Get_FactoryEntryNumber(void)
{
    return (u8_FactoryEntryNumber);
}

FridgeState_t Get_FridgeState(void)
{
    return (fridgeState);
}
