/*!
 * @file
 * @brief This file defines public constants, types and functions for the pwm adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _Adpt_PWM_H_
#define _Adpt_PWM_H_

#include <stdint.h>
#include "adt.h"
#include "pca.h"
#include "bt.h"
#include "gpio.h"
#include "timer3.h"

#define TIME3_PERIOD_VALUE (uint16_t)(SystemCoreClock / 25000 - 1) // period
#define TIME3_COMPARE_VALUE (TIME3_PERIOD_VALUE + 1) // off

#define IO_REF_TOP_LAMP(CompareValue) Bt_M23_CCR_Set(TIM2, BtCCR0A, CompareValue)
#define U16_GRADUAL_LAMP_REAL_CYCLE_VALUE ((uint16_t)(48000000 / 1250))

// #define SET_FAN_DUTY_ID0(CompareValue) Tim3_M23_CCR_Set(Tim3CCR2A, (TIME3_PERIOD_VALUE - CompareValue))
#define SET_FAN_DUTY_ID0(CompareValue) Tim3_M23_CCR_Set(Tim3CCR2A, CompareValue)
#define SET_FAN_DUTY_ID1(CompareValue) Tim3_M23_CCR_Set(Tim3CCR1A, CompareValue)

void Board_InitTim(void);
#endif
