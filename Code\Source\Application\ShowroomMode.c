/*!
 * @file
 * @brief Manages all the state variables of the showroom mode.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "ShowroomMode.h"
#include "ResolverDevice.h"
#include "Driver_DoubleDamper.h"
#include "Driver_CompFrequency.h"
#include "SystemManager.h"
#include "Driver_Fan.h"

void ShowroomMode_Init(void)
{
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLCLOSE);
}

void ShowroomMode_Exit(void)
{
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, DS_DontCare);
}
