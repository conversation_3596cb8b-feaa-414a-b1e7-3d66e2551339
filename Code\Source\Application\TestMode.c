/*!
 * @file
 * @brief Manages all the state variables of the test mode.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "TestMode.h"
#include "Core_CallBackTimer.h"
#include "ResolverDevice.h"
#include "Driver_GradualLamp.h"
#include "IO_Device.h"
#include "Driver_DoubleDamper.h"
#include "Driver_CompFrequency.h"
#include "SystemManager.h"
#include "Driver_Fan.h"
#include "DisplayInterface.h"
#include "DisplayUsart.h"
#include "Driver_AdSample.h"
#include "LedController.h"
#include "FridgeRunner.h"
#include "SpecialKeyManager.h"
#include "SystemTimerModule.h"

#define U16_TEST_MODE_CYCLE_SECOND (uint16_t)1

static st_CoreCallbackTimer st_TestModeTimer;
static uint16_t u16_TestControlCount;
static bool b_DamperInit;
static bool b_DefrostInit;

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_TestModeTimer);
}

static void TestMode_ControlCooling(void)
{
    if(false == b_DamperInit)
    {
        b_DamperInit = true;
        Reset_DoubleDamper();
        u16_TestControlCount = Get_MinuteCount();
    }

    if(Get_MinuteElapsedTime(u16_TestControlCount) < (72 * 60))
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, FREQ_147HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLOPEN);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLOPEN);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, RPM_55DUTY);
    }
    else
    {
        TestMode_Exit();
        Exit_Tmode();
        FridgeState_Update(eFridge_Startup);
    }
}

static void TestMode_ControlDefrosting(void)
{
    DefrostMode_t defrost_mode = Get_DefrostMode();

    if(false == b_DefrostInit)
    {
        b_DefrostInit = true;
        Reset_DoubleDamper();
        Defrosting_Init(eEnterState_First);
    }

    if((DefrostMode_t)eDefrostMode_AfterComp < defrost_mode)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        Exit_Tmode();
        FridgeState_Update(eFridge_Startup);
    }
}

static void Process_TestMode(void)
{
    uint8_t u8_TestMode = Get_TestMode();

    switch(u8_TestMode)
    {
        case TMODE_FORCE_COOLING:
            TestMode_ControlCooling();
            break;
        case TMODE_FORCE_DEFROST:
            TestMode_ControlDefrosting();
            break;
        case TMODE_FORCE_ENERGY:
            break;
        case TMODE_TT:
            break;
        default:
            break;
    }
}

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_TestModeTimer,
        Process_TestMode,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

void TestMode_Init(void)
{
    b_DamperInit = false;
    b_DefrostInit = false;
    Start_PollTimer(U16_TEST_MODE_CYCLE_SECOND);
}

void TestMode_Exit(void)
{
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, DS_DontCare);
    Stop_PollTimer();
}
