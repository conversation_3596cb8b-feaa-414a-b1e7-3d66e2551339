/*!
 * @file
 * @brief This file defines public constants, types and functions for resolver.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef RESOLVER_DEVICE_H
#define RESOLVER_DEVICE_H

#include <stdint.h>
#include <stdbool.h>

#define U8_INVALID_FSM_ID ((uint8_t)0xFF)
#define U8_INVALID_INDEX ((uint8_t)0xFF)

typedef enum
{
    FSM_SpecialModes = 0,
    FSM_OpenDoorControl,
    FSM_NormalControl,
    FSM_DefaultControl,
    FSM_TotalFSMs
} DeviceFSM_ID;

typedef enum
{
    DS_NoChange = -2,
    DS_DontCare,
    DS_Off,
    DS_On
} DeviceStatus;

typedef enum
{
    DEVICE_Comp = 0,
    DEVICE_FrzFan,
    DEVICE_CoolFan,
    DEVICE_RefDamper,
    DEVICE_RefVarDamper,
    DEVICE_VerticalBeamHeater,
    DEVICE_FrzDef<PERSON>eater,
    DEVICE_LastDevice
} Device_ID;

void Init_ResolverDeviceData(void);
void Vote_DeviceStatus(uint8_t u8_FSM_ID, uint8_t u8_Device_ID, int8_t s8_DeviceState);
void Process_ResolverDeviceData(void);
uint8_t Get_ResolvedDeviceStatus(uint8_t u8_DeviceID);
uint8_t Get_DeviceControllingFSM(uint8_t u8_DeviceID);

#endif
