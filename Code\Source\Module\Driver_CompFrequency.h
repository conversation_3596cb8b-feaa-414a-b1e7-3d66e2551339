/*!
 * @file
 * @brief This file defines public constants, types and functions for the compressor drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef DRIVER_COMPFREQUENCY_H
#define DRIVER_COMPFREQUENCY_H

#include <stdint.h>
#include <stdbool.h>

#define U16_COMP_FREQINDEX_MIN (uint16_t)40
#define U16_COMP_FREQINDEX_MAX (uint16_t)150

#define U8_COMP_FREQUENCY_UPDATEINTERVAL_SECS (uint8_t)2
#define U8_TIME2MINUTE (uint8_t)(2 * 60)
#define U8_TIME4MINUTE (uint8_t)(4 * 60)

#define FREQ_0HZ (uint8_t)0
#define FREQ_41HZ (uint8_t)1 // 1230
#define FREQ_46HZ (uint8_t)2 // 1380
#define FREQ_55HZ (uint8_t)3 // 1650
#define FREQ_57HZ (uint8_t)4 // 1710
#define FREQ_70HZ (uint8_t)5 // 2100
#define FREQ_80HZ (uint8_t)6 // 2400
#define FREQ_108HZ (uint8_t)7 // 3240
#define FREQ_118HZ (uint8_t)8 // 3540
#define FREQ_136HZ (uint8_t)9 // 4080
#define FREQ_147HZ (uint8_t)10 // 4410
#define FREQ_MAX (uint8_t)11

typedef struct
{
    uint16_t u16_CompFreq;
    uint16_t u16_CompFreqAdjust;
    uint8_t u8_OnTimeSecs;
    uint8_t u8_CompFreqIndex;

    bool b_Init;
    bool b_ForceOff;
    bool b_WakeupFlag;
} DriveCompFreq_st;

void Driver_CompFreqInit(void);
void Driver_CompFreqForce(bool b_State);
void Driver_CompFreqSet(uint8_t u8_CompFreqIndex);
void Driver_CompAdjustFreqSet(uint16_t u16_CompFreq);
void Set_CompFreqMicroAdjustParm(uint16_t comp_freq_adjust_value);
uint16_t Get_CompFreqMicroAdjustParm(void);
uint16_t Get_CompFreq(void);

#endif
