/*!
 * @file
 * @brief This module is device IO.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "IO_Device.h"
#include "Driver_GradualLamp.h"

void Set_DefrostHeaterState(bool state)
{
    if(true == state)
    {
        IO_FRZ_DEFROST_HEATER_ENABLE;
    }
    else
    {
        IO_FRZ_DEFROST_HEATER_DISABLE;
    }
}

void Set_VerticalBeamHeaterState(bool state)
{
    bool b_ref_lamp = Get_GradualLampState(REF_SURFACE_LAMP);

    if((true == state) && (false == b_ref_lamp))
    {
        IO_CBX_LED_ENABLE;
    }
    else
    {
        IO_CBX_LED_DISABLE;
    }
}

void Ctrl_FrzLeftLamp(bool b_ref_left_door_state, bool b_frz_Left_door_state)
{
    bool b_state = true;

    if((false == b_frz_Left_door_state) ||
        (true == b_ref_left_door_state))
    {
        b_state = false;
    }

    if(true == b_state)
    {
        IO_V_TOP_LED_ENABLE;
    }
    else
    {
        IO_V_TOP_LED_DISABLE;
    }
}

void Ctrl_FrzRightLamp(bool b_ref_right_door_state, bool b_frz_right_door_state)
{
    bool b_state = true;

    if((false == b_frz_right_door_state) ||
        (true == b_ref_right_door_state))
    {
        b_state = false;
    }

    if(true == b_state)
    {
        IO_FRZ_LED_ENABLE;
    }
    else
    {
        IO_FRZ_LED_DISABLE;
    }
}
