/*!
 * @file
 * @brief This file defines public constants, types and functions for the special key manager.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef SPECIAL_KEY_MANAGER_H
#define SPECIAL_KEY_MANAGER_H

#include <stdint.h>
#include <stdbool.h>
#include "LedController.h"

typedef enum
{
    TMODE_NONE,
    TMODE_FORCE_COOLING, // 强制制冷
    TMODE_FORCE_DEFROST, // 强制化霜
    TMODE_FORCE_ENERGY, // 强制能效
    TMODE_TT
} Tmode_em;

typedef struct
{
    void (*p_Deal_KeyActionCompleted)(void);
    uint16_t u16_UniteKeyValue; // 组合按键值
    uint16_t u16_SingleKeyValue; // 按住不动的按键值
    uint8_t u8_NeedCounter;
    bool b_IsValid;
} UniteKey_st;

#define CON_TMODE_KEY_CANCEL_TIME 3 // unit:s
#define TIME_QUERYDATEEXIT 240 // s

typedef enum
{
    CHECK_SHOWROOM_MODE = 1,
    CHECK_REF_SNR,
    CHECK_REF_VAR_SNR,
    CHECK_FRZ_SNR,
    CHECK_FRZ_DEF_SNR,
    CHECK_ROOM_SNR,
    CHECK_ROOM_HUMI,
    CHECK_COMP,
    CHECK_REF_DAMPER,
    CHECK_REF_VAR_DAMPER,
    CHECK_DEF_HEATER,
    CHECK_FRZ_FAN_DUTY,
    CHECK_COND_FAN_DUTY,
    CHECK_REF_LEFT_DOOR_SW,
    CHECK_REF_RIGHT_DOOR_SW,
    CHECK_FRZ_LEFT_DOOR_SW,
    CHECK_FRZ_RIGHT_DOOR_SW,
    CHECK_REF_ON,
    CHECK_REF_OFF,
    CHECK_FRZ_ON,
    CHECK_FRZ_OFF,
    CHECK_VAR_ON,
    CHECK_VAR_OFF,
    CHECK_MAX
} Query_em;

void Ctrl_SpecialKey(uint16_t u16_keyValue);
void Adjust_SpecialMode(void);
void Exit_Tmode(void);
uint8_t Get_TestMode(void);
uint8_t Get_QueryMode(void);
bool Get_SpecialMode(void);
void Inc_AdjustPara(void);
void Dec_AdjustPara(void);
void Confirm_AdjustPara(void);

#endif
