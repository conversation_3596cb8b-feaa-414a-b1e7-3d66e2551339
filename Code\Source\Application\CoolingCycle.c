/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "CoolingCycle.h"
#include "FridgeRunner.h"
#include "Core_CallBackTimer.h"
#include "Parameter_TemperatureZone.h"
#include "Driver_AdSample.h"
#include "ResolverDevice.h"
#include "Driver_Fan.h"
#include "Driver_DoubleDamper.h"
#include "SystemTimerModule.h"
#include "DisplayInterface.h"

#define COOLING_CYCLE_POLL_SECONDS (uint16_t)1
#define NUM_COUNTS_PER_MINUTE (uint16_t)60
#define REF_COOLING_ON_SECOND (uint16_t)3600 // 60min 60x60=3600

enum
{
    Signal_Entry = SimpleFsmSignal_Entry,
    Signal_Exit = SimpleFsmSignal_Exit,
    Signal_PollTimerExpired = SimpleFsmSignal_UserStart,
    Signal_CompStartup
};

static st_CoreCallbackTimer st_CoolingCycleTimer;
static SimpleFsm_t st_CoolingFsm;
static Cooling_st st_Cooling;
static CoolingCompState_t coolingCompState;
static ZoneCoolingState_t zoneCoolingState;
static CoolingCapacityState_t coolingCapacityState;
static uint16_t u16_CoolingOffTimerCounter;
static uint16_t u16_CoolingProtectTimerCounter;
static uint16_t u16_CoolingStartupTimerCounter;
static uint16_t u16_CoolingOnTimerCounter;
static uint16_t u16_RefCoolingOnTimerCounter;
static uint16_t u16_CoolFanCondensationTimerCounter;
static uint8_t u8_CompOnFreqIndex;

static void CoolingState_CompOff(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void CoolingState_CompProtect(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void CoolingState_CompStartup(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void CoolingState_CompOn(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);

static void Process_RefVarZoneCoolingState(void)
{
    bool cooling_state = CoolingCycle_GetRefVarCoolingState();

    if(true == cooling_state)
    {
        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLOPEN);
    }
    else
    {
        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLCLOSE);
    }
}

static void Expired_PollTimer(void)
{
    Process_RefVarZoneCoolingState();
    SimpleFsm_SendSignal(&st_CoolingFsm, Signal_PollTimerExpired, NULL);
}

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_CoolingCycleTimer,
        Expired_PollTimer,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_CoolingCycleTimer);
}

static bool CompOff_GetCompProtectState(void)
{
    bool b_protect_state = true;

    if(0 == st_Cooling.u16_CoolingCycleNumber)
    {
        b_protect_state = false;
    }

    return (b_protect_state);
}

static void CompOff_SetOutputState(ZoneCoolingState_t state)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t fan_duty = Get_CompOffFanSettingIndex(room_range);
    bool b_protect_state = CompOff_GetCompProtectState();
    bool varcooling_state = CoolingCycle_GetRefVarCoolingState();

    switch(state)
    {
        case(ZoneCoolingState_t)eZoneCooling_Idle:
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, FREQ_0HZ);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, RPM_0DUTY);

            if(true == varcooling_state)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, fan_duty);
            }
            else
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, 0);
            }

            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);
            break;
        case(ZoneCoolingState_t)eZoneCooling_Ref:
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, FREQ_0HZ);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, RPM_0DUTY);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, fan_duty);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLOPEN);
            break;
        case(ZoneCoolingState_t)eZoneCooling_Frz:
        case(ZoneCoolingState_t)eZoneCooling_RefFrz:
            if(false == b_protect_state)
            {
                SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompStartup);
            }
            else
            {
                if(COOLING_PROTECT_TIME_SECONDS <= u16_CoolingOffTimerCounter)
                {
                    SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompStartup);
                }
                else
                {
                    u16_CoolingProtectTimerCounter = u16_CoolingOffTimerCounter;
                    SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompProtect);
                }
            }
            break;
        default:
            break;
    }
}

static void FirstCycle_UpdateCapacityState(void)
{
    bool b_energy_mode = Get_EnergyConsumptionModeState();

    if((CoolingEntryMode_t)eMode_FactoryCompleted == st_Cooling.coolingEntryMode)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_FactoryCompleted;
    }
    else if((CoolingEntryMode_t)eMode_FridgePowerOn == st_Cooling.coolingEntryMode)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_FridgePowerOn;
    }
    else
    {
        if(true == b_energy_mode)
        {
            coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_EnergyMode;
        }
        else
        {
            coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_FastRunning;
        }
    }
}

static void InProcess_UpdateCapacityState(void)
{
    bool ref_high_load_flag = Get_RefTempHighLoadState();
    bool frz_high_load_flag = Get_FrzTempHighLoadState();
    bool frz_less_zero = Get_FrzTempLessZero();
    bool b_energy_mode = Get_EnergyConsumptionModeState();

    if((true == ref_high_load_flag) || (true == frz_high_load_flag))
    {
        st_Cooling.b_HighLoad = true;
    }
    else if((true == frz_less_zero) &&
        (u16_RefCoolingOnTimerCounter >= REF_COOLING_ON_SECOND))
    {
        st_Cooling.b_HighLoad = true;
    }

    if(true == st_Cooling.b_TurboFreeze)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_FastRunning;
    }
    else if(true == st_Cooling.b_TurboCool)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_FastRunning;
    }
    else if(true == b_energy_mode)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_EnergyMode;
    }
    else if(true == st_Cooling.b_HighLoad)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_HighLoad;
    }
    else
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_Normal;
    }
}

static void CoolingCycle_UpdateCapacityState(void)
{
    if(0 == st_Cooling.u16_CoolingCycleNumber)
    {
        FirstCycle_UpdateCapacityState();
    }
    else
    {
        InProcess_UpdateCapacityState();
    }
}

static void CompStartup_SetFanOutputState(uint8_t freq_index)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_condensation_mode = Get_CondensationModeState();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    uint8_t frz_fan_duty = Get_CompOnFanSettingIndex(freq_index);
    uint8_t cond_fan_duty = Get_CondFanSettingIndex(freq_index);
    bool b_cool_fan_on = true;

    if((true == b_condensation_mode) || ((RT_BELOW18 == room_range) && (b_energy_mode == false)))
    {
        b_cool_fan_on = false;
    }

    if(u16_CoolingStartupTimerCounter < FAN_DELAY_STARTUP_SECOND)
    {
        frz_fan_duty = 0;
        cond_fan_duty = 0;
    }
    else if(u16_CoolingStartupTimerCounter < DAMPER_DELAY_STARTUP_SECOND)
    {
        if(false == b_cool_fan_on)
        {
            cond_fan_duty = 0;
        }
    }
    else if(u16_CoolingStartupTimerCounter < COOL_FAN_DELAY_ON_SECOND)
    {
        if(false == b_cool_fan_on)
        {
            cond_fan_duty = 0;
        }
    }
    else if(u16_CoolingStartupTimerCounter < CONDENSATION_COOL_FAN_DELAY_SECOND)
    {
        if(true == b_condensation_mode)
        {
            cond_fan_duty = 0;
        }
    }
    else
    {
    }

    Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, frz_fan_duty);
    Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);

    if(((0 != cond_fan_duty) || (RT_BELOW13 >= room_range)) && (0 != frz_fan_duty))
    {
        st_Cooling.b_CompStartupCompleted = true;
    }
}

static void CompStartup_SetOutputState(ZoneCoolingState_t state)
{
    uint8_t freq_index = Get_CompFreqIndex(coolingCapacityState, st_Cooling.u16_CompStillOnTimeMinute);

    switch(state)
    {
        case(ZoneCoolingState_t)eZoneCooling_Idle:
            SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompOff);
            break;
        case(ZoneCoolingState_t)eZoneCooling_Frz:
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            CompStartup_SetFanOutputState(freq_index);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);
            break;
        case(ZoneCoolingState_t)eZoneCooling_Ref:
        case(ZoneCoolingState_t)eZoneCooling_RefFrz:
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            CompStartup_SetFanOutputState(freq_index);
            if(u16_CoolingStartupTimerCounter >= DAMPER_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLOPEN);
            }
            break;
        default:
            break;
    }
}

static void Ctrl_CoolFanStatus(uint8_t freq_index)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    bool b_condensation_mode = Get_CondensationModeState();
    uint8_t cond_fan_duty = 0;

    if(false == b_condensation_mode)
    {
        cond_fan_duty = Get_CondFanSettingIndex(freq_index);
        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);
    }
    else
    {
        u16_CoolFanCondensationTimerCounter++;
        if(u16_CoolFanCondensationTimerCounter <= CONDENSATION_COOL_FAN_ON_SECOND)
        {
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, CONDENSATION_COOL_FAN_DUTY);
        }
        else if(u16_CoolFanCondensationTimerCounter <= (CONDENSATION_COOL_FAN_ON_SECOND + CONDENSATION_COOL_FAN_OFF_SECOND))
        {
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, RPM_0DUTY);
        }
        else
        {
            u16_CoolFanCondensationTimerCounter = 0;
        }
    }
}

static void CompOn_SetOutputState(ZoneCoolingState_t state)
{
    uint8_t freq_index = Get_CompFreqIndex(coolingCapacityState, st_Cooling.u16_CompStillOnTimeMinute);
    uint8_t fan_duty = Get_CompOnFanSettingIndex(freq_index);

    u8_CompOnFreqIndex = freq_index;
    switch(state)
    {
        case(ZoneCoolingState_t)eZoneCooling_Idle:
            SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompOff);
            u16_RefCoolingOnTimerCounter = 0;
            break;
        case(ZoneCoolingState_t)eZoneCooling_Frz:
            u16_RefCoolingOnTimerCounter = 0;
            Ctrl_CoolFanStatus(freq_index);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, fan_duty);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);

            break;
        case(ZoneCoolingState_t)eZoneCooling_Ref:
        case(ZoneCoolingState_t)eZoneCooling_RefFrz:
            u16_RefCoolingOnTimerCounter++;
            Ctrl_CoolFanStatus(freq_index);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, fan_duty);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLOPEN);
            break;
        default:
            break;
    }
}

static void CoolingState_CompOff(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            u16_CoolingOffTimerCounter = 0;
            if((CoolingCompState_t)eCooling_CompOn == coolingCompState)
            {
                st_Cooling.b_PullDown = false;
                st_Cooling.b_HighLoad = false;
                st_Cooling.u16_CoolingCycleNumber++;
            }
            coolingCompState = (CoolingCompState_t)eCooling_CompOff;
            break;
        case Signal_PollTimerExpired:
            u16_CoolingOffTimerCounter++;
            zoneCoolingState = CompOff_GetZoneCoolingState();
            CompOff_SetOutputState(zoneCoolingState);
            break;
        case Signal_Exit:
            u16_CoolingOffTimerCounter = 0;
            break;
        default:
            break;
    }
}

static void CoolingState_CompProtect(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            coolingCompState = (CoolingCompState_t)eCooling_CompProtect;
            break;
        case Signal_PollTimerExpired:
            u16_CoolingProtectTimerCounter++;
            if(COOLING_PROTECT_TIME_SECONDS <= u16_CoolingProtectTimerCounter)
            {
                SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompStartup);
            }
            break;
        case Signal_Exit:
            u16_CoolingProtectTimerCounter = 0;
            break;
        default:
            break;
    }
}

static void CoolingState_CompStartup(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            coolingCompState = (CoolingCompState_t)eCooling_CompStartup;
            u16_CoolingStartupTimerCounter = 0;
            st_Cooling.b_CompStartupCompleted = false;
            break;
        case Signal_PollTimerExpired:
            u16_CoolingStartupTimerCounter++;
            u16_RefCoolingOnTimerCounter = 0;
            zoneCoolingState = CompOn_GetZoneCoolingState();
            CoolingCycle_UpdateCapacityState();
            CompStartup_SetOutputState(zoneCoolingState);
            if(true == st_Cooling.b_CompStartupCompleted)
            {
                SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompOn);
            }
            break;
        case Signal_Exit:
            u16_CoolingStartupTimerCounter = 0;
            break;
        default:
            break;
    }
}

static void CoolingState_CompOn(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            coolingCompState = (CoolingCompState_t)eCooling_CompOn;
            u16_CoolingOnTimerCounter = 0;
            u16_RefCoolingOnTimerCounter = 0;
            u16_CoolFanCondensationTimerCounter = 0;
            st_Cooling.u16_CompStillOnTimeStart = Get_MinuteCount();
            st_Cooling.u16_CompStillOnTimeMinute = 0;
            st_Cooling.u16_CompTotalOnTimeSuspend = st_Cooling.u16_CompTotalOnTimeMinute;
            break;
        case Signal_PollTimerExpired:
            u16_CoolingOnTimerCounter++;
            zoneCoolingState = CompOn_GetZoneCoolingState();
            CoolingCycle_UpdateCapacityState();
            st_Cooling.u16_CompStillOnTimeMinute = Get_MinuteElapsedTime(st_Cooling.u16_CompStillOnTimeStart);
            st_Cooling.u16_CompTotalOnTimeMinute =
                st_Cooling.u16_CompTotalOnTimeSuspend + st_Cooling.u16_CompStillOnTimeMinute;
            CompOn_SetOutputState(zoneCoolingState);
            break;
        case Signal_Exit:
            u16_CoolingOnTimerCounter = 0;
            u16_RefCoolingOnTimerCounter = 0;
            u16_CoolFanCondensationTimerCounter = 0;
            st_Cooling.u16_CompStillOnTimeMinute = 0;
            st_Cooling.u16_CompTotalOnTimeSuspend = st_Cooling.u16_CompTotalOnTimeMinute;
            break;
        default:
            break;
    }
}

void CoolingCycle_Init(CoolingEntryMode_t mode)
{
    zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
    coolingCompState = (CoolingCompState_t)eCooling_CompOff;
    st_Cooling.u16_CoolingCycleNumber = 0;
    Clear_RefCoolingCycleNumber();
    st_Cooling.coolingEntryMode = mode;
    if((CoolingEntryMode_t)eMode_FridgePowerOn == mode)
    {
        Update_TempPullDownState();
        SimpleFsm_Init(&st_CoolingFsm, CoolingState_CompOff, NULL);
    }
    else
    {
        SimpleFsm_Init(&st_CoolingFsm, CoolingState_CompOn, NULL);
    }
    Start_PollTimer(COOLING_CYCLE_POLL_SECONDS);
}

void CoolingCycle_Exit(void)
{
    // zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
    // coolingCompState = (CoolingCompState_t)eCooling_CompOff;
    Stop_PollTimer();
}

CoolingCompState_t Get_CoolingCompState(void)
{
    return (coolingCompState);
}

CoolingCapacityState_t Get_CoolingCapacityState(void)
{
    return (coolingCapacityState);
}

void Clear_CompStillOnTimeMinute(void)
{
    u16_CoolingOnTimerCounter = 0;
    st_Cooling.u16_CompStillOnTimeStart = Get_MinuteCount();
    st_Cooling.u16_CompStillOnTimeMinute = 0;
}

void Clear_CompTotalOnTimeMinute(void)
{
    u16_CoolingOnTimerCounter = 0;
    st_Cooling.u16_CompTotalOnTimeSuspend = 0;
    st_Cooling.u16_CompTotalOnTimeMinute = 0;
}

uint16_t Get_CompStillOnTimeMinute(void)
{
    return (st_Cooling.u16_CompStillOnTimeMinute);
}

uint16_t Get_CompTotalOnTimeMinute(void)
{
    return (st_Cooling.u16_CompTotalOnTimeMinute);
}

uint16_t Get_CompOffTimeSecond(void)
{
    return (u16_CoolingOffTimerCounter);
}

void Set_TurboCoolState(bool state)
{
    st_Cooling.b_TurboCool = state;
}

void Set_TurboFreezeState(bool state)
{
    st_Cooling.b_TurboFreeze = state;
}

bool Get_TurboCoolState(void)
{
    return (st_Cooling.b_TurboCool);
}

bool Get_TurboFreezeState(void)
{
    return (st_Cooling.b_TurboFreeze);
}

CoolingEntryMode_t Get_CoolingEntryMode(void)
{
    return (st_Cooling.coolingEntryMode);
}

uint16_t Get_CoolingCycleNumber(void)
{
    return (st_Cooling.u16_CoolingCycleNumber);
}

uint8_t Get_CompOnFreqIndex(void)
{
    return (u8_CompOnFreqIndex);
}
