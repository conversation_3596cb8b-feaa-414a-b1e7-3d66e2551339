#ifndef _SYSLOG_H_
#define _SYSLOG_H_

#include <stdint.h>

#define LOG_DEBUG 0
#define LOG_INFO 1
#define LOG_WARN 1
#define LOG_ERR 1
#define LOG_ALERT 1

#define LOG_CONSOLE_OUTPUT 0

#define none
#if LOG_DEBUG
#define debug(format, ...) printf(format, ##__VA_ARGS__)
#else
#define debug(format, ...) none
#endif

#if LOG_INFO
#define info(format, ...) printf(format, ##__VA_ARGS__)
#else
#define info(format, ...) none
#endif

#if LOG_WARN
#define warn(format, ...) printf(format, ##__VA_ARGS__)
#else
#define warn(format, ...) none
#endif

#if LOG_ERR
#define err(format, ...) printf(format, ##__VA_ARGS__)
#else
#define err(format, ...) none
#endif

#if LOG_ALERT
#define alert(format, ...) printf(format, ##__VA_ARGS__)
#else
#define alert(format, ...) none
#endif

void Panic(void);
void Debug_Init(void);
void software_reset(void);
void HardFault_Handler(void);
#endif
