/*******************************************************************************************************************/
/**
 * Brief description, until the first dot (use JAVADOC_AUTOBRIEF).
 * \author $Author$
 * \remarks $Id$
 * \copyright
 * \file $Id$
 * @copyright Diehl Controls
 * @file IOT.c
 *
 **********************************************************************************************************************/

#include "Iot.h"
#include "miio_api.h"
#include "Iot_SpecHandler.h"
#include "Driver_AdSample.h"
#include "Parameter_TemperatureZone.h"
#include "DisplayInterface.h"
#include "Driver_DoorSwitch.h"
#include "LedController.h"
#include "DisplayUsart.h"
#include "Defrosting.h"
#include "Driver_DoubleDamper.h"
#include "Driver_Fan.h"
#include "Driver_Flash.h"
#include "Driver_CompFrequency.h"
#include "Core_Types.h"
#include "FaultCode.h"
#include "ParameterManager.h"
#include "SystemManager.h"
#include "syslog.h"

static void IOT_FridgeParam(IotPropName_e IotIndex, TLong lvalue);

static void IOT_FaultReportedJudge(void);
static void IOT_FaultReportedJudge24h(void);
static void IOT_ModeReportedJudge(void);
static IotExecuteRet_e IOT_ModeSetJudge(TLong lvalue);
static void IOT_DoorAlarmReportedJudge(void);
static void IOT_RefTempReportedJudge(void);
static void IOT_RefSetReportedJudge(void);
static IotExecuteRet_e IOT_RefSetSetJudge(TLong lvalue);
static void IOT_FrzTempReportedJudge(void);
static void IOT_FrzSetReportedJudge(void);
static IotExecuteRet_e IOT_FrzSetSetJudge(TLong lvalue);
static void IOT_SuperFrzSetReportedJudge(void);
static void IOT_RefVarTempReportedJudge(void);
static void IOT_RefVarSetReportedJudge(void);
static IotExecuteRet_e IOT_RefVarSetSetJudge(TLong lvalue);
static void IOT_DefrostStatusReportedJudge(void);
static void IOT_DefrostTempReportedJudge(void);
static void IOT_RefDamperReportedJudge(void);
static void IOT_FrzFanLevelReportedJudge(void);
static void IOT_CompSpeedReportedJudge(void);
static void IOT_CoolFanLevelReportedJudge(void);
static void IOT_VarDamperReportedJudge(void);
static void IOT_RefVarDamperReportedJudge(void);
static void IOT_RoomTempReportedJudge(void);
static void IOT_HumidityReportedJudge(void);
static void IOT_DoorStateReportedJudge(void);
static IotExecuteRet_e IOT_WriteFactorySn(TLong lvalue);
static void IOT_ReadFactorySn(void);
static void IOT_ReadFactoryData(void);

typedef void (*PFNFRIDGEPARAMREPORTHANDLER)(void);
typedef IotExecuteRet_e (*PFNFRIDGEPARAMSETHANDLER)(TLong lvalue);

typedef struct SFridgeParamIottoDev
{
    IotPropName_e ePropName; // 閿熸枻鎷峰墠閿熸枻鎷烽敓鏂ゆ嫹
    PFNFRIDGEPARAMREPORTHANDLER pfnFridgeParamDevtoIot; // 閿熷€熷閿熻緝鎲嬫嫹
    PFNFRIDGEPARAMSETHANDLER pfnFridgeParamIottoDev; // IoT閿熼摪鍑ゆ嫹
} TSControlFridgeParam;

static const TSControlFridgeParam gdsFridgeParamCtlFunc[] = {
    { IOT_PROP_FAULT, IOT_FaultReportedJudge, NULL },
    { IOT_PROP_MODE, IOT_ModeReportedJudge, IOT_ModeSetJudge },
    { IOT_PROP_DOOR_ALARM, IOT_DoorAlarmReportedJudge, NULL },
    { IOT_PROP_REF_SET, IOT_RefSetReportedJudge, IOT_RefSetSetJudge },
    { IOT_PROP_FRZ_SET, IOT_FrzSetReportedJudge, IOT_FrzSetSetJudge },
    { IOT_PROP_SUPER_FRZ_SET, IOT_SuperFrzSetReportedJudge, NULL },
    { IOT_PROP_REFVAR_SET, IOT_RefVarSetReportedJudge, IOT_RefVarSetSetJudge },
    { IOT_DL_PROP_DEFROST_STATUS, IOT_DefrostStatusReportedJudge, NULL },
    { IOT_DL_PROP_REF_DAMPER, IOT_RefDamperReportedJudge, NULL },
    { IOT_DL_PROP_FRZ_FAN, IOT_FrzFanLevelReportedJudge, NULL },
    { IOT_DL_PROP_COMP_SPEED, IOT_CompSpeedReportedJudge, NULL },
    { IOT_DL_PROP_COOL_FAN, IOT_CoolFanLevelReportedJudge, NULL },
    { IOT_DL_PROP_VAR_DAMPER, IOT_VarDamperReportedJudge, NULL },
    { IOT_DL_PROP_REFVAR_DAMPER, IOT_RefVarDamperReportedJudge, NULL },
    { IOT_PROP_DOOR_STATE, IOT_DoorStateReportedJudge, NULL },
    { IOT_PROP_FACTORY_SN, IOT_ReadFactorySn, IOT_WriteFactorySn }
};

static const TSControlFridgeParam gdsFridgeParamCtlFunc_20min[] = {
    { IOT_PROP_REF_TEMP, IOT_RefTempReportedJudge, NULL },
    { IOT_PROP_FRZ_TEMP, IOT_FrzTempReportedJudge, NULL },
    { IOT_PROP_REFVAR_TEMP, IOT_RefVarTempReportedJudge, NULL },
    { IOT_DL_PROP_DEFROST_TEMP, IOT_DefrostTempReportedJudge, NULL },
};

static const TSControlFridgeParam gdsFridgeParamCtlFunc_60min[] = {
    { IOT_DL_PROP_ROOMTTEMP, IOT_RoomTempReportedJudge, NULL },
    { IOT_DL_PROP_HUMIDITY, IOT_HumidityReportedJudge, NULL },
};

static const TSControlFridgeParam gdsFridgeParamCtlFunc_24h[] = {
    { IOT_PROP_FAULT, IOT_FaultReportedJudge24h, NULL },
};

static void IOT_FridgeParam(IotPropName_e IotIndex, TLong lvalue)
{
    IotExecuteRet_e eRet = IOT_EXECUTE_OK;
    IotGeneralPropVal_t sGeneral;
    sGeneral.propType = IOT_TYPE_LONG;
    sGeneral.lValue = lvalue;
    eRet = set_fridge_param_by_dev(IotIndex, &sGeneral);
    if(eRet != IOT_EXECUTE_OK)
    {
        // mNOP();
    }
}

// 閿熸枻鎷烽敓鏂ゆ嫹
static void IOT_FaultReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint32_t error = 0;
    uint32_t error0 = Get_FaultCodeByte(eFCode_IotByte0);
    uint32_t error1 = Get_FaultCodeByte(eFCode_IotByte1);
    uint32_t error2 = Get_FaultCodeByte(eFCode_IotByte2);
    uint32_t error3 = Get_FaultCodeByte(eFCode_IotByte3);

    error = error0 + (error1 << 8) + (error2 << 16) + (error3 << 24);
    IOT_FridgeParam(IOT_PROP_FAULT, (TLong)error);
}

// 閿熸枻鎷烽敓鏂ゆ嫹
static void IOT_FaultReportedJudge24h(void) // 閿熻緝鎲嬫嫹
{
    uint32_t error = 0;
    IOT_FridgeParam(IOT_PROP_FAULT, (TLong)error);
}

// 妯″紡
static void IOT_ModeReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t ModeSet = Get_UserMode();
    IOT_FridgeParam(IOT_PROP_MODE, (TLong)ModeSet);
}

static IotExecuteRet_e IOT_ModeSetJudge(TLong lvalue) // 閿熸枻鎷烽敓鏂ゆ嫹
{
    Set_UserMode((UserMode_t)lvalue);
    Wakeup_UserInterface();
    Set_ModeDisplay(false, (UserMode_t)lvalue);
    Set_MusicType((MusicType_t)eShort_Tone);
    return IOT_EXECUTE_OK;
}

// 閿熻剼鎲嬫嫹閿熸枻鎷风姸鎬�
static void IOT_DoorAlarmReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t DoorStateAlarm = 0;
    uint8_t DoorStateRL = Get_DoorSwitchflagState(DOOR_REF_LEFT);
    uint8_t DoorStateRR = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
    uint8_t DoorStateFL = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
    uint8_t DoorStateFR = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);

    if(DoorStateRL & 0x08)
    {
        DoorStateAlarm |= 0x02;
    }
    if(DoorStateRR & 0x08)
    {
        DoorStateAlarm |= 0x04;
    }
    if(DoorStateFL & 0x08)
    {
        DoorStateAlarm |= 0x08;
    }
    if(DoorStateFR & 0x08)
    {
        DoorStateAlarm |= 0x10;
    }

    IOT_FridgeParam(IOT_PROP_DOOR_ALARM, (TLong)DoorStateAlarm);
}

// 閿熸枻鎷烽敓鏂ゆ嫹闇查敓锟�
static void IOT_RefTempReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    int16_t u16_RefTemp = Get_SensorValue((SensorType_t)SENSOR_REF);

    if((u16_RefTemp % 10) >= 5)
    {
        u16_RefTemp = (u16_RefTemp - 500) / 10 + 1;
    }
    else
    {
        u16_RefTemp = (u16_RefTemp - 500) / 10;
    }
    IOT_FridgeParam(IOT_PROP_REF_TEMP, (TLong)u16_RefTemp);
}

// 閿熸枻鎷烽敓鏂ゆ嫹鐡掞拷
static void IOT_RefSetReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t u8_RefSet = Get_RefSetTemp() - 1;
    IOT_FridgeParam(IOT_PROP_REF_SET, (TLong)u8_RefSet);
}

static IotExecuteRet_e IOT_RefSetSetJudge(TLong lvalue) // 閿熸枻鎷烽敓鏂ゆ嫹
{
    uint8_t u8_RefSet = (uint8)lvalue + 1;

    Wakeup_UserInterface();
    Update_ManualRefSetTemp(u8_RefSet);
    if((Get_UserMode() == eFuzzy_Mode) || (Get_UserMode() == eTurboCool_Mode))
    {
        Set_UserMode(eManual_Mode);
        Set_ModeDisplay(false, eManual_Mode);
    }
    Update_RefSetTemp(u8_RefSet);
    Set_NumberDisplay((DisplayZone_t)eDispRef_Zone, false, u8_RefSet);
    Set_MusicType((MusicType_t)eShort_Tone);
    return IOT_EXECUTE_OK;
}

// 閿熸垝鍐婚敓閾拌鎷�
static void IOT_FrzTempReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    int16_t u16_FrzTemp = Get_SensorValue((SensorType_t)SENSOR_FRZ);

    if((u16_FrzTemp % 10) >= 5)
    {
        u16_FrzTemp = (u16_FrzTemp - 500) / 10 + 1;
    }
    else
    {
        u16_FrzTemp = (u16_FrzTemp - 500) / 10;
    }

    IOT_FridgeParam(IOT_PROP_FRZ_TEMP, (TLong)u16_FrzTemp);
}

// 閿熸垝鍐婚敓鍊熷畾
static void IOT_FrzSetReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    TLong u8_FrzSet = Get_FrzSetTemp();
    u8_FrzSet = u8_FrzSet - (TLong)30;
    IOT_FridgeParam(IOT_PROP_FRZ_SET, (TLong)u8_FrzSet);
}

static IotExecuteRet_e IOT_FrzSetSetJudge(TLong lvalue) // 閿熸枻鎷烽敓鏂ゆ嫹
{
    int8_t u8_FrzSet = (int8_t)lvalue + (int8_t)30;

    Wakeup_UserInterface();
    Update_ManualFrzSetTemp(u8_FrzSet);
    if((Get_UserMode() == eFuzzy_Mode) || (Get_UserMode() == eTurboFreeze_Mode))
    {
        Set_UserMode(eManual_Mode);
        Set_ModeDisplay(false, eManual_Mode);
    }
    Update_FrzSetTemp(u8_FrzSet);
    Set_NumberDisplay((DisplayZone_t)eDispFrz_Zone, false, u8_FrzSet);
    Set_MusicType((MusicType_t)eShort_Tone);
    return IOT_EXECUTE_OK;
}

// 閿熷姭璁规嫹閿熸枻鎷蜂綅
static void IOT_SuperFrzSetReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    TLong u8_FrzSet = (TLong)-30;

    IOT_FridgeParam(IOT_PROP_SUPER_FRZ_SET, (TLong)u8_FrzSet);
}

// 閿熸枻鎷烽敓閾拌鎷烽敓閾拌鎷�
static void IOT_RefVarTempReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    int16_t u16_VarTemp = Get_SensorValue((SensorType_t)SENSOR_VAR);

    if((u16_VarTemp % 10) >= 5)
    {
        u16_VarTemp = (u16_VarTemp - 500) / 10 + 1;
    }
    else
    {
        u16_VarTemp = (u16_VarTemp - 500) / 10;
    }
    IOT_FridgeParam(IOT_PROP_REFVAR_TEMP, (TLong)u16_VarTemp);
}
// 閿熸枻鎷烽敓閾拌鎷烽敓鍊熷畾
static void IOT_RefVarSetReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t u8_VarSet = Get_RefVarSetTemp();

    IOT_FridgeParam(IOT_PROP_REFVAR_SET, (TLong)u8_VarSet);
}

static IotExecuteRet_e IOT_RefVarSetSetJudge(TLong lvalue) // 閿熸枻鎷烽敓鏂ゆ嫹
{
    uint8_t u8_VarSet = (uint8)lvalue;
    Update_RefVarSetTemp(u8_VarSet);
    Wakeup_UserInterface();
    Set_RefVarDisplay(false, u8_VarSet);
    Set_MusicType((MusicType_t)eShort_Tone);
    // SetOver();

    return IOT_EXECUTE_OK;
}

static void IOT_ReadFactorySn(void)
{
    uint8_t sn[PRODUCT_SN_SIZE + 1] = { 0 };
    IotGeneralPropVal_t sGeneral;

    if(ReadProductSn(sn, PRODUCT_SN_SIZE) == 0)
    {
        sGeneral.propType = IOT_TYPE_STRING;
        strcpy(sGeneral.sValue, "\"");
        strcat(sGeneral.sValue, (const char *)sn);
        strcat(sGeneral.sValue, "\"");
        set_fridge_param_by_dev(IOT_PROP_FACTORY_SN, &sGeneral);
    }
}

static void IOT_ReadFactoryData(void)
{
    uint8_t sn[PRODUCT_SN_SIZE + 1] = { 0 };
    IotGeneralPropVal_t sGeneral;

    if(ReadProductSn(sn, PRODUCT_SN_SIZE) == 0)
    {
        sGeneral.propType = IOT_TYPE_STRING;
        strcpy(sGeneral.sValue, "\"");
        strcat(sGeneral.sValue, (const char *)sn);
        strcat(sGeneral.sValue, "\"");
        set_fridge_param_by_dev(IOT_PROP_FACTORY_SN, &sGeneral);
    }
}

static IotExecuteRet_e IOT_WriteFactorySn(TLong lvalue)
{
    uint8_t *psn = (uint8_t *)lvalue;

    if(Get_FridgeState() != eFridge_Factory)
    {
        return IOT_EXECUTE_CANNOT_WRITE;
    }

    if(WriteProductSn(&psn[1], PRODUCT_SN_SIZE, true) < 0)
    {
        return IOT_EXECUTE_FAIL;
    }

    ParameterSnUpdate();
    return IOT_EXECUTE_OK;
}

// 閿熸枻鎷烽湝鐘舵€�
static void IOT_DefrostStatusReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t defrost_mode = Get_DefrostMode();
    if((DefrostMode_t)eDefrostMode_None == defrost_mode)
    {
        defrost_mode = 0;
    }
    else
    {
        defrost_mode = 1;
    }
    IOT_FridgeParam(IOT_DL_PROP_DEFROST_STATUS, (TLong)defrost_mode);
}

// 閿熸枻鎷烽湝閿熼摪璁规嫹
static void IOT_DefrostTempReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t defrost_mode = Get_DefrostMode();

    if(((DefrostMode_t)eDefrostMode_PreHold < defrost_mode) && ((DefrostMode_t)eDefrostMode_Completed > defrost_mode))
    {
        int16_t u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_DEFROST);

        if((u16_sensor_value % 10) >= 5)
        {
            u16_sensor_value = (u16_sensor_value - 500) / 10 + 1;
        }
        else
        {
            u16_sensor_value = (u16_sensor_value - 500) / 10;
        }
        IOT_FridgeParam(IOT_DL_PROP_DEFROST_TEMP, (TLong)u16_sensor_value);
    }
}

// 閿熸枻鎷烽敓鏂ゆ嫹鏇抽敓鏂ゆ嫹閿燂拷
static void IOT_RefDamperReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t u8_device_state = (uint8_t)Get_ActiveDamperState();

    IOT_FridgeParam(IOT_DL_PROP_REF_DAMPER, (TLong)u8_device_state);
}

// 	閿熸垝鍐婚敓鏂ゆ嫹閿熼ズ纰夋嫹浣�
static void IOT_FrzFanLevelReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t u8_device_state = Get_FanDuty(FRZ_FAN);

    if(u8_device_state >= 30)
    {
        u8_device_state = u8_device_state / 5 - 5;
    }
    IOT_FridgeParam(IOT_DL_PROP_FRZ_FAN, (TLong)u8_device_state);
}

// 鍘嬮敓鏂ゆ嫹閿熸枻鎷疯浆閿熸枻鎷�
static void IOT_CompSpeedReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint16_t u16_device_state = Get_CompFreq();

    u16_device_state = u16_device_state * 30;
    IOT_FridgeParam(IOT_DL_PROP_COMP_SPEED, (TLong)u16_device_state);
}

// 閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓楗虹鎷蜂綅
static void IOT_CoolFanLevelReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t u8_device_state = Get_FanDuty(COOL_FAN);

    if(u8_device_state >= 30)
    {
        u8_device_state = u8_device_state / 5 - 5;
    }
    IOT_FridgeParam(IOT_DL_PROP_COOL_FAN, (TLong)u8_device_state);
}

// 	閿熸枻鎷烽敓鏂ゆ嫹閿熸彮鍑ゆ嫹閿熸枻鎷�
static void IOT_VarDamperReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t u8_device_state = 0; //(uint8_t)Get_SlaveDamperState();

    IOT_FridgeParam(IOT_DL_PROP_VAR_DAMPER, (TLong)u8_device_state);
}

// 閿熸枻鎷烽敓閾拌埍鍑ゆ嫹閿熸枻鎷�
static void IOT_RefVarDamperReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t u8_device_state = (uint8_t)Get_SlaveDamperState();

    IOT_FridgeParam(IOT_DL_PROP_REFVAR_DAMPER, (TLong)u8_device_state);
}

// 閿熸枻鎷烽敓鏂ゆ嫹閿熼摪璁规嫹
static void IOT_RoomTempReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    int16_t u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_ROOM);

    if((u16_sensor_value % 10) >= 5)
    {
        u16_sensor_value = (u16_sensor_value - 500) / 10 + 1;
    }
    else
    {
        u16_sensor_value = (u16_sensor_value - 500) / 10;
    }
    IOT_FridgeParam(IOT_DL_PROP_ROOMTTEMP, (TLong)u16_sensor_value);
}

// 閿熸枻鎷烽敓鏂ゆ嫹婀块敓鏂ゆ嫹
static void IOT_HumidityReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t u8_sensor_value = Get_HumidityRange();

    u8_sensor_value = u8_sensor_value * 5 + 10;
    IOT_FridgeParam(IOT_DL_PROP_HUMIDITY, (TLong)u8_sensor_value);
}

// 閿熻剼鍖℃嫹閿熸枻鎷�
static void IOT_DoorStateReportedJudge(void) // 閿熻緝鎲嬫嫹
{
    uint8_t DoorState = 0;
    uint8_t DoorStateRL = Get_DoorSwitchflagState(DOOR_REF_LEFT);
    uint8_t DoorStateRR = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
    uint8_t DoorStateFL = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
    uint8_t DoorStateFR = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);

    if(DoorStateRL & 0x02)
    {
        DoorState |= 0x01;
    }
    if(DoorStateRR & 0x02)
    {
        DoorState |= 0x02;
    }
    if(DoorStateFL & 0x02)
    {
        DoorState |= 0x04;
    }
    if(DoorStateFR & 0x02)
    {
        DoorState |= 0x08;
    }

    IOT_FridgeParam(IOT_PROP_DOOR_STATE, (TLong)DoorState);
}

static IotExecuteRet_e IOT_FridgeStartShakeSetJudge(TLong *value, TByte length);
static IotExecuteRet_e IOT_FridgeStopShakeSetJudge(TLong *value, TByte length);
static IotExecuteRet_e IOT_FactoryReset(TLong *value, TByte length);

typedef IotExecuteRet_e (*PFNFRIDGEACTIONSETHANDLER)(TLong *value, TByte length);

typedef struct SFridgeActionIottoDev
{
    IotActionName_e eActionName; // 閿熸枻鎷峰墠閿熸枻鎷烽敓鏂ゆ嫹
    PFNFRIDGEACTIONSETHANDLER pfnFridgeActionIottoDev; // IoT閿熼摪鍑ゆ嫹
} TSControlFridgeAction;

static const TSControlFridgeAction gdsFridgeActionCtlFunc[IOT_ACTION_MAX] = {
    { IOT_ACTION_RESET, IOT_FactoryReset },
    { IOT_ACTION_PLUGIN_CONNECT, IOT_FridgeStartShakeSetJudge },
    { IOT_ACTION_PLUGIN_DISCONNECT, IOT_FridgeStopShakeSetJudge },

};

static IotExecuteRet_e IOT_FridgeStartShakeSetJudge(TLong *value, TByte length) // IOT閿熼摪鍑ゆ嫹
{
    return IOT_EXECUTE_OK;
}

static IotExecuteRet_e IOT_FridgeStopShakeSetJudge(TLong *value, TByte length) // IOT閿熼摪鍑ゆ嫹
{
    return IOT_EXECUTE_OK;
}

static IotExecuteRet_e IOT_FactoryReset(TLong *value, TByte length) // IOT閿熼摪鍑ゆ嫹
{
    Wakeup_UserInterface();
    Update_RefSetTemp((uint8_t)REF_LEVEL_5);
    Set_NumberDisplay((DisplayZone_t)eDispRef_Zone, false, ((uint8_t)REF_LEVEL_5));

    Update_FrzSetTemp((uint8_t)FRZ_LEVEL_F18);
    Set_NumberDisplay((DisplayZone_t)eDispFrz_Zone, false, (uint8_t)FRZ_LEVEL_F18);

    Update_RefVarSetTemp((uint8_t)eRefVar_Treasure);
    Set_RefVarDisplay(false, (uint8_t)eRefVar_Treasure);

    Set_UserMode((UserMode_t)eFuzzy_Mode);
    Set_ModeDisplay(false, (UserMode_t)eFuzzy_Mode);

    Set_MusicType((MusicType_t)eShort_Tone);

    return IOT_EXECUTE_OK;
}

IotExecuteRet_e IOT_FridgeParamCallback(IotPropName_e propName, TLong value)
{
    TByte rIndex;
    IotExecuteRet_e eRet;

    for(rIndex = 0; rIndex < IOT_PROP_MAX; rIndex++)
    {
        if(propName == gdsFridgeParamCtlFunc[rIndex].ePropName)
        {
            eRet = gdsFridgeParamCtlFunc[rIndex].pfnFridgeParamIottoDev(value);
            break;
        }
    }

    return eRet;
}

IotExecuteRet_e IOT_FridgeActionCallback(IotActionName_e action, TLong *value, TByte length)
{
    TByte rIndex;
    IotExecuteRet_e eRet;

    for(rIndex = 0; rIndex < IOT_ACTION_MAX; rIndex++)
    {
        if(action == gdsFridgeActionCtlFunc[rIndex].eActionName)
        {
            eRet = gdsFridgeActionCtlFunc[rIndex].pfnFridgeActionIottoDev(value, length);
            break;
        }
    }

    return eRet;
}

typedef enum
{
    IOT_FristProcedure = 0,
    IOT_SecondProcedure,
    IOT_ThirdProcedure,
    IOT_FourthProcedure,
    IOT_FifthProcedure,
} IotTDatetimes_e;

#define IOT_FristCnt 8
#define IOT_SecondCnt NUM_ELEMENTS(gdsFridgeParamCtlFunc)
#define IOT_ThirdCnt NUM_ELEMENTS(gdsFridgeParamCtlFunc_20min)
#define IOT_FourthCnt NUM_ELEMENTS(gdsFridgeParamCtlFunc_60min)
#define IOT_FifthCnt NUM_ELEMENTS(gdsFridgeParamCtlFunc_24h)

static TByte rIOTDateTimes = IOT_FristProcedure;

void IOT_FridgeDataUpdate(void)
{
    TByte rIotCnt;
    int DeviceReportTime = Miio_GetDeviceReportTimeMin();
    static int DeviceReportTime_20min = -19;
    static int DeviceReportTime_60min = -59;
    static int DeviceReportTime_24h = -1439;

    switch(rIOTDateTimes)
    {
        case IOT_FristProcedure:
            for(rIotCnt = 0; rIotCnt < IOT_FristCnt; rIotCnt++)
            {
                gdsFridgeParamCtlFunc[rIotCnt].pfnFridgeParamDevtoIot();
            }

            rIOTDateTimes = IOT_SecondProcedure;
            break;
        case IOT_SecondProcedure:
            for(rIotCnt = IOT_FristCnt; rIotCnt < IOT_SecondCnt; rIotCnt++)
            {
                gdsFridgeParamCtlFunc[rIotCnt].pfnFridgeParamDevtoIot();
            }

            rIOTDateTimes = IOT_ThirdProcedure;
            break;
        case IOT_ThirdProcedure:
            if(DeviceReportTime - DeviceReportTime_20min >= IOT_DEVICE_REPORT_TIME_20MIN)
            {
                DeviceReportTime_20min = DeviceReportTime;
                for(rIotCnt = 0; rIotCnt < IOT_ThirdCnt; rIotCnt++)
                {
                    gdsFridgeParamCtlFunc_20min[rIotCnt].pfnFridgeParamDevtoIot();
                }
            }
            rIOTDateTimes = IOT_FourthProcedure;
            break;
        case IOT_FourthProcedure:
            if(DeviceReportTime - DeviceReportTime_60min >= IOT_DEVICE_REPORT_TIME_60MIN)
            {
                DeviceReportTime_60min = DeviceReportTime;
                for(rIotCnt = 0; rIotCnt < IOT_FourthCnt; rIotCnt++)
                {
                    gdsFridgeParamCtlFunc_60min[rIotCnt].pfnFridgeParamDevtoIot();
                }
            }
            rIOTDateTimes = IOT_FifthProcedure;
            break;
        case IOT_FifthProcedure:
            if(DeviceReportTime - DeviceReportTime_24h >= IOT_DEVICE_REPORT_TIME_24H)
            {
                DeviceReportTime_24h = DeviceReportTime;
                for(rIotCnt = 0; rIotCnt < IOT_FifthCnt; rIotCnt++)
                {
                    gdsFridgeParamCtlFunc_24h[rIotCnt].pfnFridgeParamDevtoIot();
                }
            }
            rIOTDateTimes = IOT_FristProcedure;
            break;
        default:
            break;
    }
}

void IOT_Func(void)
{
    miio_command_rx_tx();
    IOT_FridgeDataUpdate();
    sync_fridge_params_set_by_iot(IOT_FridgeParamCallback);
    sync_action_cached_by_iot(IOT_FridgeActionCallback);
}
/* ========== local functions ======================================================================================= */

/*=====================================================================@@mb=*/
