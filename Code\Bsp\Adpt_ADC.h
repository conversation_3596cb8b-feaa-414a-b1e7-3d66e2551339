/*!
 * @file
 * @brief This file defines public constants, types and functions for the clock adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _Adpt_ADC_H_
#define _Adpt_ADC_H_

#include <stdint.h>
#include "adc.h"

enum
{
    eSqrChannel_RefSnr = 0,
    eSqrChannel_VvSnr,
    eSqrChannel_VarSnr,
    eSqrChannel_AcVolt,
    eSqrChannel_DcVolt,
    eSqrChannel_HumSnr,
    eSqrChannel_RoomSnr,
    eSqrChannel_FrzSnr,
    eSqrChannel_FrzDefSnr,
    eSqrChannel_Max
};
typedef uint8_t AdcSqrChannel_t;

void Board_InitAdc(void);
void ADC_Restart(void);
uint16_t ADC_GetResult(uint8_t sqr_Channel);

#endif
