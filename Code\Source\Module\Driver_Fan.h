/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef DRIVER_FAN_H
#define DRIVER_FAN_H

#include <stdint.h>
#include <stdbool.h>

#define U8_FAN_PWM_PERCENT_INIT_VALUE 101
#define U16_SPEED_ERROR_LIMITVALUE 200
#define U8_FAN_START_PWM_PERCENT 50
#define U8_FRZ_FAN_START_PWM_PERCENT 69
#define U8_FAN_START_PWM_PERCENT_THREE_WIRE 69
#define U8_FAN_ERROR_START_PWM_PERCENT 100
#define U8_FAN_ERROR_START_PWM_PERCENT_THREE_WIRE 100
#define U16_FAN_START_PWM_DELAY 3 // s
#define U16_FAN_START_PWM_KEEPTIME 10
#define U8_FAN_ADJUST_V_DELAY 10

#define U16_FAN_STOP_POW_DELAY 3
#define U16_FAN_ERROR_POWEROFF_TIME 44
#define U16_THREEWIRE_FAN_ERROR_POWEROFF_TIME 40
#define U16_FAN_ERROR_TENCYCLE_INTERVAL (50 * 60)
#define U8_FAN_ERROR_START_MAXCOUNT 10

#define STEP_ZERO 0
#define STEP_ONE 1
#define STEP_TWO 2
#define STEP_THREE 3
#define STEP_FOUR 4
#define STEP_FIVE 5
#define STEP_SIX 6
#define STEP_SEVEN 7
#define STEP_EIGHT 8
#define STEP_NINE 9
#define STEP_TEN 10
#define STEP_FINISH 0xff

#define RPM_0DUTY (uint8_t)0
#define RPM_30DUTY (uint8_t)1 // 700
#define RPM_35DUTY (uint8_t)2 // 820
#define RPM_40DUTY (uint8_t)3 // 930
#define RPM_45DUTY (uint8_t)4 // 1030
#define RPM_50DUTY (uint8_t)5 // 1140
#define RPM_55DUTY (uint8_t)6 // 1220
#define RPM_60DUTY (uint8_t)7 // 1330
#define RPM_65DUTY (uint8_t)8 // 1420
#define RPM_70DUTY (uint8_t)9 // 1510
#define RPM_75DUTY (uint8_t)10 // 1600
#define RPM_80DUTY (uint8_t)11 // 1660
#define RPM_85DUTY (uint8_t)12 // 1750
#define RPM_90DUTY (uint8_t)13 // 1830
#define RPM_95DUTY (uint8_t)14 // 1900
#define RPM_100DUTY (uint8_t)15 // 1960
#define RPM_MAX (uint8_t)16

#define LEVEL_LOW 0
#define LEVEL_HIGH 1

#define U16_COMPUTER_FANV_LONGINTERVAL 10 // s
#define U16_COMPUTER_FANV__SHORTINTERVAL 2 // s
#define U16_FANV_LPARAM (60 / U16_COMPUTER_FANV_LONGINTERVAL)
#define U16_FANV_SPARAM (60 / U16_COMPUTER_FANV__SHORTINTERVAL)
#define U16_FANV_TWOPULSE_PARAM (2 * 2)
#define U16_FANV_FOURPULSE_PARAM (4 * 2)

#define U8_FBPULSE_SUM_COUNT_UPLIMIT 5

#define CON_PULSE_ONE 1
#define CON_PULSE_TWO 2
#define CON_PULSE_THREE 3
#define CON_PULSE_FOUR 4

#define U16_FAN_ADJUST_V_FAST_LIMIT 50
#define U16_FAN_ADJUST_V_STEADY_LIMIT 8
#define U16_FAN_ADJUST_V_REG_CHANGE_VALUE 4
#define U16_FAN_ADJUST_V_INTERVAL 2
#define U16_FAN_ADJUST_V_RANG 20

#define U8_FAN_DETECT_PWM_VALUE 100

/*Vx = (AD * 5 * (1000+2700))/(1000*1024)*/
#define ADToVoltage(AD) ((AD) * 18) // 0.018V
#define FAN_TREND_STOP 0
#define FAN_TREND_UP 1
#define FAN_TREND_DOWN 2
#define U16_FAN_START_ADJUST_VOLTAGE_LIMIT 300
#define U8_FAN_SHOCK_COUNT 3
#define U16_FAN_DEMARCATION_VOLATGE 1000

typedef enum fan_em
{
    FAN_ID0,
    FAN_ID1,
    FRZ_FAN = FAN_ID0,
    COOL_FAN = FAN_ID1,
    FAN_MAXNUMBER
} Fan_em;

typedef enum fourWireFanState
{
    FAN_STATE_STOP,
    FAN_STATE_RUN,
    FAN_STATE_ERROR
} FourWireFanState_em;

typedef enum fanRunProcess
{
    FANPOWOFF,
    FANPOWERON,
    FANSTARTPWM,
    FANRUN,
    FANSTOPPWM,
} FanRunProcess_em;

typedef struct fanSpeed
{
    uint16_t u16_SFbPulseNumber;
    uint16_t u16_SFbPulseNumberBak;
    uint16_t u16_LFbPulseNumberSum;
    uint16_t u16_LFbPulseNumberSumBak;
    uint16_t u16_TimerSec;
    uint16_t u16_LVTimerSec;
    uint16_t u16_SVTimerSec;
    uint16_t u16_SetSpeed;
    uint16_t u16_CurSpeed;
    uint16_t u16_PreOffsetVoltage;
    uint16_t u16_SetVoltage;
    uint16_t u16_JudgeErrorSpeed;
    uint8_t u8_ShockCount;
    uint8_t u8_Trend;
    uint8_t u8_LParam;
    uint8_t u8_SParam;
    uint8_t u8_LCount;
    uint8_t u8_AdjVStep;
    bool b_IsLComputerEd;
    bool b_IsLGetFbValueEd;
    bool b_IsCurFasterSet;
    bool b_IsBeginCurFasterSet;
    uint16_t (*Get_FanVoltage)(void);
} FanSpeed_st;

typedef struct fan_st
{
    FanSpeed_st st_FanSpeed;
    FanRunProcess_em em_RunProcess;
    FourWireFanState_em em_FanState;

    uint16_t u16_StepTimerSec;
    uint16_t u16_StepInterValSec;
    uint16_t u16_PwmRegCycleValue;
    uint16_t u16_PwmRegDutyValue;
    uint16_t u16_PwmRegDutyDownLimitValue;
    uint16_t u16_GetFbTimerSec;

    uint8_t u8_FanType;
    uint8_t u8_FbPulseState;
    uint8_t u8_PreFbPulseState;
    uint8_t u8_SetSpeedMethod;
    uint8_t u8_AppSetPwmPercent;
    uint8_t u8_SetPwmPercent;
    uint8_t u8_CurPwmPercent;
    uint8_t u8_OneRoundPlus;
    uint8_t u8_ErrorStep;
    uint8_t u8_ErrorStartCount;
    uint8_t u8_AdjustVDelay;

    bool b_IsInit;
    bool b_IsOn;
    bool b_IsError;
    bool b_IsContinueError;
    bool b_IsFeedBackLow;
    void (*Ctrl_Power)(bool);
    void (*Set_FanPwmRegValue)(uint16_t);
    uint8_t (*Read_FeedbackIO)(void);
} Fan_st;

void Count_AllFanFbPulse(void);
void Driver_AllFan(void);
void Set_FanSpeed(Fan_em em_fanType, uint16_t u16_speed);
void Set_FrzFanDuty(uint8_t duty);
void Set_CoolFanSpeed(uint8_t u8_speedIndex);
void Set_FanPwmPercent(Fan_em em_fanType, uint8_t u8_pwmPercent);
void Set_FanVoltage(Fan_em em_fan, uint16_t u16_voltage);
uint16_t Get_FanParameter(Fan_em em_fanType);
uint16_t Get_FanDuty(Fan_em em_fanType);
bool Is_FanError(Fan_em em_fanType);
bool Is_FanContinueError(Fan_em em_fanType);
void Force_FanState(uint8_t em_fanType, bool b_testState);

#endif
