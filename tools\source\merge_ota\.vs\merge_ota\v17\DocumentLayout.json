{"Version": 1, "WorkspaceRootPath": "D:\\冰箱\\508\\iot_508\\merge_ota\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\merge_ota.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|solutionrelative:merge_ota\\merge_ota.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\hw_port.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:merge_ota\\hw_port.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\merge_ota.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|solutionrelative:merge_ota\\merge_ota.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\soft_crc.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|solutionrelative:merge_ota\\soft_crc.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\stdafx.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|solutionrelative:merge_ota\\stdafx.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\stdafx.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|solutionrelative:merge_ota\\stdafx.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\ReadMe.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{C105A79A-0B94-483E-BF85-764A96F3E9A6}|merge_ota\\merge_ota.vcxproj|solutionrelative:merge_ota\\ReadMe.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\multi_mcu_ota.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:merge_ota\\multi_mcu_ota.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 7, "Children": [{"$type": "Document", "DocumentIndex": 7, "Title": "multi_mcu_ota.h", "DocumentMoniker": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\multi_mcu_ota.h", "RelativeDocumentMoniker": "merge_ota\\multi_mcu_ota.h", "ToolTip": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\multi_mcu_ota.h", "RelativeToolTip": "merge_ota\\multi_mcu_ota.h", "ViewState": "AQIAABYAAAAAAAAAAAAswCQAAAAyAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2024-07-19T02:43:29.305Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 1, "Title": "hw_port.h", "DocumentMoniker": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\hw_port.h", "RelativeDocumentMoniker": "merge_ota\\hw_port.h", "ToolTip": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\hw_port.h", "RelativeToolTip": "merge_ota\\hw_port.h", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2024-07-19T02:44:00.987Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "merge_ota.h", "DocumentMoniker": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\merge_ota.h", "RelativeDocumentMoniker": "merge_ota\\merge_ota.h", "ToolTip": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\merge_ota.h", "RelativeToolTip": "merge_ota\\merge_ota.h", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2024-07-19T02:43:17.399Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "ReadMe.txt", "DocumentMoniker": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\ReadMe.txt", "RelativeDocumentMoniker": "merge_ota\\ReadMe.txt", "ToolTip": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\ReadMe.txt", "RelativeToolTip": "merge_ota\\ReadMe.txt", "ViewState": "AQIAABIAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2024-07-19T02:41:19.079Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "stdafx.cpp", "DocumentMoniker": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\stdafx.cpp", "RelativeDocumentMoniker": "merge_ota\\stdafx.cpp", "ToolTip": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\stdafx.cpp", "RelativeToolTip": "merge_ota\\stdafx.cpp", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-07-17T07:48:22.066Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "soft_crc.c", "DocumentMoniker": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\soft_crc.c", "RelativeDocumentMoniker": "merge_ota\\soft_crc.c", "ToolTip": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\soft_crc.c", "RelativeToolTip": "merge_ota\\soft_crc.c", "ViewState": "AQIAAAsAAAAAAAAAAAAswBkAAAATAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2024-07-17T07:48:20.459Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "stdafx.h", "DocumentMoniker": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\stdafx.h", "RelativeDocumentMoniker": "merge_ota\\stdafx.h", "ToolTip": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\stdafx.h", "RelativeToolTip": "merge_ota\\stdafx.h", "ViewState": "AQIAAAQAAAAAAAAAAAAWwBAAAAAGAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2024-07-17T01:42:08.595Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "merge_ota.cpp", "DocumentMoniker": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\merge_ota.cpp", "RelativeDocumentMoniker": "merge_ota\\merge_ota.cpp", "ToolTip": "D:\\冰箱\\508\\iot_508\\merge_ota\\merge_ota\\merge_ota.cpp", "RelativeToolTip": "merge_ota\\merge_ota.cpp", "ViewState": "AQIAALQAAAAAAAAAAAAmwD4BAABLAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-07-17T01:39:46.377Z", "EditorCaption": ""}]}]}]}