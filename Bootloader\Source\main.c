/*!
 * @file
 * @brief Main program.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stdint.h>
#include "Init_Mcu.h"
#include "Driver_Flash.h"
#include "OtaManager.h"
#include "DisplayUsart.h"
#include "InverterUsart.h"
#include "FirewareComm.h"
#include "Adpt_Iwdg.h"
#include "syslog.h"

int main(void)
{
    Debug_Init();
    Init_Mcu();
    Init_UartDisplay();
    Init_UartInverter();
    Init_Flash();
    OtaManagerInit();

    while(1)
    {
        IWDG_Refesh();
        OtaManagerRun();
        Handle_UartDisplayFrame();
    }
    return (1);
}
