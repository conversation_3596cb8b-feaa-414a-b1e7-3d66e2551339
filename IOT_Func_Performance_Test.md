# IOT_Func 函数执行时间测量

## 概述
本文档描述了如何通过GPIO端口翻转来测量IOT_Func函数的执行时间。

## 实现方案

### 选择的GPIO端口
- **端口**: PC13 (GpioPortC, GpioPin13)
- **物理引脚**: PIN2
- **状态**: 未使用的GPIO口，已配置为普通IO输出

### 代码修改

#### 1. 头文件包含
在 `Code/Source/Iot/Iot.c` 文件中添加了GPIO适配器头文件：
```c
#include "Adpt_GPIO.h" // 添加GPIO适配器头文件
```

#### 2. IOT_Func函数修改
在IOT_Func函数的开始和结束位置添加了GPIO翻转代码：

```c
void IOT_Func(void)
{
    // 设置PC13为高电平，标记函数开始执行
    Gpio_SetIO(GpioPortC, GpioPin13);
    
    miio_command_rx_tx();
    IOT_FridgeDataUpdate();
    sync_fridge_params_set_by_iot(IOT_FridgeParamCallback);
    sync_action_cached_by_iot(IOT_FridgeActionCallback);
    
    // 设置PC13为低电平，标记函数执行结束
    Gpio_ClrIO(GpioPortC, GpioPin13);
}
```

## 测量方法

### 硬件连接
1. 将示波器探头连接到PC13引脚（PIN2）
2. 将示波器地线连接到系统地

### 测量步骤
1. 启动系统，确保IOT_Func函数正常调用
2. 使用示波器观察PC13引脚的波形
3. 测量高电平持续时间，即为IOT_Func函数的执行时间

### 波形特征
- **高电平**: 函数执行期间
- **低电平**: 函数未执行期间
- **上升沿**: 函数开始执行
- **下降沿**: 函数执行结束

## 注意事项

1. **GPIO初始化**: PC13已在Board_InitPins()函数中初始化为输出模式，默认输出低电平
2. **性能影响**: GPIO操作非常快速（通常几个时钟周期），对函数执行时间的影响可忽略不计
3. **测量精度**: 示波器的时间基准应设置为适当的精度以准确测量执行时间
4. **多次测量**: 建议进行多次测量以获得平均执行时间和执行时间的变化范围

## 其他可用的未使用GPIO口

如果PC13不可用，还可以选择以下未使用的GPIO口：
- PC14 (GpioPortC, GpioPin14) - PIN3
- PC15 (GpioPortC, GpioPin15) - PIN4  
- PC11 (GpioPortC, GpioPin11) - PIN52

使用方法相同，只需要修改Gpio_SetIO和Gpio_ClrIO函数中的端口和引脚参数即可。

## 编译和测试

1. 编译项目确保没有编译错误
2. 烧录到目标硬件
3. 使用示波器进行测量
4. 记录和分析测量结果
