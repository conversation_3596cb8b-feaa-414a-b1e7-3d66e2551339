#include "miio_define.h"
#include "miio_api.h"
#include "miio_uart.h"

#include "user_app_func.h"
#include "Driver_Flash.h"
#include "ParameterManager.h"

int app_func_get_time(char *pResult)
{
    strcpy(pResult, "time\r");
    return 0;
}

int app_func_get_mac(char *pResult)
{
    strcpy(pResult, "mac\r");

    return 0;
}

int app_func_get_model(char *pResult)
{
    strcpy(pResult, "model\r");
    return 0;
}

int app_func_get_version(char *pResult)
{
    strcpy(pResult, "version\r");
    return 0;
}

int app_func_getwifi(char *pResult)
{
    strcpy(pResult, "getwifi\r");
    return 0;
}

int app_func_get_arch_platform(char *pResult)
{
    strcpy(pResult, "arch\r");
    return 0;
}

int app_func_get_net_state(char *pResult)
{
    strcpy(pResult, "net\r");
    return 0;
}

int app_func_reboot(char *pResult)
{
    strcpy(pResult, "reboot\r");
    return 0;
}

int app_func_restore(char *pResult)
{
    strcpy(pResult, "restore\r");

    return 0;
}

int app_func_setwifi(char *pResult)
{
    strcpy(pResult, "setwifi " USER_SSID " " USER_PASSWD "\r");
    return 0;
}

int app_func_set_mcu_version(char *pResult)
{
    strcpy(pResult, "mcu_version " USER_MCU_VERSION "\r");
    return 0;
}

int app_func_factory(char *pResult)
{
    strcpy(pResult, "factory\r");

    return 0;
}

int app_func_sn(char *pResult)
{
    uint8_t fct_sn[PRODUCT_SN_SIZE + 1] = { 0 };

    if(ReadProductSn(fct_sn, PRODUCT_SN_SIZE) < 0)
    {
        strcpy((char *)fct_sn, "null");
    }
    strcpy(pResult, "sn ");
    strcat(pResult, (const char *)fct_sn);
    strcat(pResult, "\r");
    return 0;
}

int app_func_get_sn(char *pResult)
{
    strcpy(pResult, "sn\r");
    return 0;
}

int app_func_model(char *pResult)
{
    uint8_t model[PRODUCT_MODEL_SIZE + 1] = { 0 };

    ReadProductUserModel(model, PRODUCT_MODEL_SIZE);
    strcpy(pResult, "model ");
    strcat(pResult, (const char *)model);
    strcat(pResult, "\r");
    return 0;
}

int app_func_ble_conf(char *pResult)
{
    uint8_t model[PRODUCT_MODEL_BYTES + 1] = { 0 };
    uint8_t *pid = NULL;

    ReadProductModel(model, PRODUCT_MODEL_BYTES);
    if(pid != NULL)
    {
        sprintf(pResult, "ble_config set %s %04d\r", pid, GetMcuVersion());
    }
    return 0;
}
