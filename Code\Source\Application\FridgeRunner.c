/*!
 * @file
 * @brief Manages all the state variables of the fridge runner.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "FridgeRunner.h"
#include "Core_CallBackTimer.h"
#include "Parameter_TemperatureZone.h"
#include "Driver_AdSample.h"
#include "Driver_DoorSwitch.h"
#include "Driver_DoubleDamper.h"
#include "SystemTimerModule.h"
#include "VerticalBeamHeater.h"
#include "DisplayInterface.h"
#include "FaultCode.h"
#include "InverterUsart.h"

#define FRIDGE_NUM_COUNTS_PER_MINUTE (uint16_t)60
#define U16_FRIDGERUNNER_CYCLE_SECOND (uint16_t)1
#define U16_TEMP_SWINGS_RANGE (uint16_t)20
#define U16_ENTER_ENERGY_MODE_MINUTES (uint16_t)(20 * 60)
#define U16_ENTER_CONDENSATION_MODE_MINUTES (uint16_t)240
#define U16_EXIT_CONDENSATION_MODE_MINUTES (uint16_t)60
#define U16_ROOM_RANGE_EXIT_MINUTES (uint16_t)10
#define U16_ROOM_SWINGS_EXIT_MINUTES (uint16_t)30
#define U16_HUMI_RANGE_EXIT_MINUTES (uint16_t)20
#define U8_ENERGY_MODE_EXIT_REF_DOOR_OPEN_COUNT (uint8_t)5
#define U16_POWER_ON_DELAY_REF_VAR_COLLING_MINUTES (uint16_t)10

// clang-format off
static DefrostNormalCondition_st ary_DefrostNormalCondition[] = {
    // RT <= 13
    {
        5,
        { // 冰箱时间    温区时间  冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,   240,   240,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,   120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 24 * 60,  0xFFFF,    90,    90,   0xFFFF, CON_8P0_DEGREE },
            { 36 * 60,  0xFFFF,    30,    30,   0xFFFF, CON_8P0_DEGREE },
            { 48 * 60,  0xFFFF,     0,     0,   0xFFFF, CON_3P0_DEGREE },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 } } },
    // 13 < RT <= 35
    {
        7,
        { // 冰箱时间   温区时间  冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,  540,   540,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,  360,   360,   0xFFFF, CON_8P0_DEGREE },
            { 24 * 60,  0xFFFF,  240,   240,   0xFFFF, CON_8P0_DEGREE },
            { 36 * 60,  0xFFFF,  120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 48 * 60,  0xFFFF,   90,    90,   0xFFFF, CON_8P0_DEGREE },
            { 60 * 60,  0xFFFF,   30,    30,   0xFFFF, CON_8P0_DEGREE },
            { 73 * 60,  0xFFFF,    0,     0,   0xFFFF, CON_8P0_DEGREE } } },
    // 35 < RT
    {
        3,
        { // 冰箱时间   温区时间   冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,   360,   360,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,   120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,     0,     0,   0xFFFF, CON_1P0_DEGREE },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 } } }
};
// clang-format on

enum
{
    Signal_Entry = SimpleFsmSignal_Entry,
    Signal_Exit = SimpleFsmSignal_Exit,
    Signal_PollTimerExpired = SimpleFsmSignal_UserStart,
    Signal_EnterDefrosting
};

static st_CoreCallbackTimer st_RunningTimer;
static SimpleFsm_t st_RunningFsm;
static RunningState_t runningState;
static uint16_t u16_FridgeRuntimeStart;
static uint16_t u16_FridgeRuntimeMinute;
static bool b_PowerOnDelayRefVarCooling = true;
static uint16_t u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE;
static EnterDefrostingState_t enterDefrostingState;
static EnterDefrostingState_t enterDefrostingStateBak;
static bool b_FirstDefrostingEntered;
static CoolingEntryMode_t coolingEntryMode;
static SaveDefrostType_st st_SaveDefrostType;
static TurboFreezeDefrostingState_t turboFreezeDefrostingState;
static bool b_TurboFreezeDefrostingAutoExitState;
static bool b_OverLoadDefrostingInTurboFreeze;
static bool b_EnergyConsumptionMode;
static bool b_EnergyModeFirstDeforst;
static uint16_t u16_EnterEnergyModeTimeStart;
static uint16_t u16_EnterEnergyModeMinute;
static uint16_t u16_EnergyModeDefrostTimeStart;
static uint16_t u16_EnergyDefrostModeMinute;
static uint16_t u16_ExitEnergyModeTimeStart;
static uint16_t u16_ExitEnergyModeMinute;
static uint16_t u16_RoomTempBackup;
static uint16_t u16_EneryExitRoomTempBackup;
static bool b_EneryExitRoomTempSwingsOverRange;
static bool b_CondensationMode;
static uint16_t u16_EnterCondensationModeTimeStart;
static uint16_t u16_EnterCondensationModeMinute;
static uint16_t u16_ExitCondensationModeTimeStart;
static uint16_t u16_ExitCondensationModeMinute;
static uint8_t u8_RefDoorCounter;
static uint16_t u16_TotalPower;
static uint32_t u32_ElectricEnergy;

static void Judge_EnergyConsumptionMode(void)
{
    bool door_state = Get_DoorSwitchState((DoorTypeId_t)DOOR_ALL);
    UserMode_t user_mode = Get_UserMode();
    uint8_t fault_number = Get_FaultCodeNumber();
    HumidityRange_t humi_range = Get_HumidityRange();
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_room_energy = false;
    uint16_t room_temp = Get_SensorValue((uint8_t)SENSOR_ROOM);
    bool b_RoomTempSwingsOverRange = false;
    uint8_t ref_door_counter = 0;
    bool b_time_out = false;

    if((room_range == RT_BELOW18) || (room_range == RT_BELOW35))
    {
        b_room_energy = true;
    }

    if((u16_RoomTempBackup > (room_temp + U16_TEMP_SWINGS_RANGE)) ||
        ((u16_RoomTempBackup + U16_TEMP_SWINGS_RANGE) < room_temp))
    {
        u16_RoomTempBackup = room_temp;
        b_RoomTempSwingsOverRange = true;
    }

    if(false == b_EnergyConsumptionMode)
    {
        if((false == door_state) &&
            (true == b_room_energy) &&
            (false == b_RoomTempSwingsOverRange) &&
            ((UserMode_t)eManual_Mode == user_mode) &&
            (0 == fault_number) &&
            (HBELOW70 >= humi_range))
        {
            u16_EnterEnergyModeMinute = Get_MinuteElapsedTime(u16_EnterEnergyModeTimeStart);
            if(u16_EnterEnergyModeMinute >= U16_ENTER_ENERGY_MODE_MINUTES)
            {
                b_EnergyConsumptionMode = true;
                b_EnergyModeFirstDeforst = false;
                u16_RoomTempBackup = room_temp;
                u16_EneryExitRoomTempBackup = room_temp;
                u16_ExitEnergyModeTimeStart = Get_MinuteCount();
                u16_EnergyModeDefrostTimeStart = Get_MinuteCount();
                u8_RefDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF_LEFT);
                u8_RefDoorCounter += Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF_RIGHT);
            }
        }
        else
        {
            u16_EnterEnergyModeTimeStart = Get_MinuteCount();
        }
    }
    else
    {
        ref_door_counter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF_LEFT);
        ref_door_counter += Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF_RIGHT);
        ref_door_counter -= u8_RefDoorCounter;

        if((u16_EneryExitRoomTempBackup > (room_temp + U16_TEMP_SWINGS_RANGE)) ||
            ((u16_EneryExitRoomTempBackup + U16_TEMP_SWINGS_RANGE) < room_temp))
        {
            b_EneryExitRoomTempSwingsOverRange = true;
        }
        else
        {
            b_EneryExitRoomTempSwingsOverRange = false;
        }

        if((true == b_room_energy) &&
            (false == b_EneryExitRoomTempSwingsOverRange) &&
            (HBELOW70 >= humi_range))
        {
            u16_ExitEnergyModeTimeStart = Get_MinuteCount();
        }
        u16_ExitEnergyModeMinute = Get_MinuteElapsedTime(u16_ExitEnergyModeTimeStart);
        if((false == b_room_energy) &&
            (U16_ROOM_RANGE_EXIT_MINUTES < u16_ExitEnergyModeMinute))
        {
            b_time_out = true;
        }

        if((true == b_EneryExitRoomTempSwingsOverRange) &&
            (U16_ROOM_SWINGS_EXIT_MINUTES < u16_ExitEnergyModeMinute))
        {
            b_time_out = true;
        }

        if((HBELOW70 < humi_range) &&
            (U16_HUMI_RANGE_EXIT_MINUTES < u16_ExitEnergyModeMinute))
        {
            b_time_out = true;
        }

        if((ref_door_counter > U8_ENERGY_MODE_EXIT_REF_DOOR_OPEN_COUNT) ||
            ((EnterDefrostingState_t)eEnterState_OverLoadError == enterDefrostingState) ||
            ((UserMode_t)eManual_Mode != user_mode) ||
            (0 != fault_number) ||
            (true == b_time_out))
        {
            b_EnergyConsumptionMode = false;
            u16_RoomTempBackup = room_temp;
            u16_EnterEnergyModeTimeStart = Get_MinuteCount();
        }
    }
}

static void Judge_CondensationMode(void)
{
    HumidityRange_t humi_range = Get_HumidityRange();
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_room_condensation = false;
    bool b_turbo_cool = false;
    bool b_TurboFreeze = false;

    b_turbo_cool = Get_TurboCoolState();
    b_TurboFreeze = Get_TurboFreezeState();

    if((room_range == RT_BELOW28) || (room_range == RT_BELOW35))
    {
        b_room_condensation = true;
    }

    if(false == b_CondensationMode)
    {
        if((true == b_room_condensation) &&
            (HBELOW70 < humi_range) &&
            (false == b_turbo_cool) &&
            (false == b_TurboFreeze))
        {
            u16_EnterCondensationModeMinute = Get_MinuteElapsedTime(u16_EnterCondensationModeTimeStart);
            if(u16_EnterCondensationModeMinute >= U16_ENTER_CONDENSATION_MODE_MINUTES)
            {
                b_CondensationMode = true;
                u16_ExitCondensationModeTimeStart = Get_MinuteCount();
            }
        }
        else
        {
            u16_EnterCondensationModeTimeStart = Get_MinuteCount();
        }
    }
    else
    {
        if((false == b_room_condensation) ||
            (HBELOW70 >= humi_range) ||
            (true == b_turbo_cool) ||
            (true == b_TurboFreeze))
        {
            u16_ExitCondensationModeMinute = Get_MinuteElapsedTime(u16_ExitCondensationModeTimeStart);
            if(u16_ExitCondensationModeMinute >= U16_EXIT_CONDENSATION_MODE_MINUTES)
            {
                b_CondensationMode = false;
                u16_EnterCondensationModeTimeStart = Get_MinuteCount();
            }
        }
        else
        {
            u16_ExitCondensationModeTimeStart = Get_MinuteCount();
        }
    }
}

void Update_PowerOnDelayRefVarCooling(void)
{
    if((CoolingEntryMode_t)eMode_FridgePowerOn == coolingEntryMode)
    {
        if((true == b_PowerOnDelayRefVarCooling) && (u16_FridgeRuntimeMinute >= U16_POWER_ON_DELAY_REF_VAR_COLLING_MINUTES))
        {
            Reset_DoubleDamper();
            b_PowerOnDelayRefVarCooling = false;
        }
    }
    else
    {
        b_PowerOnDelayRefVarCooling = false;
    }
}

void Update_ElectricEnergy(void)
{
    uint16_t u16_12V_power = Get_12VPower();
    uint16_t comp_power = Get_CompPower();
    uint16_t voltage = 0;
    float voltage_coef = 0;
    uint16_t defrost_heater_power = 0;
    bool b_defrost_heater_on_time = Get_DefrostHeaterOnSecond();

    if(0 != b_defrost_heater_on_time)
    {
        voltage = Get_CompBusVoltage();
        voltage /= 10;
        voltage_coef = ((float)voltage) / FLOAT_AC_VOLTAGE;
        voltage_coef *= voltage_coef;
        defrost_heater_power = (uint16_t)(voltage_coef * FLOAT_DEFROST_RATING);
    }
    // real power * 10
    u16_TotalPower = defrost_heater_power + u16_12V_power + U16_POWER_OFFSET + comp_power / 2;
    u32_ElectricEnergy += (uint32_t)u16_TotalPower;
}

static void PollTimerExpired(void)
{
    u16_FridgeRuntimeMinute = Get_MinuteElapsedTime(u16_FridgeRuntimeStart);
    Update_PowerOnDelayRefVarCooling();
    Ctrl_VerticalBeamHeater();
    Process_UserMode();
    Judge_EnergyConsumptionMode();
    Judge_CondensationMode();
    Update_ElectricEnergy();
    SimpleFsm_SendSignal(&st_RunningFsm, Signal_PollTimerExpired, NULL);
}

static void ArmPollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_RunningTimer,
        PollTimerExpired,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_RunningTimer);
}

static void RunningState_CoolingCycle(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void RunningState_Defrosting(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);

static void Save_DefrostType(EnterDefrostingState_t state)
{
    uint8_t u8_index = 0;

    st_SaveDefrostType.u8_SavedFlag = 0xAA;

    if(st_SaveDefrostType.u8_SavedCount < U8_DEFROST_TYPE_MAX_SAVE_NUMBER)
    {
        st_SaveDefrostType.u8_SavedCount++;
    }
    else
    {
        st_SaveDefrostType.u8_SavedCount = U8_DEFROST_TYPE_MAX_SAVE_NUMBER;
    }

    if(st_SaveDefrostType.u8_SavedCount >= 2)
    {
        for(u8_index = st_SaveDefrostType.u8_SavedCount - 1; u8_index > 0; u8_index--)
        {
            st_SaveDefrostType.ary_DefrostTypeBuff[u8_index] = st_SaveDefrostType.ary_DefrostTypeBuff[u8_index - 1];
        }
    }

    st_SaveDefrostType.ary_DefrostTypeBuff[0] = state;
}

static void Judge_EnterFirstDefrost(void)
{
    if(u16_FridgeRuntimeMinute >= U16_FIRST_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_First;
        u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE;
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static void Judge_EnterOverLoadDefrost(void)
{
    uint16_t comp_total_on_minute = Get_CompTotalOnTimeMinute();
    bool b_TurboFreeze = Get_TurboFreezeState();

    if(comp_total_on_minute >= U16_OVERLOAD_DEFROST_COMP_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_OverLoadError;
        u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE;
        if(true == b_TurboFreeze)
        {
            b_OverLoadDefrostingInTurboFreeze = true;
        }
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

void Set_TurboFreezeDefrostingAutoExitState(bool state)
{
    b_TurboFreezeDefrostingAutoExitState = state;
    if(false == state)
    {
        turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None;
    }
    b_OverLoadDefrostingInTurboFreeze = false;
}

static void Judge_EnterTurboFreezeDefrost(void)
{
    bool b_TurboFreeze = Get_TurboFreezeState();
    uint16_t turbo_freeze_minute = Get_TurboFreezeTimeMinute();

    switch(turboFreezeDefrostingState)
    {
        case(TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None:
            if(true == b_TurboFreeze)
            {
                turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_First;
            }
            enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
            break;
        case(TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_First:
            if((true == b_TurboFreeze) &&
                (turbo_freeze_minute >= U16_TURBO_FRZ_DEFROST_COMP_TOTAL_ON_TIME_MINUTES))
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_TurboFreeze;
                u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE;
                turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_Second;
            }
            else
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
            }
            break;
        case(TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_Second:
            if((false == b_TurboFreeze) && (true == b_TurboFreezeDefrostingAutoExitState))
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_TurboFreeze;
                u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE;
                turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None;
            }
            else
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
            }
            break;
        default:
            break;
    }
}

static bool Get_SensorErrorDefrost(void)
{
    bool b_state = false;
    bool b_frz_def_snr_error = Get_SensorError((SensorType_t)SENSOR_DEFROST);
    bool b_frz_snr_error = Get_SensorError((SensorType_t)SENSOR_FRZ);
    bool b_ref_door_error = Get_DoorSwitchErrorState((DoorTypeId_t)DOOR_REF);
    bool b_frz_door_error = Get_DoorSwitchErrorState((DoorTypeId_t)DOOR_FRZ);

    if((true == b_frz_snr_error) || (true == b_frz_def_snr_error))
    {
        b_state = true;
    }

    if((true == b_ref_door_error) || (true == b_frz_door_error))
    {
        b_state = true;
    }

    return (b_state);
}

static void Judge_EnterSensorErrorDefrost(void)
{
    uint16_t comp_total_on_minute = Get_CompTotalOnTimeMinute();

    if(comp_total_on_minute >= U16_FRZ_SENSOR_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_SensorError;
        u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE;
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static void Judge_EnterDefFunctionErrorDefrost(void)
{
    if(u16_FridgeRuntimeMinute >= U16_DEF_FUNCTION_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_DefFunctionError;
        u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE;
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static void Judge_EnterEnergyModeDefrost(void)
{
    uint16_t energy_deforst_minute = 0;

    if(false == b_EnergyModeFirstDeforst)
    {
        energy_deforst_minute = U16_ENERGY_FIRST_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES;
    }
    else
    {
        energy_deforst_minute = U16_ENERGY_MORMAL_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES;
    }

    u16_EnergyDefrostModeMinute = Get_MinuteElapsedTime(u16_EnergyModeDefrostTimeStart);
    if(u16_EnergyDefrostModeMinute >= energy_deforst_minute)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_EnergyMode;
        u16_DeforstExitTemp = U16_ENERGY_MODE_DEFROST_EXIT_TEMPERATURE;
        u16_EnergyModeDefrostTimeStart = Get_MinuteCount();
        b_EnergyModeFirstDeforst = true;
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static void Judge_EnterNormalDefrost(void)
{
    uint8_t u8_defrost_condition_romm_temp_index = 0; // 正常化霜环温
    uint8_t u8_defrost_condition_number = 0; // 环温段内条件数量
    uint8_t u8_index = 0; // 查询循环索引
    uint8_t u8_item = 0; // 查询条件索引
    uint16_t u16_ref_door_total_open_time_seconds = Get_DoorOpenTimeSecond((DoorTypeId_t)DOOR_REF); // 冷藏门开门时间
    uint16_t u16_frz_door_total_open_time_seconds = Get_DoorOpenTimeSecond((DoorTypeId_t)DOOR_FRZ);
    uint16_t u16_condition_fridge_total_on_time_minutes = 0;
    uint16_t u16_condition_ref_door_total_open_time_seconds = 0;
    uint16_t u16_condition_frz_door_total_open_time_seconds = 0;
    DefrostNormalCondition_st *p_condition = (DefrostNormalCondition_st *)NULL;
    RoomTempRange_t room_range = Get_RoomTempRange();

    enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;

    if(room_range <= RT_BELOW13)
    {
        u8_defrost_condition_romm_temp_index = 0;
    }
    else if(room_range <= RT_BELOW35)
    {
        u8_defrost_condition_romm_temp_index = 1;
    }
    else
    {
        u8_defrost_condition_romm_temp_index = 2;
    }
    p_condition = &ary_DefrostNormalCondition[u8_defrost_condition_romm_temp_index];
    u8_defrost_condition_number = p_condition->u8_DefrostConditionNumber;

    if(u8_defrost_condition_number > 0)
    {
        for(u8_index = 0, u8_item = u8_defrost_condition_number - 1;
            u8_index < u8_defrost_condition_number;
            u8_index++, u8_item--)
        {
            u16_condition_fridge_total_on_time_minutes =
                p_condition->ary_DefrostCondition[u8_item].u16_FridgeTotalOnTimeMinutes;

            u16_condition_ref_door_total_open_time_seconds =
                p_condition->ary_DefrostCondition[u8_item].u16_RefDoorTotalOpenTimeSecond;

            u16_condition_frz_door_total_open_time_seconds =
                p_condition->ary_DefrostCondition[u8_item].u16_FrzVarDoorTotalOpenTimeSecond;

            if((u16_FridgeRuntimeMinute >= u16_condition_fridge_total_on_time_minutes) &&
                ((u16_ref_door_total_open_time_seconds >= u16_condition_ref_door_total_open_time_seconds) ||
                    (u16_frz_door_total_open_time_seconds >= u16_condition_frz_door_total_open_time_seconds)))
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_Normal;
                u16_DeforstExitTemp = p_condition->ary_DefrostCondition[u8_item].u16_DefrostExitTemp;
                break;
            }
        }
    }
}

static void Update_EnterDefrostingState(void)
{
    bool b_OverLoadDefrost = false;
    bool b_SensorErrorDefrost = false;
    bool b_DefFunctionErrorDefrost = false;
    bool b_turbo_cool = false;
    bool b_TurboFreeze = false;

    b_OverLoadDefrost = Get_RefTempOverLoadDefrost();
    b_SensorErrorDefrost = Get_SensorErrorDefrost();
    b_DefFunctionErrorDefrost = Get_DefrostFunctionError();
    b_turbo_cool = Get_TurboCoolState();
    b_TurboFreeze = Get_TurboFreezeState();

    if(false == b_FirstDefrostingEntered)
    {
        Judge_EnterFirstDefrost();
    }
    else if(true == b_OverLoadDefrost)
    {
        Judge_EnterOverLoadDefrost();
    }
    else if((false == b_OverLoadDefrostingInTurboFreeze) &&
        ((true == b_TurboFreeze) || (turboFreezeDefrostingState != (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None)))
    {
        Judge_EnterTurboFreezeDefrost();
    }
    else if(true == b_SensorErrorDefrost)
    {
        Judge_EnterSensorErrorDefrost();
    }
    else if(true == b_DefFunctionErrorDefrost)
    {
        Judge_EnterDefFunctionErrorDefrost();
    }
    else if(true == b_turbo_cool)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
    else if(true == b_EnergyConsumptionMode)
    {
        Judge_EnterEnergyModeDefrost();
    }
    else
    {
        Judge_EnterNormalDefrost();
    }

    if(enterDefrostingStateBak != enterDefrostingState)
    {
        enterDefrostingStateBak = enterDefrostingState;
        Save_DefrostType(enterDefrostingState);
    }
}

static void RunningState_CoolingCycle(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            runningState = (RunningState_t)eRunning_CoolingCycle;
            CoolingCycle_Init(coolingEntryMode);
            Clear_DefrostMode();
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            Update_EnterDefrostingState();
            if((EnterDefrostingState_t)eEnterState_Forbid != enterDefrostingState)
            {
                SimpleFsm_Transition(&st_RunningFsm, RunningState_Defrosting);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            CoolingCycle_Exit();
            break;
        default:
            break;
    }
}

static void RunningState_Defrosting(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    DefrostMode_t defrost_mode;

    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            runningState = (RunningState_t)eRunning_Defrosting;
            Defrosting_Init(enterDefrostingState);
            b_FirstDefrostingEntered = true;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            defrost_mode = Get_DefrostMode();
            if((DefrostMode_t)eDefrostMode_Completed == defrost_mode)
            {
                coolingEntryMode = (CoolingEntryMode_t)eMode_DefrostingCompleted;
                SimpleFsm_Transition(&st_RunningFsm, RunningState_CoolingCycle);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            Defrosting_Exit();
            break;
        default:
            break;
    }
}

void FridgeRunner_Init(void)
{
    SimpleFsm_Init(&st_RunningFsm, RunningState_CoolingCycle, NULL);
    ArmPollTimer(U16_FRIDGERUNNER_CYCLE_SECOND);
    b_FirstDefrostingEntered = false;
    u16_FridgeRuntimeStart = Get_MinuteCount();
    u16_FridgeRuntimeMinute = 0;
    turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None;
}

void FridgeRunner_Exit(void)
{
    Stop_PollTimer();
    CoolingCycle_Exit();
    Defrosting_Exit();
    // runningState = (RunningState_t)eRunning_CoolingCycle;
}

RunningState_t Get_RunningState(void)
{
    return (runningState);
}

EnterDefrostingState_t Get_EnterDefrostingState(void)
{
    return (enterDefrostingState);
}

uint8_t Get_SavedDefrostType(uint8_t **p_defrost_type_buff)
{
    *p_defrost_type_buff = (uint8_t *)(st_SaveDefrostType.ary_DefrostTypeBuff);

    return (st_SaveDefrostType.u8_SavedCount);
}

void Clear_FridgeTotalOnTimeMinute(void)
{
    u16_FridgeRuntimeStart = Get_MinuteCount();
    u16_FridgeRuntimeMinute = 0;
}

uint16_t Get_FridgeTotalOnTimeMinute(void)
{
    return (u16_FridgeRuntimeMinute);
}

bool Get_PowerOnDelayRefVarCoolingState(void)
{
    return (b_PowerOnDelayRefVarCooling);
}

uint16_t Get_DeforstExitTemp(void)
{
    return (u16_DeforstExitTemp);
}

void Set_CoolingEntryMode(CoolingEntryMode_t mode)
{
    coolingEntryMode = mode;
}

void Force_EnterEnergyConsumptionModeState(void)
{
    uint16_t room_temp = Get_SensorValue((uint8_t)SENSOR_ROOM);

    b_EnergyConsumptionMode = true;
    b_EnergyModeFirstDeforst = false;
    u16_RoomTempBackup = room_temp;
    u16_EneryExitRoomTempBackup = room_temp;
    u16_ExitEnergyModeTimeStart = Get_MinuteCount();
    u16_EnergyModeDefrostTimeStart = Get_MinuteCount();
    u8_RefDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF_LEFT);
    u8_RefDoorCounter += Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF_RIGHT);
}

bool Get_EnergyConsumptionModeState(void)
{
    return b_EnergyConsumptionMode;
}

bool Get_CondensationModeState(void)
{
    return b_CondensationMode;
}

uint16_t Get_TotalPower(void)
{
    return u16_TotalPower;
}

uint32_t Get_ElectricEnergy(void)
{
    return u32_ElectricEnergy;
}
