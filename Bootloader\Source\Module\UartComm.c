/*!
 * @file
 * @brief This module covers the complete fan functionality.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <string.h>
#include "UartComm.h"
#include "InverterUsart.h"
#include "Crc16_CCITT_FALSE.h"
#include "syslog.h"

static void Start_UartCommRece(UartComm_st *uart)
{
    uart->u8_ReceDataState = 0;
    uart->u8_ReceCount = 0;
    uart->u8_ReceLength = 0;
    uart->u8_ReceTimeOutCount = 0;
    uart->u16_ReceCrcValue = U16_CRC_INITIAL_VALUE;
}

static void Process_UartCommSendData(UartComm_st *uart)
{
    uint8_t u8_send_data = 0;

    switch(uart->u8_SendDataState)
    {
        case(uint8_t)FIREWARE_FRAME_STATE_HEAD:
            uart->u8_SendCount = 0;
            uart->sendPos = &uart->sendPacket.head;
            u8_send_data = *uart->sendPos++;
            uart->uart_ops.sendOneData(u8_send_data);
            uart->u8_SendDataState = (uint8_t)FIREWARE_FRAME_STATE_SRC;
            break;
        case(uint8_t)FIREWARE_FRAME_STATE_SRC:
        case(uint8_t)FIREWARE_FRAME_STATE_DEST:
        case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE1:
        case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE2:
        case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE3:
            u8_send_data = *uart->sendPos++;
            uart->uart_ops.sendOneData(u8_send_data);
            uart->u8_SendDataState++;
            break;
        case(uint8_t)FIREWARE_FRAME_STATE_LENGTH:
            uart->u8_SendLength = *uart->sendPos;
            u8_send_data = *uart->sendPos++;
            uart->uart_ops.sendOneData(u8_send_data);
            uart->u8_SendDataState = (uint8_t)FIREWARE_FRAME_STATE_DATA;
            break;
        case(uint8_t)FIREWARE_FRAME_STATE_DATA:
            uart->u8_SendCount++;
            if(uart->u8_SendCount == uart->u8_SendLength + 1)
            {
                uart->sendPos = &uart->sendPacket.crch;
            }
            u8_send_data = *uart->sendPos++;
            uart->uart_ops.sendOneData(u8_send_data);
            if(uart->u8_SendCount >= (uart->u8_SendLength + 3))
            {
                uart->u8_SendDataState = (uint8_t)FIREWARE_FRAME_STATE_OVER;
                uart->f_FrameSending = false;
                uart->f_FrameReceiving = true;
                Start_UartCommRece(uart);
                uart->u8_SendTimeOutCount = 0;
                uart->uart_ops.enableTx(false);
                uart->uart_ops.enableRx(true);
            }
            break;
        default:
            uart->f_FrameSending = false;
            uart->f_FrameReceiving = true;
            Start_UartCommRece(uart);
            uart->u8_SendTimeOutCount = 0;
            uart->uart_ops.enableTx(false);
            uart->uart_ops.enableRx(true);
            break;
    }
}

static void Process_UartCommReceData(UartComm_st *uart, const uint8_t u8_rece_data)
{
    uint16_t crc16_value = 0;

    switch(uart->u8_ReceDataState)
    {
        case(uint8_t)FIREWARE_FRAME_STATE_HEAD:
            if(FIREWARE_FRAME_HEAD == u8_rece_data)
            {
                uart->recvPos = &uart->recvPacket.head;
                *uart->recvPos++ = u8_rece_data;
                uart->u16_ReceCrcValue =
                    Cal_CRC_SingleData(uart->u16_ReceCrcValue, u8_rece_data);
                uart->u8_ReceCount = 0;
                uart->u8_ReceDataState = (uint8_t)FIREWARE_FRAME_STATE_SRC;
            }
            break;
        case(uint8_t)FIREWARE_FRAME_STATE_SRC:
        case(uint8_t)FIREWARE_FRAME_STATE_DEST:
        case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE1:
        case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE2:
        case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE3:
            *uart->recvPos++ = u8_rece_data;
            uart->u16_ReceCrcValue =
                Cal_CRC_SingleData(uart->u16_ReceCrcValue, u8_rece_data);
            uart->u8_ReceDataState++;
            break;
        case(uint8_t)FIREWARE_FRAME_STATE_LENGTH:
            *uart->recvPos++ = u8_rece_data;
            uart->u16_ReceCrcValue =
                Cal_CRC_SingleData(uart->u16_ReceCrcValue, u8_rece_data);
            uart->u8_ReceLength = u8_rece_data;
            uart->u8_ReceDataState = (uint8_t)FIREWARE_FRAME_STATE_DATA;
            break;
        case(uint8_t)FIREWARE_FRAME_STATE_DATA:
            if(uart->u8_ReceLength > FIREWARE_FRAME_DATA_MAX)
            {
                return;
            }
            if(uart->u8_ReceCount < uart->u8_ReceLength)
            {
                uart->u16_ReceCrcValue =
                    Cal_CRC_SingleData(uart->u16_ReceCrcValue, u8_rece_data);
            }
            if(uart->u8_ReceCount == uart->u8_ReceLength)
            {
                uart->recvPos = &uart->recvPacket.crch;
            }
            uart->u8_ReceCount++;
            *uart->recvPos++ = u8_rece_data;
            if(uart->u8_ReceCount == (uart->u8_ReceLength + 3))
            {
                crc16_value = uart->recvPacket.crch << 8 | uart->recvPacket.crcl;

                if(crc16_value == uart->u16_ReceCrcValue &&
                    uart->recvPacket.end == FIREWARE_FRAME_END)
                {
                    uart->u8_ReceDataState = (uint8_t)FIREWARE_FRAME_STATE_OVER;
                    uart->b_ReceiveError = false;
                    uart->u16_RecvDataErrorCount = 0;
                    uart->f_FrameReceiving = false;
                    uart->u8_ReceTimeOutCount = 0;
                    uart->u8_SendTimeOutCount = 0;
                    uart->uart_ops.enableRx(false);
                    uart->f_SendIE = true;
                }
            }
            break;
        default:
            uart->u8_ReceTimeOutCount = 0;
            break;
    }
}

void Handle_UartCommSendData(UartComm_st *uart)
{
    if(false == uart->f_FrameSending)
    {
        uart->u8_SendTimeOutCount = 0;
    }
    else
    {
        Process_UartCommSendData(uart);
    }
}

void Handle_UartCommReceData(UartComm_st *uart, const uint8_t u8_rece_data)
{
    if(true == uart->f_FrameReceiving)
    {
        Process_UartCommReceData(uart, u8_rece_data);
    }
}

int32_t UartCommTransmit(fireware_comm_st *fw)
{
    UartComm_st *uart;

    if(NULL == fw)
    {
        return FIREWARE_FRAME_SENDERROR;
    }

    uart = (UartComm_st *)CONTAINER_OF(UartComm_st, fw, fw);

    if(true == uart->f_SendIE && false == uart->f_FrameSending)
    {
        memcpy(&uart->sendPacket, &fw->sendPacket, sizeof(fireware_frame_st));
        uart->f_FrameSending = true;
        uart->f_FrameReceiving = false;
        uart->f_SendIE = false;
        uart->u8_SendCount = 0;
        uart->u8_SendDataState = 0;
        uart->u8_ReceDataState = 0;
        uart->u8_SendTimeOutCount = 0;
        uart->f_FrameSendTimeout = false;
        uart->f_FrameRecvTimeout = false;
        uart->uart_ops.enableRx(false);
        uart->uart_ops.enableTx(true);

        while(!uart->f_FrameSendTimeout && uart->u8_SendDataState != FIREWARE_FRAME_STATE_OVER)
        {
            __asm("WFI");
        }

        if(uart->f_FrameSendTimeout)
        {
            return FIREWARE_FRAME_TIMEOUT;
        }
        return FIREWARE_FRAME_SUCCESS;
    }
    return FIREWARE_FRAME_SENDBUSY;
}

int32_t UartCommReceive(fireware_comm_st *fw)
{
    UartComm_st *uart;

    if(NULL == fw)
    {
        return FIREWARE_FRAME_SENDERROR;
    }

    uart = (UartComm_st *)CONTAINER_OF(UartComm_st, fw, fw);

    while(!uart->f_FrameRecvTimeout && uart->u8_ReceDataState != FIREWARE_FRAME_STATE_OVER)
    {
        __asm("WFI");
    }

    if(uart->f_FrameRecvTimeout)
    {
        return FIREWARE_FRAME_TIMEOUT;
    }
    memcpy(&fw->recvPacket, &uart->recvPacket, sizeof(fireware_frame_st));
    return FIREWARE_FRAME_SUCCESS;
}

void Handle_UartCommOverTime(UartComm_st *uart)
{
    if(uart->u8_SendTimeOutCount < 0xFF)
    {
        if(true == uart->f_FrameSending)
        {
            uart->u8_SendTimeOutCount++;
        }
        if(uart->u8_SendTimeOut <= uart->u8_SendTimeOutCount)
        {
            uart->u8_SendTimeOutCount = 0;
            uart->f_FrameSending = false;
            uart->f_FrameSendTimeout = true;
            uart->f_SendIE = true;
            uart->uart_ops.enableTx(false);
        }
    }

    if(uart->u8_ReceTimeOutCount < 0xFF)
    {
        if(true == uart->f_FrameReceiving)
        {
            uart->u8_ReceTimeOutCount++;
        }

        if(uart->u8_ReceTimeOut <= uart->u8_ReceTimeOutCount)
        {
            uart->u8_ReceTimeOutCount = 0;
            uart->f_FrameRecvTimeout = true;
            uart->u16_RecvDataErrorCount++;
            if(uart->u16_RecvDataErrorCount >= uart->u16_RecvDataError)
            {
                uart->b_ReceiveError = true;
            }
            uart->f_FrameReceiving = false;
            uart->f_SendIE = true;
            uart->uart_ops.enableRx(false);
        }
    }
}

void UartCommInit(UartComm_st *uart, uint8_t send_timeout, uint8_t recv_timeout, uint16_t recv_dataerror)
{
    uart->u8_ReceTimeOut = recv_timeout;
    uart->u8_SendTimeOut = send_timeout;
    uart->u16_RecvDataError = recv_dataerror;
    uart->f_SendIE = true;
    uart->f_FrameSending = false;
    uart->f_FrameReceiving = false;
    uart->u8_SendTimeOutCount = 0;
    uart->u8_ReceTimeOutCount = 0;
}
