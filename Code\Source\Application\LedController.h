/*!
 * @file
 * @brief This file defines public constants, types and functions for the led controller.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef LED_CONTROLLER_H
#define LED_CONTROLLER_H

#include <stdint.h>
#include <stdbool.h>
#include "DisplayInterface.h"
#include "Parameter_TemperatureZone.h"

#define U8_FLASH_COUNT_MAX (uint8_t)50
#define U8_LOCK_FLASH_COUNT_MAX (uint8_t)12

#define CON_Disp0 (uint8_t)0
#define CON_Disp1 (uint8_t)1
#define CON_Disp2 (uint8_t)2
#define CON_Disp3 (uint8_t)3
#define CON_Disp4 (uint8_t)4
#define CON_Disp5 (uint8_t)5
#define CON_Disp6 (uint8_t)6
#define CON_Disp7 (uint8_t)7
#define CON_Disp8 (uint8_t)8
#define CON_Disp9 (uint8_t)9
#define CON_DispNC (uint8_t)10
#define CON_DispT (uint8_t)11
#define CON_DispF (uint8_t)12
#define CON_DispL (uint8_t)13
#define CON_DispH (uint8_t)14
#define CON_DispE (uint8_t)15
#define CON_DispC (uint8_t)16
#define CON_DispD (uint8_t)17
#define CON_DispA (uint8_t)18
#define CON_DispR (uint8_t)19
#define CON_DispI (uint8_t)20
#define CON_DispO (uint8_t)21
#define CON_Disptt (uint8_t)22
#define CON_DispN (uint8_t)23
#define CON_DispDispVer (uint8_t)24
#define CON_DispMainVer (uint8_t)25
#define CON_DispSlaveVer (uint8_t)26
#define CON_DispSubSign (uint8_t)27
#define CON_DispB (uint8_t)28
#define CON_DispU (uint8_t)29
#define CON_DispP (uint8_t)30
#define CON_MaxDispNum (uint8_t)31

typedef struct
{
    bool b_C;
    bool b_Minus;
    uint8_t u8_High;
    uint8_t u8_Low;
} ZoneDisp_st;

enum
{
    eLockLed_BitIndex = 0,
    eFuzzyLed_BitIndex,
    eTurboCoolLed_BitIndex,
    eTurboFreezeLed_BitIndex,
    eWiFiLed_BitIndex,
    eTreasureLed_BitIndex,
    eBabyLed_BitIndex,
    eZeroLed_BitIndex
};
typedef uint8_t DisplayByte3_t;

enum
{
    eDispRef_Zone = 0,
    eDispFrz_Zone
};
typedef uint8_t DisplayZone_t;

void Set_AllLedsState(bool state);
void Init_DisplayStatus(void);
uint8_t Get_DisplayByte1Value(void);
uint8_t Get_DisplayByte2Value(void);
uint8_t Get_DisplayByte3Value(void);
void Set_NumberDisplay(DisplayZone_t zone, bool flashState, uint8_t numberIndex);
void Set_ModeDisplay(bool flashState, UserMode_t mode);
void Set_RefVarDisplay(bool flashState, RefVarSet_t setpoint);
void Set_LockDisplay(uint8_t flashCount);
void Update_DisplayStatus(void);
void Set_FactoryResetDisplay(void);
void Set_ShowRoomDisplay(void);
void Set_ForceHeaterDisplay(bool state);
void Set_PcbTestDisplay(void);
void Set_TestModeDisplay(bool flashState);
void Set_QueryModeDisplay(void);
void Set_AdjustParaDisplay(uint8_t display_value, bool display_state);
bool Get_WifiConnectState(void);
void Refresh_Display(void);

#endif
