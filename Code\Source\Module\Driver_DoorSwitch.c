/*!
 * @file
 * @brief door switch detection and alarm.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Driver_DoorSwitch.h"
#include "Adpt_GPIO.h"
#include "Driver_DoubleDamper.h"
#include "Driver_GradualLamp.h"
#include "IO_Device.h"
#include "DisplayInterface.h"
#include "Core_Types.h"
#include "ResolverDevice.h"
#include "DisplayUsart.h"
#include "SystemManager.h"

static bool b_RefDoorOpen = false;
static bool b_FrzDoorOpen = false;
static bool b_RefLeftDoorOpenLamp = false;
static bool b_RefRightDoorOpenLamp = false;
static bool b_FrzLeftDoorOpenLamp = false;
static bool b_FrzRightDoorOpenLamp = false;
static uint8_t u8_FrzFanDelayTime = 0;
static DoorState_st ary_DoorState[(uint8_t)DOOR_MAX_NUMBER];

static void Judge_RefLeftDoorState(void);
static void Judge_RefRightDoorState(void);
static void Judge_FrzLeftDoorState(void);
static void Judge_FrzRightDoorState(void);
static void DoorSwitch_CtrlDisplay(void);
static void DoorSwitch_CtrlLamp(void);
static void Handle_RefDoorState(void);
static void Handle_FrzDoorState(void);
static void Judge_AllDoorState(void);
static void Cal_AllDoorsTimeSecond(void);
static void Judge_AllDoorsAlarmAndSwitchError(void);
static void Deal_DoorOpenAlarm(void);
static void Handle_DoorOpen(void);

#ifdef BCD508WGBI
static DoorSwitchState_st ary_DoorSwitchState[(uint8_t)DOOR_REF] = {
    { Judge_RefLeftDoorState, Handle_RefDoorState, IO_LEVEL_HIGH },
    { Judge_RefRightDoorState, Handle_RefDoorState, IO_LEVEL_HIGH },
    { Judge_FrzLeftDoorState, Handle_FrzDoorState, IO_LEVEL_HIGH },
    { Judge_FrzRightDoorState, Handle_FrzDoorState, IO_LEVEL_HIGH }
};
#elif defined(BCD508WMBI) || defined(BCD508WFMBI)
static DoorSwitchState_st ary_DoorSwitchState[(uint8_t)DOOR_REF] = {
    { Judge_RefLeftDoorState, Handle_RefDoorState, IO_LEVEL_HIGH },
    { Judge_RefRightDoorState, Handle_RefDoorState, IO_LEVEL_HIGH },
    { Judge_FrzLeftDoorState, Handle_FrzDoorState, IO_LEVEL_HIGH },
    { Judge_FrzRightDoorState, Handle_FrzDoorState, IO_LEVEL_HIGH }
};
#endif

static void Judge_RefLeftDoorState(void)
{
    // if((uint8_t)IO_REF_LEFT_DOOR_IN == ary_DoorSwitchState[(uint8_t)DOOR_REF_LEFT].u8_DoorOpenSwitchState)
    if(0)
    {
        ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorSwitchState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorSwitchState = DOOR_CLOSE;
    }
}

static void Judge_RefRightDoorState(void)
{
    if((uint8_t)IO_REF_RIGHT_DOOR_IN == ary_DoorSwitchState[(uint8_t)DOOR_REF_RIGHT].u8_DoorOpenSwitchState)
    {
        ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorSwitchState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorSwitchState = DOOR_CLOSE;
    }
}

static void Judge_FrzLeftDoorState(void)
{
    if((uint8_t)IO_FRZ_LEFT_DOOR_IN == ary_DoorSwitchState[(uint8_t)DOOR_FRZ_LEFT].u8_DoorOpenSwitchState)
    {
        ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorSwitchState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorSwitchState = DOOR_CLOSE;
    }
}

static void Judge_FrzRightDoorState(void)
{
    if((uint8_t)IO_FRZ_RIGHT_DOOR_IN == ary_DoorSwitchState[(uint8_t)DOOR_FRZ_RIGHT].u8_DoorOpenSwitchState)
    {
        ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorSwitchState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorSwitchState = DOOR_CLOSE;
    }
}

static void Handle_RefDoorState(void)
{
    if((DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorState) ||
        (DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorState))
    {
        ary_DoorState[(uint8_t)DOOR_REF].f_DoorSwitchState = DOOR_OPEN;
        ary_DoorState[(uint8_t)DOOR_REF].f_DoorState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_REF].f_DoorSwitchState = DOOR_CLOSE;
        ary_DoorState[(uint8_t)DOOR_REF].f_DoorState = DOOR_CLOSE;
    }
}

static void Handle_FrzDoorState(void)
{
    if((DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorState) ||
        (DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorState))
    {
        ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorSwitchState = DOOR_OPEN;
        ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorSwitchState = DOOR_CLOSE;
        ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorState = DOOR_CLOSE;
    }
}

static void Judge_AllDoorState(void)
{
    if((DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_REF].f_DoorState) ||
        (DOOR_OPEN == ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorState))
    {
        ary_DoorState[(uint8_t)DOOR_ALL].f_DoorSwitchState = DOOR_OPEN;

        ary_DoorState[(uint8_t)DOOR_ALL].f_DoorState = DOOR_OPEN;
    }
    else
    {
        ary_DoorState[(uint8_t)DOOR_ALL].f_DoorSwitchState = DOOR_CLOSE;

        ary_DoorState[(uint8_t)DOOR_ALL].f_DoorState = DOOR_CLOSE;
    }
}

void Update_AllDoorsSwitchState(void)
{
    uint8_t u8_index = 0;
    DoorState_st *p_door_state = (DoorState_st *)NULL;
    DoorSwitchState_st *p_door_switch_state = (DoorSwitchState_st *)NULL;

    p_door_state = ary_DoorState;
    p_door_switch_state = ary_DoorSwitchState;

    for(u8_index = 0; u8_index < (uint8_t)DOOR_REF; u8_index++)
    {
        if(NULL == p_door_switch_state->p_JudgeDoorSwitchState)
        {
            p_door_state->f_DoorSwitchState = DOOR_CLOSE;
        }
        else
        {
            p_door_switch_state->p_JudgeDoorSwitchState();
        }

        if(p_door_state->f_DoorState == p_door_state->f_DoorSwitchState)
        {
            p_door_state->u8_JudgeDoorState10MsCounter = 0;
        }
        else
        {
            p_door_state->u8_JudgeDoorState10MsCounter++;
            if(p_door_state->u8_JudgeDoorState10MsCounter >= U8_ANTI_SHAKE_TIMER)
            {
                if(DOOR_CLOSE == p_door_state->f_DoorState)
                {
                    // 开门
                    p_door_state->f_DoorState = DOOR_OPEN;
                    p_door_state->f_DoorOpenClose = false;
                }
                else
                {
                    // 关门
                    p_door_state->f_DoorState = DOOR_CLOSE;
                    p_door_state->f_DoorOpenClose = true;
                    if(p_door_state->u8_DoorOpenCloseTotalCounter < U8_MAX_UINT8)
                    {
                        p_door_state->u8_DoorOpenCloseTotalCounter++;
                    }
                }

                if(p_door_switch_state->p_HandleDoorSwitchState != NULL)
                {
                    p_door_switch_state->p_HandleDoorSwitchState();
                }
            }
        }

        p_door_state++;
        p_door_switch_state++;
    }

    Judge_AllDoorState();
    DoorSwitch_CtrlDisplay();
    DoorSwitch_CtrlLamp();
}

static void Cal_AllDoorsTimeSecond(void)
{
    uint8_t u8_index = 0;
    DoorState_st *p_door_state = (DoorState_st *)NULL;

    p_door_state = ary_DoorState;

    for(u8_index = 0; u8_index < (uint8_t)DOOR_MAX_NUMBER; u8_index++)
    {
        if(DOOR_OPEN == p_door_state->f_DoorState)
        {
            // 开门
            p_door_state->u16_DoorCloseSecondCounter = 0;

            if(p_door_state->u16_DoorOpenSecondCounter < U16_MAX_UINT16)
            {
                p_door_state->u16_DoorOpenSecondCounter++;
            }

            if(p_door_state->u16_DoorOpenTotalSecondCounter < U16_MAX_UINT16)
            {
                p_door_state->u16_DoorOpenTotalSecondCounter++;
            }
        }
        else
        {
            // 关门
            p_door_state->u16_DoorOpenSecondCounter = 0;

            if(p_door_state->u16_DoorCloseSecondCounter < U16_MAX_UINT16)
            {
                p_door_state->u16_DoorCloseSecondCounter++;
            }
        }

        p_door_state++;
    }
}

static void Judge_AllDoorsAlarmAndSwitchError(void)
{
    uint8_t u8_index = 0;
    DoorState_st *p_door_state = (DoorState_st *)NULL;

    p_door_state = ary_DoorState;

    for(u8_index = 0; u8_index < (uint8_t)DOOR_MAX_NUMBER; u8_index++)
    {
        if(p_door_state->u16_DoorOpenSecondCounter < U16_OPEN_DOOR_ALARM_DELAY_TIME_SECOND)
        {
            p_door_state->f_DoorOpenAlarm = false;
            p_door_state->f_DoorOpenLamp = false;
            p_door_state->f_DoorDefaultClose = false;
            p_door_state->f_DoorSwitchError = false;
        }
        else if(p_door_state->u16_DoorOpenSecondCounter < U16_DOOR_OPEN_CLOSE_LAMP_TIME_SECOND)
        {
            p_door_state->f_DoorOpenAlarm = true;
            p_door_state->f_DoorOpenLamp = false;
            p_door_state->f_DoorDefaultClose = false;
            p_door_state->f_DoorSwitchError = false;
        }
        else if(p_door_state->u16_DoorOpenSecondCounter < U16_DOOR_OPEN_DEFAULT_CLOSE_TIME_SECOND)
        {
            p_door_state->f_DoorOpenAlarm = true;
            p_door_state->f_DoorOpenLamp = true;
            p_door_state->f_DoorDefaultClose = false;
            p_door_state->f_DoorSwitchError = false;
        }
        else if(p_door_state->u16_DoorOpenSecondCounter < U16_DOOR_OPEN_SWITCH_ERROR_TIME_SECOND)
        {
            p_door_state->f_DoorOpenAlarm = true;
            p_door_state->f_DoorOpenLamp = true;
            p_door_state->f_DoorDefaultClose = true;
            p_door_state->f_DoorSwitchError = false;
        }
        else
        {
            p_door_state->f_DoorOpenAlarm = true;
            p_door_state->f_DoorOpenLamp = true;
            p_door_state->f_DoorDefaultClose = true;
            p_door_state->f_DoorSwitchError = true;
        }

        p_door_state++;
    }
}

static void Deal_DoorOpenAlarm(void)
{
    static uint8_t u8_door_open_alarm_interval_counter_second = 0;
    bool b_door_open_alarm = false;
    bool b_door_open_default_close = false;
    uint8_t u8_index = 0;
    DoorState_st *p_door_state = (DoorState_st *)NULL;

    p_door_state = ary_DoorState;

    for(u8_index = 0; u8_index < (uint8_t)DOOR_REF; u8_index++)
    {
        b_door_open_alarm |= (bool)(p_door_state->f_DoorOpenAlarm);
        b_door_open_default_close |= (bool)(p_door_state->f_DoorDefaultClose);
        p_door_state++;
    }

    if(false == b_door_open_alarm)
    {
        u8_door_open_alarm_interval_counter_second = 0;
    }
    else
    {
        if(0 == u8_door_open_alarm_interval_counter_second)
        {
            if(true == b_door_open_default_close)
            {
                u8_door_open_alarm_interval_counter_second = U16_OPEN_DOOR_ALARM_MIN_INTERVAL_TIME_SECOND;
            }
            else
            {
                u8_door_open_alarm_interval_counter_second = U16_OPEN_DOOR_ALARM_MAX_INTERVAL_TIME_SECOND;
            }
            Set_MusicType((MusicType_t)eDoorAlarm_Tone);
        }

        u8_door_open_alarm_interval_counter_second--;
    }
}

static void DoorSwitch_CtrlDisplay(void)
{
    if((true == ary_DoorState[(uint8_t)DOOR_REF].f_DoorState) ||
        (true == ary_DoorState[(uint8_t)DOOR_REF].f_DoorOpenAlarm))
    {
        Wakeup_UserInterface();
    }

    if((true == ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorState) ||
        (true == ary_DoorState[(uint8_t)DOOR_FRZ].f_DoorOpenAlarm))
    {
        Wakeup_UserInterface();
    }
}

static void DoorSwitch_CtrlLamp(void)
{
    if(((true == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorOpenLamp)) ||
        ((true == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorState) &&
            (false == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorOpenLamp)))
    {
        Set_GradualLampState(REF_SURFACE_LAMP, true);
    }
    else
    {
        Set_GradualLampState(REF_SURFACE_LAMP, false);
    }

    if(((true == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorOpenLamp)))
    {
        if(false == b_RefLeftDoorOpenLamp)
        {
            b_RefLeftDoorOpenLamp = true;
        }
    }
    else
    {
        if(true == b_RefLeftDoorOpenLamp)
        {
            b_RefLeftDoorOpenLamp = false;
        }
    }

    if(((true == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorOpenLamp)))
    {
        if(false == b_RefRightDoorOpenLamp)
        {
            b_RefRightDoorOpenLamp = true;
        }
    }
    else
    {
        if(true == b_RefRightDoorOpenLamp)
        {
            b_RefRightDoorOpenLamp = false;
        }
    }

    if(((true == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorOpenLamp)))
    {
        if(false == b_FrzLeftDoorOpenLamp)
        {
            b_FrzLeftDoorOpenLamp = true;
        }
    }
    else
    {
        if(true == b_FrzLeftDoorOpenLamp)
        {
            b_FrzLeftDoorOpenLamp = false;
        }
    }

    if(((true == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorOpenLamp)))
    {
        if(false == b_FrzRightDoorOpenLamp)
        {
            b_FrzRightDoorOpenLamp = true;
        }
    }
    else
    {
        if(true == b_FrzRightDoorOpenLamp)
        {
            b_FrzRightDoorOpenLamp = false;
        }
    }

    Ctrl_FrzLeftLamp(b_RefRightDoorOpenLamp, b_FrzLeftDoorOpenLamp);
    Ctrl_FrzRightLamp(b_RefLeftDoorOpenLamp, b_FrzRightDoorOpenLamp);
}

static void DoorSwitch_CtrlDoubleDamper(void)
{
    if((true == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorDefaultClose) ||
        (true == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorDefaultClose) ||
        (true == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorDefaultClose) ||
        (true == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorDefaultClose))
    {
        Set_RefDoorState_DoubleDamper(false);
        Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefDamper, (int8_t)DS_DontCare);
        Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefVarDamper, (int8_t)DS_DontCare);
    }
    else if((true == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorOpenAlarm) ||
        (true == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorOpenAlarm) ||
        (true == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorOpenAlarm) ||
        (true == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorOpenAlarm))
    {
        Set_RefDoorState_DoubleDamper(false);
        Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);
        Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLCLOSE);
    }
    else if((true == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorState) ||
        (true == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorState) ||
        (true == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorState) ||
        (true == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorState))
    {
        Set_RefDoorState_DoubleDamper(true);
        Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefDamper, (int8_t)DS_DontCare);
        Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefVarDamper, (int8_t)DS_DontCare);
    }
    else
    {
        Set_RefDoorState_DoubleDamper(false);
        Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefDamper, (int8_t)DS_DontCare);
        Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_RefVarDamper, (int8_t)DS_DontCare);
    }
}

static void DoorSwitch_CtrlFrzFan(void)
{
    if(((true == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_REF_LEFT].f_DoorDefaultClose)) ||
        ((true == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorState) &&
            (false == ary_DoorState[(uint8_t)DOOR_REF_RIGHT].f_DoorDefaultClose)))
    {
        if(false == b_RefDoorOpen)
        {
            b_RefDoorOpen = true;
            Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_FrzFan, (int8_t)DS_Off);
        }
    }
    else
    {
        if(true == b_RefDoorOpen)
        {
            b_RefDoorOpen = false;
        }
    }

    if(((true == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorState) &&
           (false == ary_DoorState[(uint8_t)DOOR_FRZ_LEFT].f_DoorDefaultClose)) ||
        ((true == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorState) &&
            (false == ary_DoorState[(uint8_t)DOOR_FRZ_RIGHT].f_DoorDefaultClose)))
    {
        if(false == b_FrzDoorOpen)
        {
            b_FrzDoorOpen = true;
        }
    }
    else
    {
        if(true == b_FrzDoorOpen)
        {
            b_FrzDoorOpen = false;
        }
    }

    if((true == b_RefDoorOpen) || (true == b_FrzDoorOpen))
    {
        u8_FrzFanDelayTime = U8_FAN_DELAY_TIMER_AFTER_DOOR_CLOSE;
    }
    else
    {
        if(u8_FrzFanDelayTime > 0)
        {
            u8_FrzFanDelayTime--;
            if(0 == u8_FrzFanDelayTime)
            {
                Vote_DeviceStatus((uint8_t)FSM_OpenDoorControl, (uint8_t)DEVICE_FrzFan, (int8_t)DS_DontCare);
            }
        }
    }
}

static void Handle_DoorOpen(void)
{
    DoorSwitch_CtrlDoubleDamper();
    DoorSwitch_CtrlFrzFan();
}

void Handle_AllDoorsState(void)
{
    Cal_AllDoorsTimeSecond();
    Judge_AllDoorsAlarmAndSwitchError();
    Deal_DoorOpenAlarm();
    Handle_DoorOpen();
}

uint16_t Get_DoorOpenTimeSecond(DoorTypeId_t typeId)
{
    uint16_t u16_doorOpenTimeSecond = 0;

    if(typeId < (DoorTypeId_t)DOOR_MAX_NUMBER)
    {
        u16_doorOpenTimeSecond = ary_DoorState[typeId].u16_DoorOpenTotalSecondCounter;
    }

    return u16_doorOpenTimeSecond;
}

uint8_t Get_DoorOpenCloseCounter(DoorTypeId_t typeId)
{
    uint8_t u8_doorOpenCloseCounter = 0;

    if(typeId < (DoorTypeId_t)DOOR_MAX_NUMBER)
    {
        u8_doorOpenCloseCounter = ary_DoorState[typeId].u8_DoorOpenCloseTotalCounter;
    }

    return u8_doorOpenCloseCounter;
}

void Clear_DoorOpenTimeSecond(DoorTypeId_t typeId)
{
    uint8_t u8_index = 0;
    uint8_t u8_indexend = 0;

    if(typeId < (DoorTypeId_t)DOOR_MAX_NUMBER)
    {
        ary_DoorState[typeId].u16_DoorOpenTotalSecondCounter = 0;

        if(DOOR_REF == typeId)
        {
            u8_index = (uint8_t)DOOR_REF_LEFT;
            u8_indexend = (uint8_t)DOOR_REF_RIGHT;
        }
        else if(DOOR_FRZ == typeId)
        {
            u8_index = (uint8_t)DOOR_FRZ_LEFT;
            u8_indexend = (uint8_t)DOOR_FRZ_RIGHT;
        }
        else if(DOOR_ALL == typeId)
        {
            u8_index = (uint8_t)DOOR_REF_LEFT;
            u8_indexend = (uint8_t)DOOR_ALL;
        }

        if(u8_index != u8_indexend)
        {
            for(; u8_index <= u8_indexend; u8_index++)
            {
                ary_DoorState[u8_index].u16_DoorOpenTotalSecondCounter = 0;
            }
        }
    }
}

bool Get_DoorSwitchState(DoorTypeId_t typeId)
{
    bool b_doorSwitchState = false;

    if(typeId < DOOR_MAX_NUMBER)
    {
        if(true == ary_DoorState[typeId].f_DoorState)
        {
            b_doorSwitchState = true;
        }
    }

    return b_doorSwitchState;
}

bool Get_DoorSwitchAlarmState(DoorTypeId_t typeId)
{
    bool b_doorSwitchErrorState = false;

    if(typeId < DOOR_MAX_NUMBER)
    {
        if(true == ary_DoorState[typeId].f_DoorOpenAlarm)
        {
            b_doorSwitchErrorState = true;
        }
    }

    return b_doorSwitchErrorState;
}

bool Get_DoorSwitchErrorState(DoorTypeId_t typeId)
{
    bool b_doorSwitchErrorState = false;

    if(typeId < DOOR_MAX_NUMBER)
    {
        if(true == ary_DoorState[typeId].f_DoorSwitchError)
        {
            b_doorSwitchErrorState = true;
        }
    }

    return b_doorSwitchErrorState;
}

uint8_t Get_DoorSwitchflagState(DoorTypeId_t typeId)
{
    uint8_t u8_doorSwitchFlagState = 0;

    if(typeId < (DoorTypeId_t)DOOR_MAX_NUMBER)
    {
        BIT_WRITE(u8_doorSwitchFlagState, 0, ary_DoorState[typeId].f_DoorSwitchState);
        BIT_WRITE(u8_doorSwitchFlagState, 1, ary_DoorState[typeId].f_DoorState);
        BIT_WRITE(u8_doorSwitchFlagState, 2, ary_DoorState[typeId].f_DoorOpenClose);
        BIT_WRITE(u8_doorSwitchFlagState, 3, ary_DoorState[typeId].f_DoorOpenAlarm);
        BIT_WRITE(u8_doorSwitchFlagState, 4, ary_DoorState[typeId].f_DoorDefaultClose);
        BIT_WRITE(u8_doorSwitchFlagState, 5, ary_DoorState[typeId].f_DoorSwitchError);
    }

    return u8_doorSwitchFlagState;
}
