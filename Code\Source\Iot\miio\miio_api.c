#include "Iot_SpecHandler.h"
#include "miio_define.h"

#include "uart\miio_uart.h"
#include "arch_uart.h"
#include "Iotlist.h"
#include "util.h"
#include "miio_api.h"
#include "user_app_func.h"
#include "arch_os.h"
#include "string.h"
#include "IotUsr.h"
#include "Driver_Flash.h"
#include "FactoryMode.h"
#include "SystemManager.h"

static unsigned long wifi_poll_interval = 0;
static unsigned long last_mcu_to_wifi_cmd_interval = 0;
static char shake_cmd[128];

wifi_cmd_status_t g_cmd_status = { WIFI_CMD_NONE, NULL, false };
mcu_to_wifi_comm_status_e g_comm_status = MCU_TO_WIFI_COMM_IDLE;
wifi_shakehand_step g_wifi_sh_step = WIFI_SHAKEHAND_NONE;

int execute_wifi_cmd_async(wifi_command_e cmd, callbackCommandResult callback)
{
    if(g_cmd_status.command == WIFI_CMD_NONE)
    {
        g_cmd_status.command = cmd;
        g_cmd_status.commandCallback = callback;
        g_cmd_status.is_execute = false;

        return 0;
    }

    return -1;
}

static void set_net_state_callback(const char *pValue, char result)
{
    if(result)
    {
        set_network_state(pValue);
    }
    else
    {
        APP_LOG("got network state failed!");
    }
}

static void retrieve_sys_time_callback(const char *pValue, char result)
{
    update_current_date(pValue);
}

static void get_wifi_arch_platform_callback(const char *pValue, char result)
{
    if(result)
    {
        set_wifi_arch_platform_state(pValue);
    }
    else
    {
        APP_LOG("get arch platform failed!");
    }
}

static bool do_report_properties_changed(char *pResult)
{
    if(g_comm_status == MCU_TO_WIFI_COMM_IDLE)
    {
        if(report_properties_changed_to_iot(pResult))
        {
            g_comm_status = MCU_TO_WIFI_COMM_PROP_CHANGED;
            return true;
        }
    }

    return false;
}

static bool do_report_event_occured(char *pResult)
{
    if(g_comm_status == MCU_TO_WIFI_COMM_IDLE && report_event_to_iot(pResult))
    {
        g_comm_status = MCU_TO_WIFI_COMM_EVENT_OCCURED;
        return true;
    }

    return false;
}

static bool do_invoke_other_command(char *pResult)
{
    bool ret = false;

    if(g_comm_status == MCU_TO_WIFI_COMM_IDLE && g_cmd_status.command != WIFI_CMD_NONE && g_cmd_status.is_execute == false)
    {
        g_cmd_status.is_execute = true;

        ret = true;

        switch(g_cmd_status.command)
        {
            case WIFI_CMD_NET_STATE:
                app_func_get_net_state(pResult);
                break;
            case WIFI_CMD_TIME:
                app_func_get_time(pResult);
                break;
            case WIFI_CMD_MAC:
                app_func_get_mac(pResult);
                break;
            case WIFI_CMD_MODEL:
                app_func_get_model(pResult);
                break;
            case WIFI_CMD_VERSION:
                app_func_get_version(pResult);
                break;
            case WIFI_CMD_GETWIFI:
                app_func_getwifi(pResult);
                break;
            case WIFI_CMD_GETARCH:
                app_func_get_arch_platform(pResult);
                break;
            case WIFI_CMD_REBOOT:
                app_func_reboot(pResult);
                break;
            case WIFI_CMD_RESTORE:
                app_func_restore(pResult);
                break;
            case WIFI_CMD_SETWIFI:
                app_func_setwifi(pResult);
                break;
            case WIFI_CMD_SETMCU_VERSION:
                app_func_set_mcu_version(pResult);
                break;
            case WIFI_CMD_FACTORY:
                app_func_factory(pResult);
                break;
            case WIFI_CMD_SN:
                app_func_sn(pResult);
                break;
            case WIFI_CMD_GET_SN:
                app_func_get_sn(pResult);
                break;
            case WIFI_CMD_SET_MODEL:
                app_func_model(pResult);
                break;
            case WIFI_CMD_BLE_CONF:
                app_func_ble_conf(pResult);
                break;
            default:
                g_cmd_status.command = WIFI_CMD_NONE;
                ret = false;
                APP_LOG("wifi cmd is invalid, ignore it!");
                break;
        }
    }

    if(ret)
        g_comm_status = MCU_TO_WIFI_COMM_SEND_COMMAND;

    return ret;
}

static int do_get_properties(miio_cmd_arg_t *req_arg)
{
    do
    {
        int ret = 0;
        ret = execute_property_operation_async((const char *)req_arg->pload, req_arg->pload_len, false, req_arg->presult);
        if(ret != 0)
        {
            /* add error solution here */
            break;
        }
    } while(false);

    APP_LOG("do_get_properties:result len: %d\r", strlen(req_arg->presult));

    return MIIO_OK;
}

static int do_set_properties(miio_cmd_arg_t *req_arg)
{
    do
    {
        int ret = 0;
        ret = execute_property_operation_async((const char *)req_arg->pload, req_arg->pload_len, true, req_arg->presult);
        if(ret != 0)
        {
            /* add error solution here */
            break;
        }
    } while(false);

    return MIIO_OK;
}

static int do_invoke_action(miio_cmd_arg_t *req_arg)
{
    do
    {
        int ret = 0;
        ret = execute_action_invocation_async((const char *)req_arg->pload, req_arg->pload_len, req_arg->presult);
        if(ret != 0)
        {
            break;
        }
    } while(false);

    return MIIO_OK;
}

static int do_invoke_none(miio_cmd_arg_t *req_arg)
{
    if(req_arg == NULL)
        return MIIO_ERROR;
    do
    {
        if(wifi_poll_interval - last_mcu_to_wifi_cmd_interval > 6)
        {
            mcu_to_wifi_comm_status_e old_status = g_comm_status;
            if(!do_report_event_occured(req_arg->presult))
            {
                if(!do_report_properties_changed(req_arg->presult))
                {
                    do_invoke_other_command(req_arg->presult);
                }
            }

            if(old_status == MCU_TO_WIFI_COMM_IDLE &&
                g_comm_status != MCU_TO_WIFI_COMM_IDLE)
            {
                last_mcu_to_wifi_cmd_interval = wifi_poll_interval;
            }
        }
    } while(false);

    return MIIO_OK;
}

//#pragma section bss share_data
#define OTA_NONE 0x0
#define OTA_STARTED 0xdcba1357
typedef enum
{
    OTA_MODE_NORMAL = 1,
    OTA_MODE_AUTO,
    OTA_MODE_FORCE
} ota_mode_t;
typedef struct
{
    long ota_flag;
    ota_mode_t ota_mode;
    char interrupt_ownner; //0: boot 1:app
} boot_app_shared_data_t;
boot_app_shared_data_t boot_app_share_data;
//#pragma section

static const char down_update_fw_auto_str[] = "auto";
static const char down_update_fw_force_str[] = "force";

static int do_invoke_ota(miio_cmd_arg_t *req_arg)
{
    const char *temp;
    bool quite = false;
    int pos = 0;

    if(req_arg == NULL)
        return MIIO_ERROR;
    APP_LOG("========================== ota ==========================\r");

    //TODO:also need check reservation status
    if(!is_allowed_execute_ota())
    {
        str_n_cat(req_arg->presult, 2, "result ", "\"busy\"\r");

        return MIIO_ERROR;
    }

    boot_app_share_data.ota_mode = OTA_MODE_NORMAL;
    temp = req_arg->pload;
    pos = miio_strtok(temp, " ");

    if(pos != 0)
    {
        temp = temp + pos;
        if(!strncmp((const char *)temp, down_update_fw_auto_str, sizeof(down_update_fw_auto_str) - 1))
        {
            boot_app_share_data.ota_mode = OTA_MODE_AUTO;
            quite = true;
        }
        else if(!strncmp((const char *)temp, down_update_fw_force_str, sizeof(down_update_fw_force_str) - 1))
        {
            boot_app_share_data.ota_mode = OTA_MODE_FORCE;
            quite = true;
        }
    }

    boot_app_share_data.ota_flag = OTA_STARTED;
    SetOtaFlag(quite);
    arch_os_switch_to_boot();

    return MIIO_OK;
}

static int do_net_changed(miio_cmd_arg_t *req_arg)
{
    if(req_arg == NULL)
        return MIIO_ERROR;
    set_network_state(req_arg->pload);

    return MIIO_OK;
}

static int do_mcu_version(miio_cmd_arg_t *req_arg)
{
    if(req_arg == NULL)
        return MIIO_ERROR;

    sprintf(shake_cmd, "mcu_version %04d\r", GetMcuVersion());
    strcpy(req_arg->presult, shake_cmd);
    return MIIO_OK;
}

#define COMMAND_PROC_TABLE_SUPPORT 0 /* mark it for C51, xdata conflict  */

static const char down_none_str[] = "none";
static const char down_get_props_str[] = "get_properties";
static const char down_set_props_str[] = "set_properties";
static const char down_update_fw_str[] = "update_fw";
static const char down_action_str[] = "action";
static const char down_net_change_str[] = "MIIO_net_change";
static const char down_version_str[] = "MIIO_mcu_version_req";
static const char down_result_ok[] = "ok";
static const char down_result_error[] = "error";

static int miio_command_process(const char *cmd_str)
{
    const char *pSubCmd, *pParams;
    uint16_t pos;
    miio_cmd_arg_t arg;

    if(!strncmp(cmd_str, "down", strlen("down")))
    {
        pos = miio_strtok(cmd_str, SPACE_STRING);
        if(0 == pos)
            return MIIO_ERROR_PARAM;

        pSubCmd = cmd_str + pos; //Sub command

        pos = miio_strtok(pSubCmd, SPACE_STRING);
        if(0 == pos)
            pParams = NULL;
        else
            pParams = pSubCmd + pos; //Parameters

        arg.pload = (void *)pParams;
        if(pParams == NULL)
            arg.pload_len = 0;
        else
            arg.pload_len = strlen(pParams) + 1;
        arg.presult = miio_get_result_buf();

        if(!strncmp((const char *)pSubCmd, down_get_props_str, sizeof(down_get_props_str) - 1))
        {
            do_get_properties(&arg);
        }
        else if(!strncmp((const char *)pSubCmd, down_set_props_str, sizeof(down_set_props_str) - 1))
        {
            do_set_properties(&arg);
        }
        else if(!strncmp((const char *)pSubCmd, down_version_str, sizeof(down_version_str) - 1))
        {
            do_mcu_version(&arg);
        }
        else if(!strncmp((const char *)pSubCmd, down_none_str, sizeof(down_none_str) - 1))
        {
            do_invoke_none(&arg);
        }
        else if(!strncmp((const char *)pSubCmd, down_update_fw_str, sizeof(down_update_fw_str) - 1))
        {
            do_invoke_ota(&arg);
        }
        else if(!strncmp((const char *)pSubCmd, down_action_str, sizeof(down_action_str) - 1))
        {
            do_invoke_action(&arg);
        }
        else if(!strncmp((const char *)pSubCmd, down_net_change_str, sizeof(down_net_change_str) - 1))
        {
            do_net_changed(&arg);
        }
        else
        {
            miio_encode_error_response(ERROR_MESSAGE_UNCMD, ERROR_CODE_UNCMD, arg.presult);
        }
        miio_uart_send_str(arg.presult);
    }
    else
    {
        /* Todo, process command with no prefix down, such as net,mac,time, restore, etc. */
        int ret = -1;

        if(!strncmp((const char *)cmd_str, down_result_ok, sizeof(down_result_ok) - 1))
        {
            ret = 0;
        }
        else if(!strncmp((const char *)cmd_str, down_result_error, sizeof(down_result_error) - 1))
        {
            ret = 1;
        }

        switch(g_comm_status)
        {
            case MCU_TO_WIFI_COMM_IDLE:
                break;
            case MCU_TO_WIFI_COMM_PROP_CHANGED:
                if(ret != -1)
                {
                    APP_LOG("property changed report ret:%d!\r", ret);
                    g_comm_status = MCU_TO_WIFI_COMM_IDLE;
                }
                break;
            case MCU_TO_WIFI_COMM_EVENT_OCCURED:
                if(ret != -1)
                {
                    APP_LOG("event occured report ret:%d!\r", ret);
                    g_comm_status = MCU_TO_WIFI_COMM_IDLE;
                }
                break;
            case MCU_TO_WIFI_COMM_SEND_COMMAND:
                if(g_cmd_status.command != WIFI_CMD_NONE)
                {
                    if(g_cmd_status.command == WIFI_CMD_FACTORY ||
                        g_cmd_status.command == WIFI_CMD_REBOOT ||
                        g_cmd_status.command == WIFI_CMD_RESTORE ||
                        g_cmd_status.command == WIFI_CMD_SETWIFI ||
                        g_cmd_status.command == WIFI_CMD_SETMCU_VERSION ||
                        g_cmd_status.command == WIFI_CMD_SN ||
                        g_cmd_status.command == WIFI_CMD_SET_MODEL ||
                        g_cmd_status.command == WIFI_CMD_BLE_CONF)
                    {
                        if(ret != -1)
                        {
                            APP_LOG("send command ret:%d!\r", ret);

                            if(g_cmd_status.commandCallback != NULL)
                            {
                                g_cmd_status.commandCallback(cmd_str, ret == 0 ? true : false);
                            }

                            g_cmd_status.commandCallback = NULL;
                            g_cmd_status.command = WIFI_CMD_NONE;
                            g_cmd_status.is_execute = false;
                            g_comm_status = MCU_TO_WIFI_COMM_IDLE;
                        }
                    }
                    else
                    {
                        APP_LOG("send command received:%s!\r", cmd_str);

                        if(g_cmd_status.commandCallback != NULL)
                        {
                            g_cmd_status.commandCallback(cmd_str, true);
                        }

                        g_cmd_status.commandCallback = NULL;
                        g_cmd_status.command = WIFI_CMD_NONE;
                        g_cmd_status.is_execute = false;
                        g_comm_status = MCU_TO_WIFI_COMM_IDLE;
                    }
                }
                else
                {
                    g_comm_status = MCU_TO_WIFI_COMM_IDLE;
                }

                break;
        }
    }
    return MIIO_OK;
}

int miio_encode_error_response(char *message, int errcode, char *presult)
{
    sprintf(presult, "error \"%s\" %d\r", message, errcode);
    return MIIO_OK;
}

char *miio_get_shakehand_step_cmd(wifi_shakehand_step step)
{
    if(step == WIFI_SHAKEHAND_ECHO_OFF)
    {
        return "echo off\r";
    }
    else if(step == WIFI_SHAKEHAND_MODEL)
    {
        return "model " USER_MODEL "\r";
    }
    else if(step == WIFI_SHAKEHAND_MCU_VER)
    {
        sprintf(shake_cmd, "mcu_version %04d\r", GetMcuVersion());
        return shake_cmd;
    }
    else if(step == WIFI_SHAKEHAND_AUTOOTA)
    {
        return "set_mcu_auto_ota on\r";
    }
#if HAVE_BLE
    else if(step == WIFI_SHAKEHAND_BLE_CONF)
    {
        return "ble_config set " BLE_PID " " USER_MCU_VERSION "\r";
    }
#endif

    return NULL;
}

int miio_send_basic_info(void)
{
    int result = MIIO_OK;

    do
    {
        result = MIIO_OK;
        for(int step = WIFI_SHAKEHAND_ECHO_OFF; step < WIFI_SHAKEHAND_STEP_NUM; step++)
        {
            result |= ((UART_RECV_ACK_ERROR == miio_uart_send_str_wait_ack(miio_get_shakehand_step_cmd(step))) ? MIIO_ERROR : MIIO_OK);
        }
    } while(FALSE);

    if(result == MIIO_OK)
    {
        set_module_ready();
    }

    return result;
}

int miio_send_basic_info_async(void)
{
    unsigned char *pbuf = NULL;

    switch(g_wifi_sh_step)
    {
        case WIFI_SHAKEHAND_NONE:
            g_wifi_sh_step++;
            miio_uart_send_str(miio_get_shakehand_step_cmd(g_wifi_sh_step));
            break;
        case WIFI_SHAKEHAND_COMPLETE:
            set_module_ready();
            break;
        default:
            if(miio_uart_recv_str_aync(&pbuf) > 0)
            {
                if(0 == strncmp((const char *)pbuf, "ok", strlen("ok")))
                {
                    g_wifi_sh_step++;
                }
            }

            if(g_wifi_sh_step != WIFI_SHAKEHAND_COMPLETE)
            {
                miio_uart_send_str(miio_get_shakehand_step_cmd(g_wifi_sh_step));
            }
            break;
    }

    return MIIO_OK;
}

void Miio_Init(void)
{
    boot_app_share_data.ota_flag = OTA_NONE;
    miio_send_basic_info();
    initIotFridgeHouse();
}

#define MIIO_RECV_NORMAL_INTERVAL (150)
#define MIIO_RECV_FAST_INTERVAL (30)

static int recv_interval = MIIO_RECV_NORMAL_INTERVAL;
static int total_ms = 0;
static int dev_report_min = 0;

void Miio_Count_1ms(void)
{
    static uint16 count_ms = 0;
    static uint16 count_s = 0;
    total_ms++;
    count_ms++;
    if(count_ms >= 1000)
    {
        count_ms = 0;
        count_s++;
        if(count_s > 59)
        {
            count_s = 0;
            dev_report_min++;
        }
    }
}

int Miio_GetDeviceReportTimeMin(void)
{
    return dev_report_min;
}
int miio_command_rx_tx(void)
{
    unsigned char *pbuf = NULL;
    int ret = MIIO_OK;
    int recv_bytes;

    TBool responseRet = response_for_last_execute(miio_get_result_buf());
    if(responseRet)
    {
        return ret;
    }

    if(total_ms < recv_interval)
        return ret;
    total_ms = 0;

    wifi_poll_interval++;

    if(!is_module_ready())
    {
        miio_send_basic_info_async();
        return ret;
    }

    do
    {
        recv_bytes = miio_uart_recv_str_aync(&pbuf);
        if(recv_bytes <= 0)
        {
            if(g_comm_status != MCU_TO_WIFI_COMM_IDLE && wifi_poll_interval - last_mcu_to_wifi_cmd_interval > 20)
            {
                APP_LOG("mcu to wifi cmd timeout, reset status!\r");
                if(MCU_TO_WIFI_COMM_SEND_COMMAND == g_comm_status)
                {
                    if(g_cmd_status.commandCallback != NULL)
                    {
                        g_cmd_status.commandCallback(down_result_error, false);
                    }
                    g_cmd_status.command = WIFI_CMD_NONE;
                    g_cmd_status.commandCallback = NULL;
                    g_cmd_status.is_execute = FALSE;
                }
                g_comm_status = MCU_TO_WIFI_COMM_IDLE;
            }

            break;
        }
        if(pbuf[recv_bytes - 1] != '\r')
        { /* judge if string received end with '\r' */
            APP_LOG("uart recv error[%s]", pbuf);
            ret = MIIO_ERROR_PARAM;
            break;
        }

        pbuf[recv_bytes - 1] = '\0'; /* replace \r with \0 */

        miio_command_process((const char *)pbuf);

        if(miio_uart_has_more_data())
        {
            if(recv_interval == MIIO_RECV_NORMAL_INTERVAL)
            {
                recv_interval = MIIO_RECV_FAST_INTERVAL;
                APP_LOG("wifi recv interval switch to fast_mode\r");
            }

            return ret;
        }
        else
        {
            if(recv_interval == MIIO_RECV_FAST_INTERVAL)
            {
                recv_interval = MIIO_RECV_NORMAL_INTERVAL;
                APP_LOG("wifi recv interval switch to normal_mode\r");
            }
        }

    } while(recv_bytes > 0);

    if(is_wait_dev_async_execute())
    {
        return ret;
    }
    else if(g_comm_status == MCU_TO_WIFI_COMM_IDLE)
    {
        miio_uart_send_str("get_down\r");
    }

    do
    {
        static int net_update_count = 0;

        net_update_count++;

        if((!is_local_date_valid() && is_dev_connected_iot_svr()) ||
            (net_update_count % 400 == 0))
        {
            execute_wifi_cmd_async(WIFI_CMD_TIME, retrieve_sys_time_callback);
        }

        if((is_dev_net_not_init() && (net_update_count % 20 == 0)) ||
            (net_update_count % 500 == 0))
        {
            execute_wifi_cmd_async(WIFI_CMD_NET_STATE, set_net_state_callback);
        }

        if(!is_wifi_arch_platform() && (net_update_count % 201 == 0))
        {
            execute_wifi_cmd_async(WIFI_CMD_GETARCH, get_wifi_arch_platform_callback);
        }

    } while(0);

    return ret;
}

int miio_strtok(const char *buf, char *delim)
{
    int pos = 0;

    while(1)
    {
        // search failed
        if(buf[pos] == '\0')
        {
            pos = 0;
            break;
        }

        // search success
        if(buf[pos] == *delim)
        {
            pos++;
            //buf[pos++] = '\0';
            break;
        }

        // pointer to next char
        if(pos++ >= CMD_STR_MAX_LEN)
            break;
    }

    return pos;
}

int miio_strlen(const char *str)
{
    int len = 0;
    while((*str != '\0') && (*str != ' '))
    {
        len++;
        str++;
    }
    return len;
}
int32_t miio_atoi(const char *str)
{
    int32_t result = 0;
    int32_t num = 0, tap = 1;

    while(*str == ' ')
    {
        str++;
    }

    while(*str == '-')
    {
        tap = -tap;
        str++;
    }

    while(*str >= '0' && *str <= '9')
    {
        num = *str - '0';

        result = result * 10 + num * tap; //  + *str - '0'
        str++;
    }

    return result;
}

char *miio_get_result_buf(void)
{
    static char cmd_result_buf[CMD_RESULT_BUF_SIZE + 1];
    memset(cmd_result_buf, 0, sizeof(cmd_result_buf));
    return cmd_result_buf;
}

void get_factory_data(char *result, uint16_t len)
{
    char buf[ID_MAX_LEN + 1];
    snprintf(buf, ID_MAX_LEN, "%d", 254);
    str_n_cat(result, 2, buf, " ");
    snprintf(buf, ID_MAX_LEN, "%d", 1);
    if(Get_FridgeState() != eFridge_Factory)
    {
        str_n_cat(result, 2, buf, " -4001");
    }
    else
    {
        str_n_cat(result, 2, buf, " 0 ");
        Get_FctUploadData(result, len);
    }
}
