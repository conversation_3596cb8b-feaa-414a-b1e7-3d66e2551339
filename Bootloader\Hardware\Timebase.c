/*!
 * @file
 * @brief System time base.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stdbool.h>
#include "Timebase.h"
#include "Adpt_GPIO.h"
#include "DisplayUsart.h"
#include "InverterUsart.h"
#include "OtaManager.h"
#include "HeartbeatLed.h"

static uint16_t u16_Millisec;

void ISR_Timer_1ms(void)
{
    u16_Millisec++;
    Handle_UartDisplayOverTime();
    Handle_UartInverterOverTime();
    HandlerOtaScheduleInterval();
    Update_HeartbeatLed();
}

uint16_t Get_SystemMillisec(void)
{
    return u16_Millisec;
}
